@echo off
echo Rappelz Macro Recorder Launcher
echo ================================

:: Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo This script requires administrator privileges.
    echo Requesting administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo Running with administrator privileges.
echo.

:: Check if Python is installed
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    pause
    exit /b
)

echo Python found. Checking for required libraries...

:: Change to the script directory
cd /d "%~dp0"

:: Install required libraries
echo Installing required libraries...
pip install pyautogui keyboard mouse pywin32 numpy pillow opencv-python --quiet

:: Make sure critical modules are installed (sometimes fails silently)
pip install mouse --quiet
pip install numpy --quiet
pip install pywin32 --quiet
pip install opencv-python --quiet

echo.
echo Starting Rappelz Macro Recorder...
echo.

:: Run the main script (use main.py as primary entry point)
python main.py

:: If main.py fails, try the legacy entry point
if %errorlevel% neq 0 (
    echo.
    echo Main entry point failed, trying legacy entry point...
    python game_macro_recorder.py
)

echo.
echo Application closed.
pause
