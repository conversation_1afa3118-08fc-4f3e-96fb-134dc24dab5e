# PowerShell script to run <PERSON><PERSON>z Macro Recorder as Administrator
# This script ensures the application runs with the necessary privileges

param(
    [string]$EntryPoint = "main.py"
)

Write-Host "Rappelz Macro Recorder - Administrator Launcher" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to restart as administrator
function Start-AsAdministrator {
    param([string]$ScriptPath, [string]$Arguments = "")
    
    try {
        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = "powershell.exe"
        $startInfo.Arguments = "-ExecutionPolicy Bypass -File `"$ScriptPath`" $Arguments"
        $startInfo.Verb = "runas"
        $startInfo.UseShellExecute = $true
        
        [System.Diagnostics.Process]::Start($startInfo) | Out-Null
        return $true
    }
    catch {
        Write-Host "Failed to restart as administrator: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Check if running as administrator
if (-not (Test-Administrator)) {
    Write-Host "Administrator privileges required." -ForegroundColor Yellow
    Write-Host "Attempting to restart as administrator..." -ForegroundColor Yellow
    Write-Host ""
    
    $scriptPath = $MyInvocation.MyCommand.Path
    if (Start-AsAdministrator -ScriptPath $scriptPath -Arguments $EntryPoint) {
        Write-Host "Restarting with administrator privileges..." -ForegroundColor Green
        exit 0
    } else {
        Write-Host "Failed to obtain administrator privileges." -ForegroundColor Red
        Write-Host "Please run this script as administrator manually." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host "✓ Running with administrator privileges" -ForegroundColor Green
Write-Host ""

# Change to script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptDir

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Python found: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "✗ Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python from https://www.python.org/downloads/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if the entry point exists
if (-not (Test-Path $EntryPoint)) {
    Write-Host "✗ Entry point '$EntryPoint' not found" -ForegroundColor Red
    
    # Try alternative entry points
    $alternatives = @("main.py", "game_macro_recorder.py")
    $found = $false
    
    foreach ($alt in $alternatives) {
        if (Test-Path $alt) {
            Write-Host "✓ Found alternative entry point: $alt" -ForegroundColor Green
            $EntryPoint = $alt
            $found = $true
            break
        }
    }
    
    if (-not $found) {
        Write-Host "✗ No valid entry point found" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host "✓ Entry point: $EntryPoint" -ForegroundColor Green
Write-Host ""

# Install required libraries
Write-Host "Installing required libraries..." -ForegroundColor Yellow
$libraries = @(
    "pyautogui",
    "keyboard", 
    "mouse",
    "pywin32",
    "numpy",
    "pillow",
    "opencv-python"
)

foreach ($lib in $libraries) {
    Write-Host "  Installing $lib..." -NoNewline
    try {
        pip install $lib --quiet --disable-pip-version-check 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host " ✓" -ForegroundColor Green
        } else {
            Write-Host " ⚠" -ForegroundColor Yellow
        }
    } catch {
        Write-Host " ✗" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Starting Rappelz Macro Recorder..." -ForegroundColor Cyan
Write-Host ""

# Run the application
try {
    python $EntryPoint
    $exitCode = $LASTEXITCODE
    
    Write-Host ""
    if ($exitCode -eq 0) {
        Write-Host "Application closed successfully." -ForegroundColor Green
    } else {
        Write-Host "Application exited with code: $exitCode" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error running application: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"
