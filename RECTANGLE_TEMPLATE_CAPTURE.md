# Rectangle Template Capture System

## Overview

The template capture system now uses a precise rectangle selection method where users click on two corners to define the exact boundaries of the revival button template. This provides much more accurate control over the template area compared to single-point capture methods.

## New Rectangle Selection System

### 📐 **Two-Point Rectangle Definition**

#### **How It Works**
1. **First Click**: Define the TOP-LEFT corner of the revival button
2. **Second Click**: Define the BOTTOM-RIGHT corner of the revival button
3. **Automatic Capture**: Template is captured from the defined rectangle
4. **Visual Feedback**: Real-time rectangle preview during selection

#### **Visual Process**
```
Step 1: Click TOP-LEFT corner
┌─────────────────────────────────────┐
│ Game Window                         │
│                                     │
│     ●← Click here (top-left)        │
│     ┌─────────────┐                 │
│     │ Revival Btn │                 │
│     └─────────────┘                 │
│                                     │
└─────────────────────────────────────┘

Step 2: Click BOTTOM-RIGHT corner
┌─────────────────────────────────────┐
│ Game Window                         │
│                                     │
│     ●─────────────┐                 │
│     │ Revival Btn │                 │
│     └─────────────● ← Click here    │
│                                     │
│                                     │
└─────────────────────────────────────┘

Result: Perfect template capture
┌─────────────────────────────────────┐
│ Game Window                         │
│                                     │
│     ┌═════════════┐                 │
│     ║ Revival Btn ║ ← Captured area │
│     └═════════════┘                 │
│                                     │
│                                     │
└─────────────────────────────────────┘
```

### 🎯 **Precision Benefits**

#### **Exact Boundary Control**
- **Pixel-perfect selection**: Define exact template boundaries
- **No guesswork**: See exactly what area will be captured
- **Flexible sizing**: Capture just the button or include surrounding area
- **Consistent results**: Same template area every time

#### **Visual Feedback System**
- **First click marker**: Red circle shows top-left corner
- **Selection rectangle**: Green outline shows capture area
- **Status updates**: Clear instructions for each step
- **Preview confirmation**: See selection before capture

## User Interface

### **Capture Dialog Instructions**
```
┌─ Capture Revival Button Template ──────────────┐
│                                                │
│ Instructions:                                  │
│                                                │
│ 1. Click OK to start capture mode             │
│ 2. Click on the TOP-LEFT corner of the        │
│    revival button                              │
│ 3. Click on the BOTTOM-RIGHT corner of the    │
│    revival button                              │
│ 4. The template will be captured automatically │
│ 5. Press ESC to cancel                         │
│                                                │
│ Make sure the revival button is clearly        │
│ visible! Draw a rectangle around the button    │
│ by clicking two corners.                       │
│                                                │
│           [OK]        [Cancel]                 │
└────────────────────────────────────────────────┘
```

### **Capture Overlay Interface**
```
┌─ Fullscreen Capture Overlay ───────────────────┐
│ Click TOP-LEFT corner, then BOTTOM-RIGHT       │
│ corner to define template area                  │
│ Press ESC to cancel                             │
│                                                │
│ ┌─────────────────────────────────────────────┐ │
│ │                                             │ │
│ │  Game content visible through overlay       │ │
│ │                                             │ │
│ │     ●─────────────┐  ← Visual markers      │ │
│ │     │ Selection   │                        │ │
│ │     │ Rectangle   │                        │ │
│ │     └─────────────●                        │ │
│ │                                             │ │
│ └─────────────────────────────────────────────┘ │
│                                                │
│ Step 2: Click BOTTOM-RIGHT corner of revival   │
│ button                                          │
└────────────────────────────────────────────────┘
```

## Technical Implementation

### **Coordinate System**
- **Absolute coordinates**: Uses screen coordinates for precision
- **Rectangle validation**: Ensures proper top-left to bottom-right order
- **Size validation**: Minimum 10x10, maximum 500x500 pixels
- **Error handling**: Comprehensive validation and error messages

### **Capture Process**
```python
# Rectangle capture workflow
1. User clicks first corner (top-left)
   - Store coordinates (x1, y1)
   - Show red marker at click point
   - Update status: "Click BOTTOM-RIGHT corner"

2. User clicks second corner (bottom-right)
   - Store coordinates (x2, y2)
   - Calculate rectangle bounds
   - Show green selection rectangle
   - Update status: "Capturing template..."

3. Automatic template capture
   - Hide overlay temporarily
   - Capture screenshot of defined rectangle
   - Save as template image
   - Show success confirmation
```

### **Validation Rules**
- **Minimum size**: 10x10 pixels (prevents tiny templates)
- **Maximum size**: 500x500 pixels (prevents huge templates)
- **Coordinate order**: Automatically corrects if user clicks in wrong order
- **Boundary checks**: Ensures rectangle is within screen bounds

## User Workflow

### **Step-by-Step Process**

#### **1. Initiate Capture**
- Click "📸 Capture Template" button
- Read instructions in dialog
- Click OK to start capture mode

#### **2. Define Template Area**
- **First click**: Click on the TOP-LEFT corner of revival button
  - Look for red marker confirming the click
  - Status updates to "Step 2: Click BOTTOM-RIGHT corner"
  
- **Second click**: Click on the BOTTOM-RIGHT corner of revival button
  - Green rectangle appears showing selection
  - Status updates to "Capturing template..."

#### **3. Automatic Processing**
- Overlay disappears briefly during capture
- Template is saved automatically
- Success dialog shows capture details
- Template preview updates in main interface

### **Best Practices**

#### **Optimal Template Selection**
1. **Include button only**: Select just the revival button text/icon
2. **Avoid background**: Don't include unnecessary background elements
3. **Consistent lighting**: Capture under normal game conditions
4. **Clear visibility**: Ensure button is fully visible and unobstructed

#### **Size Guidelines**
- **Small buttons**: 30x20 to 60x40 pixels
- **Medium buttons**: 60x40 to 120x80 pixels  
- **Large buttons**: 120x80 to 200x150 pixels
- **Avoid oversized**: Keep under 300x200 for best performance

## Error Handling

### **Common Issues and Solutions**

#### **Template Too Small**
- **Error**: "Template area too small (minimum 10x10 pixels)"
- **Solution**: Click corners further apart to create larger rectangle
- **Prevention**: Ensure at least 10 pixels between corner clicks

#### **Template Too Large**
- **Error**: "Template area too large (maximum 500x500 pixels)"
- **Solution**: Click corners closer together for smaller rectangle
- **Prevention**: Focus on just the button, not surrounding area

#### **Invalid Click Order**
- **Automatic correction**: System automatically determines proper rectangle
- **No user action needed**: Works regardless of click order
- **Visual feedback**: Rectangle always shows correctly

#### **Capture Failure**
- **Error**: "Failed to capture template"
- **Solutions**:
  - Ensure game window is visible
  - Try again with better lighting
  - Check screen permissions
  - Restart application if needed

## Advantages Over Previous Methods

### **Precision Improvements**
- **Exact boundaries**: No guessing about template size
- **Visual confirmation**: See exactly what will be captured
- **Consistent results**: Same area captured every time
- **Flexible sizing**: Adapt to different button sizes

### **User Experience**
- **Intuitive process**: Natural rectangle selection
- **Clear feedback**: Visual markers and status updates
- **Error prevention**: Validation prevents common mistakes
- **Professional interface**: Clean, guided workflow

### **Technical Benefits**
- **Better templates**: More precise templates improve detection
- **Reduced false positives**: Exact boundaries reduce noise
- **Improved performance**: Optimal template sizes
- **Cross-resolution support**: Works on any screen resolution

## Integration with Death Detection

### **Template Quality**
- **Higher accuracy**: Precise templates improve detection rates
- **Reduced noise**: Clean boundaries eliminate background interference
- **Optimal sizing**: Right-sized templates for best performance
- **Consistent detection**: Reliable templates work across sessions

### **Configuration Compatibility**
- **Automatic integration**: Templates immediately available for detection
- **Settings preservation**: Template settings saved with configuration
- **Cross-session persistence**: Templates work after application restart
- **Backup compatibility**: Templates included in configuration backups

## Future Enhancements

### **Planned Features**
- **Multi-template support**: Capture multiple templates for different scenarios
- **Template library**: Save and organize multiple templates
- **Advanced editing**: Fine-tune template boundaries after capture
- **Batch capture**: Capture multiple templates in one session

### **Advanced Options**
- **Confidence tuning**: Automatic confidence adjustment based on template quality
- **Template analysis**: Quality metrics and optimization suggestions
- **Performance monitoring**: Track template detection success rates
- **Smart recommendations**: AI-powered template optimization

The rectangle template capture system provides professional-grade precision for creating high-quality revival button templates, ensuring reliable death detection across all gaming scenarios.
