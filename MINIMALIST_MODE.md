# Minimalist Mode Documentation

## Overview

The Minimalist Mode provides a compact, focused interface for essential macro recording controls. It's designed for users who want quick access to core functionality without the full GUI complexity, perfect for keeping the interface visible while gaming.

## Features

### 🎯 **Compact Interface**
- **Small footprint**: 280x400 pixel window
- **Always on top**: Stays visible over game windows
- **Adjustable transparency**: 30% to 100% opacity
- **Moveable**: Drag to any screen position
- **Persistent settings**: Remembers position and transparency

### 🎮 **Essential Controls**
- **Recording controls**: Start/stop recording with visual feedback
- **Playback controls**: Play/pause/stop macro execution
- **Death detection**: Toggle automatic revival on/off
- **Status display**: Real-time action count and system status
- **Quick settings**: Transparency adjustment

### ⌨️ **Hotkey Integration**
- **F8**: Toggle recording
- **F9**: Start/stop playback
- **F10**: Toggle between minimalist and full mode
- **F12**: Toggle death detection
- **All hotkeys work in both modes**

## Usage

### Activating Minimalist Mode

**Method 1: Hotkey (Recommended)**
```
Press F10 while the application is running
```

**Method 2: From Full Interface**
- Use any method to access the full interface
- Press F10 or use the minimalist mode toggle

### Interface Elements

#### Status Display
- **Title**: "Macro Recorder"
- **Status text**: Current operation status
- **Action count**: Number of recorded actions
- **Real-time updates**: Refreshes every second

#### Control Buttons
- **🔴 Record (F8)**: Start/stop recording
  - Red when ready to record
  - Orange when recording active
- **▶️ Play (F9)**: Start/stop playback
  - Green when ready to play
  - Orange when playing (shows "Stop")
- **💀 Death Detection**: Toggle automatic revival
  - Blue when off
  - Green when active

#### Settings
- **Transparency slider**: Adjust window opacity (30%-100%)
- **📋 Full Mode (F10)**: Switch back to full interface

### Window Management

#### Positioning
- **Drag to move**: Click and drag anywhere on the window
- **Auto-save position**: Position is remembered between sessions
- **Screen boundaries**: Window stays within screen limits

#### Transparency
- **Real-time adjustment**: Use slider for immediate changes
- **Persistent setting**: Transparency level is saved
- **Gaming optimization**: Lower opacity for less distraction

#### Always on Top
- **Game compatibility**: Stays visible over most games
- **Focus management**: Doesn't steal focus from games
- **Quick access**: Always available when needed

## Configuration

### Default Settings
```json
{
    "x": 10,
    "y": 10,
    "transparency": 0.9
}
```

### Settings File
- **Location**: `minimalist_settings.json`
- **Auto-created**: Generated on first use
- **Auto-updated**: Saved when position or transparency changes

### Customization Options

#### Transparency Levels
- **100% (1.0)**: Completely opaque
- **90% (0.9)**: Default - slightly transparent
- **70% (0.7)**: Good for gaming overlay
- **50% (0.5)**: Minimal distraction
- **30% (0.3)**: Maximum transparency

#### Position Presets
- **Top-left**: `x: 10, y: 10` (default)
- **Top-right**: `x: screen_width - 290, y: 10`
- **Bottom-left**: `x: 10, y: screen_height - 410`
- **Bottom-right**: `x: screen_width - 290, y: screen_height - 410`

## Integration with Full Mode

### Seamless Switching
- **F10 hotkey**: Instant toggle between modes
- **State preservation**: All settings and recordings maintained
- **Window management**: Proper show/hide of windows
- **Focus handling**: Correct window activation

### Shared Functionality
- **Same recordings**: Actions recorded in either mode
- **Same playback**: Identical playback behavior
- **Same settings**: Death detection and game settings shared
- **Same hotkeys**: All keyboard shortcuts work in both modes

### Data Synchronization
- **Real-time updates**: Changes in one mode reflect in the other
- **Persistent storage**: Settings saved regardless of mode
- **State consistency**: Recording/playback state synchronized

## Gaming Integration

### Overlay Behavior
- **Non-intrusive**: Designed to not interfere with gameplay
- **Quick access**: Essential controls always available
- **Visual feedback**: Clear status indicators
- **Minimal resource usage**: Lightweight operation

### Game Compatibility
- **Most games**: Works with windowed and borderless windowed modes
- **Full-screen**: May not be visible in exclusive full-screen
- **Focus management**: Doesn't steal focus from games
- **Input passthrough**: Game receives all input normally

### Performance Impact
- **CPU**: Minimal impact (< 1% on modern systems)
- **Memory**: Small footprint (< 10MB additional)
- **GPU**: No graphics acceleration needed
- **Network**: No network usage

## Troubleshooting

### Common Issues

#### Window Not Visible
**Problem**: Minimalist window doesn't appear
**Solutions**:
1. Check if window is off-screen (reset position)
2. Try F10 to toggle mode
3. Restart application

#### Transparency Issues
**Problem**: Window too transparent or opaque
**Solutions**:
1. Use transparency slider to adjust
2. Reset to default (90%)
3. Check graphics driver settings

#### Hotkeys Not Working
**Problem**: F10 doesn't toggle modes
**Solutions**:
1. Ensure application has focus
2. Check for conflicting hotkey software
3. Try clicking the Full Mode button

#### Position Problems
**Problem**: Window appears in wrong location
**Solutions**:
1. Drag window to desired position
2. Delete `minimalist_settings.json` to reset
3. Manually edit settings file

### Reset to Defaults
```bash
# Delete settings file to reset
del minimalist_settings.json
```

### Debug Information
- **Log file**: Check application logs for errors
- **Settings file**: Verify `minimalist_settings.json` exists
- **Module test**: Run `python test_modules.py`

## Advanced Usage

### Multiple Monitors
- **Cross-monitor**: Can be positioned on any monitor
- **DPI awareness**: Scales correctly with different DPI settings
- **Resolution independence**: Works with any screen resolution

### Automation Scripts
```python
# Example: Programmatically control minimalist mode
from minimalist_mode import MinimalistWindow
from main import MacroRecorderGUI

# Create GUI instance
gui = MacroRecorderGUI(root)

# Access minimalist window
minimalist = gui.minimalist_window

# Show/hide programmatically
minimalist.show_window()
minimalist.hide_window()

# Check visibility
if minimalist.is_window_visible():
    print("Minimalist mode is active")
```

### Custom Themes
The minimalist window uses a dark theme optimized for gaming:
- **Background**: Dark blue-gray (#2c3e50)
- **Text**: Light gray (#ecf0f1)
- **Buttons**: Color-coded for function
- **Accents**: Blue highlights (#3498db)

## Best Practices

### For Gaming
1. **Position**: Place in a corner that doesn't obstruct important UI
2. **Transparency**: Use 70-80% for good visibility without distraction
3. **Hotkeys**: Learn F8/F9/F10/F11 for quick access
4. **Testing**: Test visibility in your specific game

### For Recording
1. **Status monitoring**: Keep an eye on action count
2. **Visual feedback**: Watch button color changes
3. **Quick stops**: Use F11 for emergency stops
4. **Mode switching**: Use F10 to access full features when needed

### For Efficiency
1. **Persistent setup**: Configure once, use everywhere
2. **Hotkey mastery**: Minimize mouse usage during gaming
3. **Quick toggles**: Use minimalist mode for active gaming
4. **Full mode**: Switch to full mode for detailed configuration

## Future Enhancements

### Planned Features
- **Custom button layouts**: Rearrangeable controls
- **Theme selection**: Multiple color schemes
- **Size options**: Different window sizes
- **Quick macros**: Favorite macro shortcuts
- **Status notifications**: Toast-style alerts

### Community Requests
- **Macro thumbnails**: Visual macro previews
- **Quick edit**: Basic editing in minimalist mode
- **Statistics**: Recording/playback statistics
- **Profiles**: Different configurations for different games

The minimalist mode provides a perfect balance between functionality and simplicity, making macro recording accessible and convenient during active gaming sessions.
