# Game Macro Recorder - Refactored Modular Structure

## Overview

The Game Macro Recorder has been successfully refactored from a single monolithic file into a modular, maintainable structure. This refactoring improves code organization, reduces complexity, and makes the codebase easier to understand and maintain.

## New File Structure

### Core Modules

1. **main.py** - Main entry point and GUI implementation
   - Contains the `MacroRecorderGUI` class
   - Handles all UI interactions and event management
   - Manages global state coordination between modules

2. **admin_check.py** - Administrator privilege checking
   - `is_admin()` - Checks if running with admin privileges
   - `check_admin_warning()` - Shows warning if not running as admin

3. **imports.py** - Centralized import management
   - Handles all external library imports
   - Manages import error handling and fallbacks
   - Defines availability flags (HAVE_PYAUTOGUI, HAVE_KEYBOARD, etc.)

4. **win_api.py** - Windows API functionality
   - Virtual key code mappings
   - Direct Windows API input functions
   - Game window detection and management

5. **recorder.py** - Recording and playback functionality
   - Action recording (keyboard and mouse)
   - Action playback with timing
   - Death detection and revival automation
   - Global state management for recording operations

6. **ui_helpers.py** - UI utility functions
   - Button creation helpers
   - Dialog and message box functions
   - Visual feedback and overlay management
   - Action formatting and display utilities

7. **config.py** - Configuration management
   - Settings loading and saving
   - Default configuration values
   - File I/O operations for macros and settings

### Legacy Files

- **game_macro_recorder.py** - Now serves as a redirect to main.py
  - Displays deprecation warning
  - Automatically launches main.py for backward compatibility

## Key Improvements

### 1. Separation of Concerns
- **UI Logic**: Isolated in main.py and ui_helpers.py
- **Business Logic**: Separated into recorder.py and config.py
- **System Integration**: Contained in win_api.py and admin_check.py
- **Dependencies**: Managed in imports.py

### 2. Global State Management
The refactored code uses a clean global state management system:

```python
# In recorder.py
def set_global_state(recorded_actions, last_action_time, running, death_detection_active):
    """Set global state variables from main module."""

# In main.py
def update_recorder_state():
    """Update the recorder module with current global state."""
    set_global_state(recorded_actions, last_action_time, running, death_detection_active)
```

### 3. Improved Error Handling
- Centralized import error handling
- Better exception management in each module
- Graceful fallbacks for missing dependencies

### 4. Enhanced Maintainability
- Each module has a single responsibility
- Clear interfaces between modules
- Reduced code duplication
- Better documentation and comments

## Usage

### Administrator Privileges Required

⚠️ **Important**: This application requires administrator privileges to function properly with games.

The application will automatically:
- Check if running with administrator privileges
- Prompt to restart as administrator if needed
- Handle the elevation process seamlessly

### Running the Application

**Method 1 - Automatic Elevation (Recommended):**
```bash
python main.py
```
*The application will automatically prompt for administrator privileges if needed.*

**Method 2 - Batch File Launcher:**
```bash
run_macro_recorder.bat
```
*Automatically handles administrator elevation and dependency installation.*

**Method 3 - PowerShell Script:**
```powershell
powershell -ExecutionPolicy Bypass -File run_as_admin.ps1
```
*Advanced launcher with detailed status reporting.*

**Method 4 - Legacy Entry Point:**
```bash
python game_macro_recorder.py
```
*Shows deprecation warning and redirects to main.py with admin check.*

### Module Dependencies

The modules have the following dependency structure:

```
main.py
├── admin_check.py
├── imports.py
├── win_api.py
├── recorder.py
├── ui_helpers.py
└── config.py
```

All modules can be imported independently, making testing and development easier.

## Hotkeys

The application supports the following global hotkeys that work in all modes:

- **F8**: Toggle recording (start/stop recording actions)
- **F9**: Toggle playback (start/stop macro execution)
- **F10**: Toggle minimalist mode (switch between full and compact interface)
- **F12**: Toggle death detection (enable/disable automatic revival)

All hotkeys work regardless of which window has focus, making them perfect for gaming scenarios.

## Features Preserved

All original functionality has been preserved:

- ✅ Keyboard and mouse action recording
- ✅ Action playback with customizable timing
- ✅ Death detection and automatic revival
- ✅ Game window targeting
- ✅ Hotkey support (F8, F9, F10, F12)
- ✅ Macro saving and loading
- ✅ Settings persistence
- ✅ Administrator privilege checking
- ✅ Multiple input method fallbacks

## Development Benefits

### For Developers
1. **Easier Testing**: Each module can be tested independently
2. **Cleaner Code**: Smaller, focused files are easier to understand
3. **Better Collaboration**: Multiple developers can work on different modules
4. **Reduced Merge Conflicts**: Changes are isolated to specific modules

### For Users
1. **Same Functionality**: All features work exactly as before
2. **Better Performance**: Optimized module loading
3. **Improved Stability**: Better error handling and recovery
4. **Backward Compatibility**: Old scripts still work

## Technical Details

### State Management
The refactored code uses a sophisticated state management system that avoids circular imports while maintaining clean separation between modules.

### Import Strategy
- All external dependencies are managed in `imports.py`
- Availability flags prevent runtime errors
- Graceful degradation when optional libraries are missing

### Error Handling
- Each module handles its own errors appropriately
- User-friendly error messages
- Logging for debugging purposes

## Migration Notes

If you have existing scripts or shortcuts that reference `game_macro_recorder.py`, they will continue to work but will show a deprecation warning. For best performance and to avoid the warning, update your scripts to use `main.py` instead.

## Future Enhancements

The modular structure makes it easy to add new features:

- New input methods can be added to `win_api.py`
- Additional UI components can be added to `ui_helpers.py`
- New configuration options can be added to `config.py`
- Enhanced recording features can be added to `recorder.py`

## Conclusion

This refactoring maintains 100% backward compatibility while significantly improving code organization and maintainability. The modular structure will make future development and maintenance much easier while preserving all existing functionality.
