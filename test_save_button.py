#!/usr/bin/env python3
"""
Test script to verify the save button in template edit dialog.
"""

import sys
import time

def test_save_button_functionality():
    """Test that the save button exists and works in the template edit dialog."""
    print("=" * 60)
    print("Save Button Test")
    print("=" * 60)
    
    try:
        # Test imports
        print("1. Testing imports...")
        from custom_button_manager import (
            CustomTemplate, add_custom_template, get_custom_template,
            update_custom_template, get_all_custom_templates
        )
        from main import MacroRecorderGUI
        import tkinter as tk
        print("   ✅ All imports successful")
        
        # Create a test template
        print("\n2. Creating test template...")
        test_template = CustomTemplate(
            name="save_button_test",
            image_path="test_save.png",
            confidence=0.85,
            auto_click=True,
            enabled=True,
            cooldown=2.0
        )
        
        # Add the template
        add_custom_template(test_template)
        print("   ✅ Test template created and added")
        
        # Verify template exists
        templates = get_all_custom_templates()
        if "save_button_test" in templates:
            print("   ✅ Template found in templates list")
        else:
            print("   ❌ Template not found in templates list")
            return False
        
        # Test update functionality (simulating save button action)
        print("\n3. Testing template update functionality...")
        original_confidence = test_template.confidence
        new_confidence = 0.75
        
        success = update_custom_template(
            "save_button_test",
            confidence=new_confidence,
            enabled=False,
            auto_click=False
        )
        
        if success:
            print("   ✅ Template update successful")
            
            # Verify the changes were applied
            updated_template = get_custom_template("save_button_test")
            if updated_template:
                if (updated_template.confidence == new_confidence and 
                    updated_template.enabled == False and 
                    updated_template.auto_click == False):
                    print("   ✅ Template changes verified")
                else:
                    print(f"   ❌ Template changes not applied correctly")
                    print(f"      Expected: confidence={new_confidence}, enabled=False, auto_click=False")
                    print(f"      Got: confidence={updated_template.confidence}, enabled={updated_template.enabled}, auto_click={updated_template.auto_click}")
                    return False
            else:
                print("   ❌ Updated template not found")
                return False
        else:
            print("   ❌ Template update failed")
            return False
        
        # Test GUI dialog creation (without actually showing it)
        print("\n4. Testing GUI dialog creation...")
        try:
            root = tk.Tk()
            root.withdraw()  # Hide the window
            
            # Create GUI instance
            gui = MacroRecorderGUI(root)
            
            # Check if the edit dialog method exists
            if hasattr(gui, '_show_template_edit_dialog'):
                print("   ✅ Edit dialog method exists")
                
                # Check if the method can be called (we won't actually show the dialog)
                # This tests that the method doesn't crash on creation
                try:
                    # We can't easily test the actual dialog without showing it,
                    # but we can verify the method exists and the template exists
                    template_exists = get_custom_template("save_button_test") is not None
                    if template_exists:
                        print("   ✅ Template exists for dialog creation")
                    else:
                        print("   ❌ Template doesn't exist for dialog creation")
                        return False
                        
                except Exception as e:
                    print(f"   ❌ Error testing dialog creation: {e}")
                    return False
            else:
                print("   ❌ Edit dialog method not found")
                return False
                
            root.destroy()
            
        except Exception as e:
            print(f"   ❌ Error creating GUI: {e}")
            return False
        
        # Clean up
        print("\n5. Cleaning up...")
        from custom_button_manager import remove_custom_template
        remove_success = remove_custom_template("save_button_test")
        print(f"   ✅ Test template removed: {remove_success}")
        
        print("\n" + "=" * 60)
        print("🎉 Save Button functionality test passed!")
        print("=" * 60)
        print("\nNote: This test verifies that:")
        print("- Template update functionality works (save button backend)")
        print("- Edit dialog method exists in GUI")
        print("- Template persistence works")
        print("\nTo test the actual save button visually:")
        print("1. Run the main application")
        print("2. Go to Custom Buttons tab")
        print("3. Create or select a template")
        print("4. Double-click to edit")
        print("5. Look for the '💾 Save' button in the dialog")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_save_button_functionality()
    sys.exit(0 if success else 1)
