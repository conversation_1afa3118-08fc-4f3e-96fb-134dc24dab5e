#!/usr/bin/env python3
"""
Visual test to show the template edit dialog with save button.
"""

import tkinter as tk
import tkinter.ttk as ttk
import sys
import time

def visual_test_save_button():
    """Visual test to show the edit dialog with save button."""
    print("Starting visual test for save button...")
    
    try:
        # Import required modules
        from custom_button_manager import CustomTemplate, add_custom_template
        from ui_helpers import create_styled_button, show_info_message
        
        # Create a test template
        test_template = CustomTemplate(
            name="visual_test_template",
            image_path="test_visual.png",
            confidence=0.85,
            auto_click=True,
            enabled=True,
            cooldown=2.0
        )
        
        # Add the template
        add_custom_template(test_template)
        
        # Create a simple GUI to test the dialog
        root = tk.Tk()
        root.title("Save Button Visual Test")
        root.geometry("500x300")
        
        # Color scheme (same as main app)
        colors = {
            'primary': '#2c3e50',
            'secondary': '#34495e',
            'accent': '#3498db',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'light': '#ecf0f1',
            'dark': '#2c3e50',
            'info': '#3498db'
        }
        
        def show_edit_dialog():
            """Show the template edit dialog."""
            try:
                from custom_button_manager import get_custom_template, update_custom_template
                
                template = get_custom_template("visual_test_template")
                if not template:
                    show_info_message("Error", "Template not found")
                    return
                
                # Create edit dialog (simplified version of the main one)
                dialog = tk.Toplevel(root)
                dialog.title("Edit Template: visual_test_template")
                dialog.geometry("500x500")  # Larger to accommodate bigger buttons
                dialog.transient(root)
                dialog.grab_set()
                dialog.resizable(False, False)
                
                # Center dialog
                dialog.geometry("+%d+%d" % (
                    root.winfo_rootx() + 50,
                    root.winfo_rooty() + 50
                ))
                
                # Template name (read-only)
                ttk.Label(dialog, text="Template Name:", font=('Arial', 10, 'bold')).pack(pady=5)
                ttk.Label(dialog, text="visual_test_template", font=('Arial', 10)).pack(pady=5)
                
                # Enabled checkbox
                enabled_var = tk.BooleanVar(value=template.enabled)
                ttk.Checkbutton(dialog, text="Enabled", variable=enabled_var).pack(pady=5)
                
                # Auto-click checkbox
                auto_click_var = tk.BooleanVar(value=template.auto_click)
                ttk.Checkbutton(dialog, text="Auto-click when detected", variable=auto_click_var).pack(pady=5)
                
                # Confidence threshold
                ttk.Label(dialog, text="Confidence Threshold:").pack(pady=5)
                confidence_var = tk.DoubleVar(value=template.confidence)
                confidence_frame = ttk.Frame(dialog)
                confidence_frame.pack(pady=5)
                ttk.Spinbox(confidence_frame, from_=0.1, to=1.0, increment=0.05,
                           textvariable=confidence_var, width=10).pack(side=tk.LEFT)
                ttk.Label(confidence_frame, text="(0.1 - 1.0)").pack(side=tk.LEFT, padx=5)
                
                # Cooldown
                ttk.Label(dialog, text="Cooldown (seconds):").pack(pady=5)
                cooldown_var = tk.DoubleVar(value=template.cooldown)
                cooldown_frame = ttk.Frame(dialog)
                cooldown_frame.pack(pady=5)
                ttk.Spinbox(cooldown_frame, from_=0.1, to=60.0, increment=0.5,
                           textvariable=cooldown_var, width=10).pack(side=tk.LEFT)
                ttk.Label(cooldown_frame, text="seconds").pack(side=tk.LEFT, padx=5)
                
                # Statistics
                stats_frame = ttk.LabelFrame(dialog, text="Statistics", padding=5)
                stats_frame.pack(pady=10, padx=10, fill="x")
                
                stats_text = (
                    f"Detections: {template.detection_count}\n"
                    f"Last Detection: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(template.last_detection_time)) if template.last_detection_time > 0 else 'Never'}\n"
                    f"Created: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(template.created_time))}"
                )
                ttk.Label(stats_frame, text=stats_text, font=('Consolas', 8)).pack()
                
                # Buttons frame
                button_frame = ttk.Frame(dialog)
                button_frame.pack(pady=20, padx=20, fill="x")
                
                def save_changes():
                    try:
                        success = update_custom_template(
                            "visual_test_template",
                            enabled=enabled_var.get(),
                            auto_click=auto_click_var.get(),
                            confidence=confidence_var.get(),
                            cooldown=cooldown_var.get()
                        )
                        
                        if success:
                            dialog.destroy()
                            show_info_message("Success", "Template updated successfully!")
                        else:
                            show_info_message("Error", "Failed to update template")
                            
                    except Exception as e:
                        show_info_message("Error", f"Failed to save changes: {e}")
                
                def test_template():
                    show_info_message("Test", "Test functionality would run here")
                
                # Create the buttons - THIS IS THE KEY PART TO TEST
                save_btn = create_styled_button(button_frame, "💾 Save", save_changes, colors['success'],
                                              font=('Arial', 12, 'bold'), width=15)
                save_btn.pack(side=tk.LEFT, padx=10, pady=10)

                test_btn = create_styled_button(button_frame, "🔍 Test", test_template, colors['warning'],
                                              font=('Arial', 12, 'bold'), width=15)
                test_btn.pack(side=tk.LEFT, padx=10, pady=10)

                cancel_btn = create_styled_button(button_frame, "❌ Cancel", dialog.destroy, colors['danger'],
                                                font=('Arial', 12, 'bold'), width=15)
                cancel_btn.pack(side=tk.LEFT, padx=10, pady=10)

                # Make save button prominent
                save_btn.focus_set()
                save_btn.configure(relief=tk.RAISED, borderwidth=3, height=2)
                
                # Bind keyboard shortcuts
                dialog.bind('<Return>', lambda e: save_changes())
                dialog.bind('<Escape>', lambda e: dialog.destroy())
                
                print("✅ Edit dialog created with save button")
                
            except Exception as e:
                print(f"❌ Error creating edit dialog: {e}")
                import traceback
                print(traceback.format_exc())
        
        # Main window content
        title_label = tk.Label(root, text="Save Button Visual Test", font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        info_label = tk.Label(root, text="Click the button below to open the template edit dialog.\nLook for the '💾 Save' button in the dialog.", 
                             font=('Arial', 12), justify='center')
        info_label.pack(pady=20)
        
        # Button to open edit dialog
        open_dialog_btn = create_styled_button(root, "Open Edit Dialog", show_edit_dialog, colors['accent'], width=20)
        open_dialog_btn.pack(pady=20)
        
        # Instructions
        instructions = tk.Label(root, text="Instructions:\n1. Click 'Open Edit Dialog'\n2. Look for the '💾 Save' button\n3. Test the save functionality\n4. Close this window when done", 
                               font=('Arial', 10), justify='left')
        instructions.pack(pady=20)
        
        print("✅ Visual test window created")
        print("Click 'Open Edit Dialog' to see the save button")
        
        # Start the GUI
        root.mainloop()
        
        # Clean up
        from custom_button_manager import remove_custom_template
        remove_custom_template("visual_test_template")
        print("✅ Test template cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Visual test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = visual_test_save_button()
    sys.exit(0 if success else 1)
