"""
Simple test script to identify GUI issues
"""

import sys
import traceback

def test_imports():
    """Test all imports to identify issues."""
    print("Testing imports...")
    
    try:
        print("  - Testing admin_check...")
        from admin_check import is_admin
        print("    ✅ admin_check OK")
    except Exception as e:
        print(f"    ❌ admin_check failed: {e}")
        return False
    
    try:
        print("  - Testing imports...")
        import imports
        print("    ✅ imports OK")
    except Exception as e:
        print(f"    ❌ imports failed: {e}")
        return False
    
    try:
        print("  - Testing win_api...")
        import win_api
        print("    ✅ win_api OK")
    except Exception as e:
        print(f"    ❌ win_api failed: {e}")
        return False

    try:
        print("  - Testing recorder...")
        import recorder
        print("    ✅ recorder OK")
    except Exception as e:
        print(f"    ❌ recorder failed: {e}")
        return False

    try:
        print("  - Testing ui_helpers...")
        import ui_helpers
        print("    ✅ ui_helpers OK")
    except Exception as e:
        print(f"    ❌ ui_helpers failed: {e}")
        return False

    try:
        print("  - Testing config...")
        import config
        print("    ✅ config OK")
    except Exception as e:
        print(f"    ❌ config failed: {e}")
        return False

    try:
        print("  - Testing death_detection...")
        import death_detection
        print("    ✅ death_detection OK")
    except Exception as e:
        print(f"    ❌ death_detection failed: {e}")
        return False
    
    try:
        print("  - Testing minimalist_mode...")
        from minimalist_mode import MinimalistWindow
        print("    ✅ minimalist_mode OK")
    except Exception as e:
        print(f"    ❌ minimalist_mode failed: {e}")
        return False
    
    return True

def test_basic_gui():
    """Test basic GUI creation."""
    print("\nTesting basic GUI...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        print("  - Creating root window...")
        root = tk.Tk()
        root.title("Test Window")
        root.geometry("400x300")
        
        print("  - Adding basic widgets...")
        label = ttk.Label(root, text="Test Label")
        label.pack(pady=10)
        
        button = ttk.Button(root, text="Test Button")
        button.pack(pady=10)
        
        print("  - Testing window display...")
        root.update()
        
        print("    ✅ Basic GUI OK")
        root.destroy()
        return True
        
    except Exception as e:
        print(f"    ❌ Basic GUI failed: {e}")
        traceback.print_exc()
        return False

def test_main_gui():
    """Test main GUI creation."""
    print("\nTesting main GUI creation...")
    
    try:
        # Import everything needed
        from admin_check import is_admin
        import imports
        import win_api
        import recorder
        import ui_helpers
        import config
        import death_detection
        from minimalist_mode import MinimalistWindow
        
        print("  - Creating main window...")
        import tkinter as tk
        root = tk.Tk()
        root.title("Rappelz Macro Recorder - Test")
        root.geometry("600x700")
        
        print("  - Testing MacroRecorderGUI creation...")
        
        # Import the main GUI class
        import main
        app = main.MacroRecorderGUI(root)
        
        print("  - Testing window update...")
        root.update()
        
        print("    ✅ Main GUI creation OK")
        root.destroy()
        return True
        
    except Exception as e:
        print(f"    ❌ Main GUI creation failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("GUI Test Suite")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed - stopping")
        return False
    
    # Test basic GUI
    if not test_basic_gui():
        print("\n❌ Basic GUI tests failed - stopping")
        return False
    
    # Test main GUI
    if not test_main_gui():
        print("\n❌ Main GUI tests failed")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All tests passed!")
    print("=" * 50)
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except Exception as e:
        print(f"\nCritical error in test suite: {e}")
        traceback.print_exc()
        sys.exit(1)
