"""
Tooltip class for tkinter widgets
Provides a tooltip when hovering over a widget
"""
import tkinter as tk

class ToolTip:
    """
    Creates a tooltip for a given widget
    """
    def __init__(self, widget, text, delay=500, wrap_length=200, background='#2c3e50', foreground='white'):
        self.widget = widget
        self.text = text
        self.delay = delay  # Delay in milliseconds
        self.wrap_length = wrap_length
        self.background = background
        self.foreground = foreground
        self.tooltip_window = None
        self.id = None
        
        # Bind events
        self.widget.bind("<Enter>", self.schedule)
        self.widget.bind("<Leave>", self.hide)
        self.widget.bind("<ButtonPress>", self.hide)
    
    def schedule(self, event=None):
        """Schedule the tooltip to appear after delay"""
        self.hide()
        self.id = self.widget.after(self.delay, self.show)
    
    def show(self):
        """Show the tooltip"""
        x, y, _, _ = self.widget.bbox("insert")
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() + 25
        
        # Creates a toplevel window
        self.tooltip_window = tk.Toplevel(self.widget)
        
        # Make it stay on top and remove decorations
        self.tooltip_window.wm_overrideredirect(True)
        self.tooltip_window.wm_geometry(f"+{x}+{y}")
        
        # Create a frame with border
        frame = tk.Frame(self.tooltip_window, 
                         background=self.background, 
                         borderwidth=1, 
                         relief="solid")
        frame.pack(ipadx=5, ipady=5)
        
        # Create the label with the tooltip text
        label = tk.Label(frame, 
                         text=self.text, 
                         justify=tk.LEFT,
                         background=self.background,
                         foreground=self.foreground,
                         wraplength=self.wrap_length,
                         font=('Arial', 9))
        label.pack()
    
    def hide(self, event=None):
        """Hide the tooltip"""
        if self.id:
            self.widget.after_cancel(self.id)
            self.id = None
        
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None

def add_tooltip(widget, text, delay=500, wrap_length=200, background='#2c3e50', foreground='white'):
    """
    Helper function to add a tooltip to a widget
    """
    ToolTip(widget, text, delay, wrap_length, background, foreground)
