#!/usr/bin/env python3
"""
Test script to verify the template edit dialog has a save button.
"""

import tkinter as tk
import tkinter.ttk as ttk
import sys
import os

def test_edit_dialog():
    """Test the template edit dialog."""
    print("Testing Template Edit Dialog...")
    
    try:
        # Import required modules
        from main import MacroRecorderGUI
        from custom_button_manager import CustomTemplate, add_custom_template
        
        # Create a test template
        test_template = CustomTemplate(
            name="test_dialog_template",
            image_path="test.png",
            confidence=0.85,
            auto_click=True,
            enabled=True,
            cooldown=2.0
        )
        
        # Add the template
        add_custom_template(test_template)
        
        # Create a minimal GUI to test the dialog
        root = tk.Tk()
        root.title("Template Edit Dialog Test")
        root.geometry("300x200")
        
        # Create a minimal GUI instance
        gui = MacroRecorderGUI(root)
        
        # Create a button to open the edit dialog
        def open_edit_dialog():
            gui._show_template_edit_dialog("test_dialog_template")
        
        test_button = tk.Button(root, text="Open Edit Dialog", command=open_edit_dialog)
        test_button.pack(pady=50)
        
        info_label = tk.Label(root, text="Click the button to open the edit dialog.\nCheck if the Save button is visible.")
        info_label.pack(pady=20)
        
        print("✅ Test dialog created successfully")
        print("Click 'Open Edit Dialog' to test the save button")
        
        # Start the GUI
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_edit_dialog()
    sys.exit(0 if success else 1)
