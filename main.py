"""
Main Entry Point - Responsible for setting up the GUI and controlling app flow
"""

# Import the modular components
from admin_check import is_admin, check_admin_and_elevate, ensure_admin
from imports import *
from win_api import *
from recorder import *
from ui_helpers import *
from config import *
from death_detection import *
from minimalist_mode import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from recording_manager import RecordingManager

# Global variables
running = False
recording = False
playback_thread = None
recorded_actions = []
last_action_time = None

# Initialize global state in recorder module
def update_recorder_state():
    """Update the recorder module with current global state."""
    death_active = is_death_detection_active()
    # Use get_recorded_actions() to get the current actions from the recorder module
    current_actions = get_recorded_actions()
    set_global_state(current_actions, last_action_time, running, death_active)

# GUI class
class MacroRecorderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Rappelz Macro Recorder")
        self.root.resizable(True, True)

        # Set minimum size
        self.root.minsize(600, 700)

        # Minimalist mode variables
        self.minimalist_mode = False
        self.minimalist_window = MinimalistWindow(self)

        # Load saved minimalist settings if available
        self.minimalist_settings = load_minimalist_settings()

        # Apply window settings
        if game_window_settings['stay_on_top']:
            self.root.attributes("-topmost", True)
        else:
            self.root.attributes("-topmost", False)

        # Color scheme
        self.colors = {
            'primary': '#2c3e50',      # Dark blue-gray
            'secondary': '#34495e',    # Lighter blue-gray
            'accent': '#3498db',       # Blue
            'success': '#27ae60',      # Green
            'warning': '#f39c12',      # Orange
            'danger': '#e74c3c',       # Red
            'light': '#ecf0f1',        # Light gray
            'dark': '#2c3e50',         # Dark
            'info': '#3498db'          # Info blue (same as accent)
        }

        # Configure style
        self._configure_style()

        # Create GUI first (this creates status_var and other UI components)
        self._create_widgets()

        # Initialize recording manager after widgets are created
        self.recording_manager = RecordingManager(self)

        # Connect recording manager to UI components
        self._connect_recording_manager()

        # Set up keyboard and mouse hooks for recording
        self._setup_hooks()

        # Set up window close handler
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        # Attempt to load the last used macro
        self.root.after(1000, self._load_last_macro)  # Delay slightly to ensure UI is fully loaded

        # Load and refresh custom templates display
        self.root.after(1500, self._load_custom_templates_on_startup)

    def _configure_style(self):
        """Configure the ttk style."""
        style = ttk.Style()
        
        # Configure notebook style
        style.configure('TNotebook', background=self.colors['light'])
        style.configure('TNotebook.Tab', padding=[20, 10])
        
        # Configure frame styles
        style.configure('Card.TFrame', background='white', relief='solid', borderwidth=1)
        
        # Configure label styles
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'), background='white')
        style.configure('Success.TLabel', foreground=self.colors['success'], background='white')
        style.configure('Warning.TLabel', foreground=self.colors['warning'], background='white')
        style.configure('Danger.TLabel', foreground=self.colors['danger'], background='white')
        style.configure('Highlight.TLabel', foreground=self.colors['accent'], background='white')

    def _create_widgets(self):
        """Create the main GUI widgets."""
        # Create main container
        main_container = ttk.Frame(self.root, padding=10)
        main_container.grid(row=0, column=0, sticky="nsew")
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_container.columnconfigure(0, weight=1)
        main_container.rowconfigure(1, weight=1)

        # Create header
        self._create_header(main_container)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_container)
        self.notebook.grid(row=1, column=0, sticky="nsew", pady=(10, 0))
        
        # Create tabs
        self._create_main_tab()
        self._create_recording_tab()
        self._create_playback_tab()
        self._create_death_detection_tab()
        self._create_custom_button_tab()
        self._create_timed_action_tab()
        self._create_settings_tab()
        
        # Create status bar
        self._create_status_bar(main_container)

    def _connect_recording_manager(self):
        """Connect the recording manager to UI components."""
        try:
            # Set up UI component references
            self.recording_manager.set_ui_components(
                actions_listbox=getattr(self, 'actions_listbox', None),
                action_details_text=getattr(self, 'action_info_text', None),  # Use text widget
                record_button=getattr(self, 'record_button', None),
                pause_button=None,  # We don't have a separate pause button
                stop_button=None    # We don't have a separate stop button
            )

            # Initial refresh
            self.recording_manager.refresh_display()

            logger.info("Recording manager connected to UI components")

        except Exception as e:
            logger.error(f"Error connecting recording manager: {e}")

    def _create_header(self, parent):
        """Create the header section."""
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        header_frame.columnconfigure(0, weight=1)

        # Title
        title_label = ttk.Label(header_frame, text="Rappelz Macro Recorder", 
                               font=('Arial', 16, 'bold'), style="Heading.TLabel")
        title_label.grid(row=0, column=0, sticky="w")

        # Admin status
        admin_status = "Administrator" if is_admin() else "Standard User"
        admin_color = "Success.TLabel" if is_admin() else "Warning.TLabel"
        admin_label = ttk.Label(header_frame, text=f"Running as: {admin_status}", style=admin_color)
        admin_label.grid(row=0, column=1, sticky="e")

    def _create_main_tab(self):
        """Create the main welcome tab."""
        main_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(main_frame, text="Welcome")
        
        # Welcome message
        welcome_text = (
            "Welcome to Rappelz Macro Recorder!\n\n"
            "This tool helps you automate repetitive tasks in Rappelz.\n\n"
            "• Use the Recording tab to record your actions\n"
            "• Use the Playback tab to replay your recorded actions\n"
            "• Use the Death Detection tab to set up automatic revival\n"
            "• Use the Custom Buttons tab to create automated button clicking\n"
            "• Use the Settings tab to configure game window settings\n\n"
            "Hotkeys:\n"
            "• F3: Test all custom templates\n"
            "• F8: Start/stop recording\n"
            "• F9: Start/stop playback\n"
            "• F10: Toggle minimalist mode\n"
            "• F12: Toggle death detection"
        )
        
        welcome_label = ttk.Label(main_frame, text=welcome_text, justify="left", 
                                 font=('Arial', 11), wraplength=500)
        welcome_label.grid(row=0, column=0, sticky="w", padx=20, pady=20)

    def _create_recording_tab(self):
        """Create the recording tab."""
        recording_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(recording_frame, text="Recording")
        
        # Recording status
        self.recording_var = tk.StringVar(value="Not Recording")
        status_label = ttk.Label(recording_frame, textvariable=self.recording_var, 
                                font=('Arial', 12, 'bold'), style="Heading.TLabel")
        status_label.grid(row=0, column=0, pady=(0, 20))
        
        # Record button
        self.record_button = create_styled_button(
            recording_frame, 
            "Start Recording (F8)",
            self._toggle_recording,
            self.colors['accent']
        )
        self.record_button.grid(row=1, column=0, pady=10)
        
        # Actions list
        self._create_actions_list(recording_frame)

    def _create_actions_list(self, parent):
        """Create the actions list widget."""
        # Actions frame
        actions_frame = ttk.LabelFrame(parent, text="Recorded Actions", padding=10)
        actions_frame.grid(row=2, column=0, sticky="nsew", pady=20)
        parent.rowconfigure(2, weight=1)
        parent.columnconfigure(0, weight=1)
        
        # Listbox with scrollbar
        list_frame = ttk.Frame(actions_frame)
        list_frame.grid(row=0, column=0, sticky="nsew")
        actions_frame.rowconfigure(0, weight=1)
        actions_frame.columnconfigure(0, weight=1)
        
        self.actions_listbox = tk.Listbox(list_frame, font=('Consolas', 10), 
                                         bg='#1e1e1e', fg='white', selectbackground='#3498db')
        self.actions_listbox.grid(row=0, column=0, sticky="nsew")
        
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.actions_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky="ns")
        self.actions_listbox.configure(yscrollcommand=scrollbar.set)
        
        list_frame.rowconfigure(0, weight=1)
        list_frame.columnconfigure(0, weight=1)
        
        # Bind events
        self.actions_listbox.bind("<Double-1>", self._edit_selected_action)
        self.actions_listbox.bind("<Button-3>", self._show_action_context_menu)
        self.actions_listbox.bind("<<ListboxSelect>>", self._on_action_select)

        # Action management buttons
        self._create_action_management_buttons(actions_frame)

    def _create_action_management_buttons(self, parent):
        """Create action management buttons."""
        try:
            # Button frame
            button_frame = ttk.Frame(parent)
            button_frame.grid(row=1, column=0, sticky="ew", pady=(10, 0))
            parent.columnconfigure(0, weight=1)

            # Create buttons in a grid layout
            buttons_config = [
                # Row 0
                [
                    ("Edit Action", self._edit_selected_action, self.colors.get('accent', '#3498db')),
                    ("Delete Action", self._delete_selected_action, self.colors.get('danger', '#e74c3c')),
                    ("Duplicate Action", self._duplicate_selected_action, self.colors.get('success', '#27ae60')),
                    ("Insert Delay", self._insert_delay_action, self.colors.get('warning', '#f39c12'))
                ],
                # Row 1
                [
                    ("Move Up", self._move_action_up, self.colors.get('info', '#3498db')),
                    ("Move Down", self._move_action_down, self.colors.get('info', '#3498db')),
                    ("Import Actions", self._import_actions, self.colors.get('success', '#27ae60')),
                    ("Export Actions", self._export_actions, self.colors.get('accent', '#3498db'))
                ],
                # Row 2
                [
                    ("Clear All", self._clear_all_actions, self.colors.get('danger', '#e74c3c')),
                    ("Save as Macro", self._save_as_macro, self.colors.get('warning', '#f39c12')),
                    ("Load Macro", self._load_macro, self.colors.get('info', '#3498db')),
                    ("Quick Save", self._quick_save_actions, self.colors.get('success', '#27ae60'))
                ]
            ]

            for row_idx, button_row in enumerate(buttons_config):
                for col_idx, (text, command, color) in enumerate(button_row):
                    try:
                        btn = create_styled_button(button_frame, text, command, color)
                        btn.grid(row=row_idx, column=col_idx, padx=5, pady=5, sticky="ew")
                        button_frame.columnconfigure(col_idx, weight=1)
                    except Exception as e:
                        logger.error(f"Error creating button '{text}': {e}")

            # Action info frame
            info_frame = ttk.LabelFrame(parent, text="Action Information", padding=5)
            info_frame.grid(row=2, column=0, sticky="ew", pady=(10, 0))

            # Action details
            self.action_info_var = tk.StringVar(value="Select an action to view details")
            info_label = ttk.Label(info_frame, textvariable=self.action_info_var,
                                  font=('Consolas', 9), wraplength=400)
            info_label.grid(row=0, column=0, sticky="w")

        except Exception as e:
            logger.error(f"Error creating action management buttons: {e}")
            # Create a simple fallback
            fallback_label = ttk.Label(parent, text="Action management temporarily unavailable")
            fallback_label.grid(row=1, column=0, pady=10)

    def _create_playback_tab(self):
        """Create the playback tab."""
        playback_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(playback_frame, text="Playback")
        
        # Playback settings
        settings_frame = ttk.LabelFrame(playback_frame, text="Playback Settings", padding=10)
        settings_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))
        playback_frame.columnconfigure(0, weight=1)
        
        # Repeat count
        ttk.Label(settings_frame, text="Repeat Count:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.repeat_count_var = tk.IntVar(value=1)
        repeat_spinbox = ttk.Spinbox(settings_frame, from_=1, to=1000, textvariable=self.repeat_count_var, width=10)
        repeat_spinbox.grid(row=0, column=1, sticky="w", padx=5, pady=5)
        
        # Infinite mode
        self.infinite_var = tk.BooleanVar()
        infinite_check = ttk.Checkbutton(settings_frame, text="Infinite Mode", variable=self.infinite_var)
        infinite_check.grid(row=0, column=2, sticky="w", padx=20, pady=5)
        
        # Repeat delay
        ttk.Label(settings_frame, text="Delay between repeats (seconds):").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.repeat_delay_var = tk.DoubleVar(value=0.5)
        delay_spinbox = ttk.Spinbox(settings_frame, from_=0, to=60, increment=0.1, textvariable=self.repeat_delay_var, width=10)
        delay_spinbox.grid(row=1, column=1, sticky="w", padx=5, pady=5)
        
        # Playback button
        self.playback_button = create_styled_button(
            playback_frame,
            "Start Playback (F9)",
            self._toggle_playback,
            self.colors['success']
        )
        self.playback_button.grid(row=1, column=0, pady=20)

    def _create_death_detection_tab(self):
        """Create the death detection tab."""
        death_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(death_frame, text="Death Detection")
        
        # Death detection settings
        settings_frame = ttk.LabelFrame(death_frame, text="Death Detection Settings", padding=10)
        settings_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))
        death_frame.columnconfigure(0, weight=1)
        
        # Enable death detection
        self.death_enabled_var = tk.BooleanVar()
        death_check = ttk.Checkbutton(settings_frame, text="Enable Death Detection", 
                                     variable=self.death_enabled_var, command=self._toggle_death_detection)
        death_check.grid(row=0, column=0, sticky="w", padx=5, pady=5)
        
        # Check interval
        ttk.Label(settings_frame, text="Check Interval (seconds):").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.check_interval_var = tk.DoubleVar(value=2.0)
        interval_spinbox = ttk.Spinbox(settings_frame, from_=0.5, to=10, increment=0.5, 
                                      textvariable=self.check_interval_var, width=10)
        interval_spinbox.grid(row=1, column=1, sticky="w", padx=5, pady=5)
        
        # Auto-click revival
        self.auto_click_var = tk.BooleanVar(value=True)
        auto_click_check = ttk.Checkbutton(settings_frame, text="Auto-click Revival Button", 
                                          variable=self.auto_click_var)
        auto_click_check.grid(row=2, column=0, sticky="w", padx=5, pady=5)
        
        # Confidence threshold
        ttk.Label(settings_frame, text="Confidence Threshold (%):").grid(row=3, column=0, sticky="w", padx=5, pady=5)
        self.confidence_var = tk.IntVar(value=85)
        confidence_spinbox = ttk.Spinbox(settings_frame, from_=50, to=99, textvariable=self.confidence_var, width=10)
        confidence_spinbox.grid(row=3, column=1, sticky="w", padx=5, pady=5)

        # Template management section
        self._create_template_management_section(death_frame)

    def _create_template_management_section(self, parent):
        """Create the template management section for death detection."""
        # Template section
        template_frame = ttk.LabelFrame(parent, text="Revival Button Template", padding=10)
        template_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=10)
        template_frame.columnconfigure(1, weight=1)

        # Template status
        ttk.Label(template_frame, text="Template Status:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.template_status_var = tk.StringVar(value="Not set")
        template_status_label = ttk.Label(template_frame, textvariable=self.template_status_var,
                                         font=('Arial', 9, 'italic'))
        template_status_label.grid(row=0, column=1, sticky="w", padx=5, pady=5)

        # Template preview frame
        preview_frame = ttk.LabelFrame(template_frame, text="Template Preview", padding=5)
        preview_frame.grid(row=1, column=0, columnspan=2, sticky="ew", padx=5, pady=10)
        preview_frame.columnconfigure(0, weight=1)

        # Template image display
        self.template_display = ttk.Label(preview_frame, text="No template loaded",
                                         background='white', relief='sunken',
                                         width=30, anchor='center')
        self.template_display.grid(row=0, column=0, sticky="ew", padx=5, pady=5)

        # Template buttons
        buttons_frame = ttk.Frame(template_frame)
        buttons_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=5, pady=10)
        buttons_frame.columnconfigure(0, weight=1)
        buttons_frame.columnconfigure(1, weight=1)
        buttons_frame.columnconfigure(2, weight=1)

        # Capture template button
        capture_btn = create_styled_button(
            buttons_frame,
            "📸 Capture Template",
            self._capture_revival_template,
            self.colors['accent']
        )
        capture_btn.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        # Browse template button
        browse_btn = create_styled_button(
            buttons_frame,
            "📁 Browse Template",
            self._browse_revival_template,
            self.colors['info']
        )
        browse_btn.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        # Test template button
        test_btn = create_styled_button(
            buttons_frame,
            "🔍 Test Template",
            self._test_revival_template,
            self.colors['warning']
        )
        test_btn.grid(row=0, column=2, padx=5, pady=5, sticky="ew")

        # Instructions
        instructions_frame = ttk.LabelFrame(template_frame, text="Instructions", padding=5)
        instructions_frame.grid(row=3, column=0, columnspan=2, sticky="ew", padx=5, pady=10)

        instructions_text = (
            "1. Capture Template: Right-click top-left then bottom-right corners\n"
            "2. Browse Template: Select an existing image file\n"
            "3. Test Template: Check if current template works\n"
            "4. Adjust confidence threshold if needed (85% recommended)"
        )

        instructions_label = ttk.Label(instructions_frame, text=instructions_text,
                                      font=('Arial', 8), wraplength=400)
        instructions_label.grid(row=0, column=0, sticky="w", padx=5, pady=5)

        # Update template display
        self._update_template_display()

    def _update_template_display(self):
        """Update the template display with current template status."""
        try:
            from death_detection import get_death_detection_settings
            settings = get_death_detection_settings()
            template_path = settings.get('template_path', '')

            if template_path and os.path.exists(template_path):
                # Template exists
                template_name = os.path.basename(template_path)
                self.template_status_var.set(f"✅ {template_name}")

                # Try to load and display the template image
                try:
                    # Check if PIL is available for better image handling
                    try:
                        from PIL import Image, ImageTk
                        # Load image with PIL
                        img = Image.open(template_path)
                        # Resize to fit display (max 200x100)
                        img.thumbnail((200, 100), Image.Resampling.LANCZOS)
                        # Convert to PhotoImage
                        photo = ImageTk.PhotoImage(img)
                        self.template_display.configure(image=photo, text="")
                        self.template_display.image = photo  # Keep reference
                    except ImportError:
                        # Fallback to tkinter PhotoImage
                        photo = tk.PhotoImage(file=template_path)
                        # Subsample if too large
                        width, height = photo.width(), photo.height()
                        if width > 200 or height > 100:
                            subsample = max(width // 200, height // 100, 1)
                            photo = photo.subsample(subsample)
                        self.template_display.configure(image=photo, text="")
                        self.template_display.image = photo  # Keep reference

                except Exception as e:
                    # If image loading fails, show text
                    self.template_display.configure(image="", text=f"Template: {template_name}\n(Preview unavailable)")
                    logger.warning(f"Could not display template image: {e}")
            else:
                # No template or template doesn't exist
                self.template_status_var.set("❌ No template set")
                self.template_display.configure(image="", text="No template loaded\n\nCapture or browse for a\nrevival button template")

        except Exception as e:
            logger.error(f"Error updating template display: {e}")
            self.template_status_var.set("❌ Error loading template")
            self.template_display.configure(image="", text="Error loading template")

    def _capture_revival_template(self):
        """Capture a new revival button template."""
        try:
            # Show instructions dialog
            result = messagebox.askokcancel(
                "Capture Revival Button Template",
                "Instructions:\n\n"
                "1. Click OK to start capture mode\n"
                "2. RIGHT-CLICK on the TOP-LEFT corner of the revival button\n"
                "3. RIGHT-CLICK on the BOTTOM-RIGHT corner of the revival button\n"
                "4. The template will be captured automatically\n"
                "5. Press ESC to cancel capture mode\n\n"
                "Make sure the revival button is clearly visible!\n"
                "No overlay will appear - just right-click the two corners."
            )

            if not result:
                return

            # Minimize the main window
            self.root.iconify()
            time.sleep(0.5)  # Give time for window to minimize

            # Start direct capture mode (no overlay)
            self._start_direct_capture()

        except Exception as e:
            logger.error(f"Error starting template capture: {e}")
            show_warning_message("Capture Error", f"Failed to start template capture: {e}")
            # Restore window
            self.root.deiconify()

    def _start_direct_capture(self):
        """Start direct capture mode without overlay."""
        try:
            # Initialize capture state
            self.capture_points = []
            self.capture_active = True

            # Set up global mouse hook for right-clicks
            if HAVE_MOUSE:
                try:
                    import mouse
                    # Hook right mouse button clicks with proper callback format
                    self.mouse_hook = mouse.hook(self._handle_direct_click)
                    logger.info("Direct capture mode started - right-click two corners")

                    # Show status message
                    self.status_var.set("Capture mode active: Right-click TOP-LEFT corner of revival button")

                except Exception as e:
                    logger.error(f"Error setting up mouse hook: {e}")
                    show_warning_message("Capture Error", f"Failed to start capture mode: {e}")
                    self.root.deiconify()
                    return
            else:
                show_warning_message("Capture Error", "Mouse library not available for capture mode")
                self.root.deiconify()
                return

            # Set up keyboard hook for ESC to cancel
            if HAVE_KEYBOARD:
                try:
                    keyboard.add_hotkey('esc', self._cancel_direct_capture)
                except Exception as e:
                    logger.warning(f"Could not set up ESC hotkey: {e}")

        except Exception as e:
            logger.error(f"Error starting direct capture: {e}")
            show_warning_message("Capture Error", f"Failed to start capture mode: {e}")
            self.root.deiconify()

    def _handle_direct_click(self, event):
        """Handle right-click events for direct capture."""
        try:
            # Filter out non-click events (like MoveEvent)
            if not hasattr(event, 'event_type'):
                return  # Ignore move events and other non-click events

            # Debug logging for click events only
            logger.info(f"Mouse event detected: type={event.event_type}, button={getattr(event, 'button', 'unknown')}")

            if not hasattr(self, 'capture_active') or not self.capture_active:
                logger.info("Capture not active, ignoring click")
                return

            # Only handle right-click down events
            if event.event_type != 'down' or event.button != 'right':
                logger.info(f"Ignoring event: type={event.event_type}, button={event.button}")
                return

            # Get current mouse position using pyautogui (ButtonEvent doesn't have x,y)
            x, y = pyautogui.position()
            logger.info(f"Processing right-click at ({x}, {y})")
            self.capture_points.append((x, y))

            if len(self.capture_points) == 1:
                # First click - top-left corner
                self.status_var.set(f"First corner captured at ({x}, {y}). Right-click BOTTOM-RIGHT corner.")
                logger.info(f"First corner captured at ({x}, {y})")

            elif len(self.capture_points) == 2:
                # Second click - bottom-right corner
                self.status_var.set(f"Second corner captured at ({x}, {y}). Processing template...")
                logger.info(f"Second corner captured at ({x}, {y})")

                # Process the capture
                self._process_direct_capture()

        except Exception as e:
            logger.error(f"Error handling direct click: {e}")
            self._cancel_direct_capture()

    def _process_direct_capture(self):
        """Process the captured rectangle and create template."""
        try:
            if len(self.capture_points) != 2:
                raise ValueError("Need exactly 2 points for rectangle capture")

            x1, y1 = self.capture_points[0]
            x2, y2 = self.capture_points[1]

            # Ensure proper rectangle coordinates
            left = min(x1, x2)
            top = min(y1, y2)
            right = max(x1, x2)
            bottom = max(y1, y2)

            width = right - left
            height = bottom - top

            # Validate rectangle size
            if width < 10 or height < 10:
                raise ValueError("Template area too small (minimum 10x10 pixels)")

            if width > 500 or height > 500:
                raise ValueError("Template area too large (maximum 500x500 pixels)")

            # Clean up hooks
            self._cleanup_direct_capture()

            # Small delay to ensure clean capture
            time.sleep(0.2)

            # Capture the rectangle
            screenshot = pyautogui.screenshot(region=(left, top, width, height))

            # Save the template
            from death_detection import get_death_detection_settings
            settings = get_death_detection_settings()
            template_path = settings['template_path']

            # Create templates directory if needed
            import os
            os.makedirs(os.path.dirname(template_path), exist_ok=True)

            # Save the screenshot as template
            screenshot.save(template_path)

            # Restore main window
            self.root.deiconify()

            # Update display
            self._update_template_display()

            # Show success message
            show_info_message("Capture Success",
                f"Revival button template captured successfully!\n\n"
                f"Area: ({left}, {top}) to ({right}, {bottom})\n"
                f"Size: {width}x{height} pixels\n"
                f"Template saved and ready to use.")

            self.status_var.set("Template capture completed successfully")

        except Exception as e:
            logger.error(f"Error processing direct capture: {e}")
            self._cleanup_direct_capture()
            self.root.deiconify()
            show_warning_message("Capture Error", f"Failed to capture template: {e}")

    def _cancel_direct_capture(self):
        """Cancel direct capture mode."""
        try:
            self._cleanup_direct_capture()
            self.root.deiconify()
            self.status_var.set("Template capture cancelled")
            logger.info("Template capture cancelled by user")

        except Exception as e:
            logger.error(f"Error canceling direct capture: {e}")
            self.root.deiconify()

    def _cleanup_direct_capture(self):
        """Clean up direct capture resources."""
        try:
            # Mark capture as inactive
            self.capture_active = False

            # Remove mouse hook
            if hasattr(self, 'mouse_hook') and HAVE_MOUSE:
                import mouse
                mouse.unhook(self.mouse_hook)
                delattr(self, 'mouse_hook')

            # Remove ESC hotkey
            if HAVE_KEYBOARD:
                try:
                    keyboard.remove_hotkey('esc')
                except:
                    pass

            # Clear capture points
            if hasattr(self, 'capture_points'):
                self.capture_points.clear()

        except Exception as e:
            logger.error(f"Error cleaning up direct capture: {e}")

    def _do_template_capture(self, overlay=None):
        """Legacy method - redirects to direct capture."""
        # This method is kept for compatibility but now uses direct capture
        self._process_direct_capture()

    def _browse_revival_template(self):
        """Browse for an existing revival button template image."""
        try:
            from tkinter import filedialog

            filename = filedialog.askopenfilename(
                title="Select Revival Button Template",
                filetypes=[
                    ("Image files", "*.png;*.jpg;*.jpeg;*.bmp"),
                    ("PNG files", "*.png"),
                    ("JPEG files", "*.jpg;*.jpeg"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                # Copy the selected file to the templates directory
                from death_detection import get_death_detection_settings
                import shutil

                settings = get_death_detection_settings()
                template_path = settings['template_path']

                # Create templates directory if needed
                os.makedirs(os.path.dirname(template_path), exist_ok=True)

                # Copy the file
                shutil.copy2(filename, template_path)

                # Update display
                self._update_template_display()

                show_info_message("Template Updated",
                    f"Revival button template updated successfully!\n\n"
                    f"Source: {os.path.basename(filename)}\n"
                    f"Template is ready to use.")

        except Exception as e:
            logger.error(f"Error browsing for template: {e}")
            show_warning_message("Browse Error", f"Failed to update template: {e}")

    def _test_revival_template(self):
        """Test the current revival button template."""
        try:
            from death_detection import get_death_detection_settings, click_revival_button

            settings = get_death_detection_settings()
            template_path = settings.get('template_path', '')

            if not template_path or not os.path.exists(template_path):
                show_warning_message("No Template",
                    "No template found. Please capture or browse for a template first.")
                return

            # Show test dialog
            result = messagebox.askokcancel(
                "Test Revival Button Template",
                "This will test if the revival button can be detected on screen.\n\n"
                "Make sure your game is visible and the revival button is showing.\n\n"
                "Click OK to test (no clicking will be performed)."
            )

            if not result:
                return

            # Temporarily disable auto-click for testing
            original_auto_click = settings.get('auto_click_revival', True)
            from death_detection import update_death_detection_settings
            update_death_detection_settings(auto_click_revival=False)

            try:
                # Test the template
                success = click_revival_button()

                if success:
                    show_info_message("Test Success",
                        "✅ Revival button template test successful!\n\n"
                        "The template was detected on screen.\n"
                        "Death detection should work properly.")
                else:
                    show_warning_message("Test Failed",
                        "❌ Revival button template test failed.\n\n"
                        "The template was not detected on screen.\n"
                        "Try:\n"
                        "• Capturing a new template\n"
                        "• Lowering the confidence threshold\n"
                        "• Making sure the revival button is visible")

            finally:
                # Restore original auto-click setting
                update_death_detection_settings(auto_click_revival=original_auto_click)

        except Exception as e:
            logger.error(f"Error testing template: {e}")
            show_warning_message("Test Error", f"Failed to test template: {e}")

    # Custom Button Management Methods
    def _toggle_custom_button_detection(self):
        """Toggle custom button detection on/off."""
        try:
            from custom_button_manager import (
                start_custom_button_detection, stop_custom_button_detection,
                is_custom_button_active, update_custom_button_settings
            )

            if self.custom_button_enabled_var.get():
                # Update settings before starting
                update_custom_button_settings(
                    check_interval=self.custom_check_interval_var.get(),
                    global_cooldown=self.custom_cooldown_var.get()
                )

                if start_custom_button_detection():
                    self.status_var.set("Custom button detection enabled")
                    logger.info("Custom button detection started")
                else:
                    self.custom_button_enabled_var.set(False)
                    self.status_var.set("Failed to start custom button detection")
            else:
                if stop_custom_button_detection():
                    self.status_var.set("Custom button detection disabled")
                    logger.info("Custom button detection stopped")
                else:
                    self.status_var.set("Failed to stop custom button detection")

        except Exception as e:
            logger.error(f"Error toggling custom button detection: {e}")
            show_warning_message("Custom Button Error", f"Failed to toggle detection: {e}")
            self.custom_button_enabled_var.set(False)

    def _refresh_templates_display(self):
        """Refresh the templates display."""
        try:
            from custom_button_manager import get_all_custom_templates, get_template_statistics

            # Clear existing items
            for item in self.templates_tree.get_children():
                self.templates_tree.delete(item)

            # Get templates
            templates = get_all_custom_templates()

            # Add templates to tree
            for name, template in templates.items():
                enabled = "✅" if template.enabled else "❌"
                confidence = f"{template.confidence:.2f}"
                auto_click = "✅" if template.auto_click else "❌"
                detections = str(template.detection_count)

                self.templates_tree.insert('', 'end', values=(name, enabled, confidence, auto_click, detections))

            # Update statistics
            stats = get_template_statistics()
            stats_text = (
                f"Total: {stats['total_templates']} | "
                f"Enabled: {stats['enabled_templates']} | "
                f"Total Detections: {stats['total_detections']}"
            )
            self.template_stats_var.set(stats_text)

        except Exception as e:
            logger.error(f"Error refreshing templates display: {e}")
            self.template_stats_var.set("Error loading templates")

    def _add_custom_template(self):
        """Add a new custom template."""
        try:
            from tkinter import filedialog
            from custom_button_manager import create_template_from_file

            # Browse for image file
            file_path = filedialog.askopenfilename(
                title="Select Template Image",
                filetypes=[
                    ("Image files", "*.png *.jpg *.jpeg *.bmp *.gif"),
                    ("PNG files", "*.png"),
                    ("JPEG files", "*.jpg *.jpeg"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            # Get template name from user
            import tkinter.simpledialog as simpledialog
            name = simpledialog.askstring(
                "Template Name",
                "Enter a name for this template:",
                initialvalue=os.path.splitext(os.path.basename(file_path))[0]
            )

            if not name:
                return

            # Create template with default settings
            template = create_template_from_file(
                name=name,
                image_path=file_path,
                confidence=0.85,
                auto_click=True,
                enabled=True
            )

            if template:
                self._refresh_templates_display()
                self.status_var.set(f"Template '{name}' added successfully")
                logger.info(f"Added custom template: {name}")
            else:
                show_warning_message("Add Template Error", "Failed to create template")

        except Exception as e:
            logger.error(f"Error adding custom template: {e}")
            show_warning_message("Add Template Error", f"Failed to add template: {e}")

    def _edit_custom_template(self, event=None):
        """Edit the selected custom template."""
        try:
            selection = self.templates_tree.selection()
            if not selection:
                show_info_message("No Selection", "Please select a template to edit")
                return

            # Get template name from selection
            item = self.templates_tree.item(selection[0])
            template_name = item['values'][0]

            # Show edit dialog
            self._show_template_edit_dialog(template_name)

        except Exception as e:
            logger.error(f"Error editing custom template: {e}")
            show_warning_message("Edit Template Error", f"Failed to edit template: {e}")

    def _delete_custom_template(self):
        """Delete the selected custom template."""
        try:
            from custom_button_manager import remove_custom_template

            selection = self.templates_tree.selection()
            if not selection:
                show_info_message("No Selection", "Please select a template to delete")
                return

            # Get template name from selection
            item = self.templates_tree.item(selection[0])
            template_name = item['values'][0]

            # Confirm deletion
            result = messagebox.askyesno(
                "Delete Template",
                f"Are you sure you want to delete the template '{template_name}'?\n\n"
                "This action cannot be undone."
            )

            if result:
                if remove_custom_template(template_name):
                    self._refresh_templates_display()
                    self.status_var.set(f"Template '{template_name}' deleted")
                    logger.info(f"Deleted custom template: {template_name}")
                else:
                    show_warning_message("Delete Error", f"Failed to delete template '{template_name}'")

        except Exception as e:
            logger.error(f"Error deleting custom template: {e}")
            show_warning_message("Delete Template Error", f"Failed to delete template: {e}")

    def _test_custom_template(self):
        """Test the selected custom template."""
        try:
            from custom_button_manager import get_custom_template, detect_template

            selection = self.templates_tree.selection()
            if not selection:
                show_info_message("No Selection", "Please select a template to test")
                return

            # Get template name from selection
            item = self.templates_tree.item(selection[0])
            template_name = item['values'][0]

            # Get template
            template = get_custom_template(template_name)
            if not template:
                show_warning_message("Test Error", f"Template '{template_name}' not found")
                return

            # Test detection
            self.status_var.set(f"Testing template '{template_name}'...")
            self.root.update()

            # Temporarily disable auto-click for testing
            original_auto_click = template.auto_click
            template.auto_click = False

            try:
                result = detect_template(template)
                if result:
                    show_info_message("Test Result", f"✅ Template '{template_name}' detected successfully!")
                    self.status_var.set(f"Template '{template_name}' test: SUCCESS")
                else:
                    show_info_message("Test Result", f"❌ Template '{template_name}' not detected")
                    self.status_var.set(f"Template '{template_name}' test: NOT DETECTED")
            finally:
                # Restore original auto-click setting
                template.auto_click = original_auto_click

        except Exception as e:
            logger.error(f"Error testing custom template: {e}")
            show_warning_message("Test Template Error", f"Failed to test template: {e}")

    def _enable_all_templates(self):
        """Enable all custom templates."""
        try:
            from custom_button_manager import get_all_custom_templates, update_custom_template

            templates = get_all_custom_templates()
            enabled_count = 0

            for name in templates.keys():
                if update_custom_template(name, enabled=True):
                    enabled_count += 1

            self._refresh_templates_display()
            self.status_var.set(f"Enabled {enabled_count} templates")
            logger.info(f"Enabled {enabled_count} custom templates")

        except Exception as e:
            logger.error(f"Error enabling all templates: {e}")
            show_warning_message("Enable Error", f"Failed to enable templates: {e}")

    def _disable_all_templates(self):
        """Disable all custom templates."""
        try:
            from custom_button_manager import get_all_custom_templates, update_custom_template

            templates = get_all_custom_templates()
            disabled_count = 0

            for name in templates.keys():
                if update_custom_template(name, enabled=False):
                    disabled_count += 1

            self._refresh_templates_display()
            self.status_var.set(f"Disabled {disabled_count} templates")
            logger.info(f"Disabled {disabled_count} custom templates")

        except Exception as e:
            logger.error(f"Error disabling all templates: {e}")
            show_warning_message("Disable Error", f"Failed to disable templates: {e}")

    def _export_templates_config(self):
        """Export templates configuration to a file."""
        try:
            from tkinter import filedialog
            from custom_button_manager import export_templates_config

            file_path = filedialog.asksaveasfilename(
                title="Export Templates Configuration",
                defaultextension=".json",
                filetypes=[
                    ("JSON files", "*.json"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                if export_templates_config(file_path):
                    show_info_message("Export Complete", f"Templates configuration exported to:\n{file_path}")
                    self.status_var.set("Templates configuration exported")
                else:
                    show_warning_message("Export Error", "Failed to export templates configuration")

        except Exception as e:
            logger.error(f"Error exporting templates config: {e}")
            show_warning_message("Export Error", f"Failed to export configuration: {e}")

    def _import_templates_config(self):
        """Import templates configuration from a file."""
        try:
            from tkinter import filedialog
            from custom_button_manager import import_templates_config

            file_path = filedialog.askopenfilename(
                title="Import Templates Configuration",
                filetypes=[
                    ("JSON files", "*.json"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                if import_templates_config(file_path):
                    self._refresh_templates_display()
                    show_info_message("Import Complete", f"Templates configuration imported from:\n{file_path}")
                    self.status_var.set("Templates configuration imported")
                else:
                    show_warning_message("Import Error", "Failed to import templates configuration")

        except Exception as e:
            logger.error(f"Error importing templates config: {e}")
            show_warning_message("Import Error", f"Failed to import configuration: {e}")

    def _show_template_context_menu(self, event):
        """Show context menu for templates."""
        try:
            # Create context menu
            context_menu = tk.Menu(self.root, tearoff=0)

            selection = self.templates_tree.selection()
            if selection:
                context_menu.add_command(label="Edit Template", command=self._edit_custom_template)
                context_menu.add_command(label="Test Template", command=self._test_custom_template)
                context_menu.add_command(label="Delete Template", command=self._delete_custom_template)
                context_menu.add_separator()

            context_menu.add_command(label="Add Template", command=self._add_custom_template)
            context_menu.add_separator()
            context_menu.add_command(label="Enable All", command=self._enable_all_templates)
            context_menu.add_command(label="Disable All", command=self._disable_all_templates)
            context_menu.add_separator()
            context_menu.add_command(label="Export Config", command=self._export_templates_config)
            context_menu.add_command(label="Import Config", command=self._import_templates_config)

            context_menu.tk_popup(event.x_root, event.y_root)

        except Exception as e:
            logger.error(f"Error showing template context menu: {e}")
        finally:
            try:
                context_menu.grab_release()
            except:
                pass

    def _capture_custom_template(self):
        """Capture a new custom template from screen."""
        try:
            # Get template name from user
            import tkinter.simpledialog as simpledialog
            name = simpledialog.askstring(
                "Template Name",
                "Enter a name for this template:",
                initialvalue="new_template"
            )

            if not name:
                return

            # Check if name already exists
            from custom_button_manager import get_custom_template
            if get_custom_template(name):
                result = messagebox.askyesno(
                    "Template Exists",
                    f"A template named '{name}' already exists.\n\nDo you want to replace it?"
                )
                if not result:
                    return

            # Hide main window and start capture
            self.root.withdraw()
            self.status_var.set("Starting template capture...")

            # Start direct capture process
            self._start_custom_template_capture(name)

        except Exception as e:
            logger.error(f"Error starting custom template capture: {e}")
            show_warning_message("Capture Error", f"Failed to start template capture: {e}")
            self.root.deiconify()

    def _start_custom_template_capture(self, template_name):
        """Start the custom template capture process."""
        try:
            # Initialize capture state
            self.capture_state = {
                'template_name': template_name,
                'points': [],
                'capturing': True
            }

            # Create capture instructions window
            self._create_capture_instructions()

            # Set up mouse hook for capture
            self._setup_capture_hooks()

        except Exception as e:
            logger.error(f"Error setting up custom template capture: {e}")
            show_warning_message("Capture Error", f"Failed to set up capture: {e}")
            self.root.deiconify()

    def _create_capture_instructions(self):
        """Create instructions window for template capture."""
        try:
            # Create instruction window
            self.capture_window = tk.Toplevel()
            self.capture_window.title("Template Capture")
            self.capture_window.geometry("400x200")
            self.capture_window.attributes("-topmost", True)
            self.capture_window.configure(bg='black')

            # Center on screen
            self.capture_window.geometry("+%d+%d" % (
                (self.capture_window.winfo_screenwidth() // 2) - 200,
                (self.capture_window.winfo_screenheight() // 2) - 100
            ))

            # Instructions
            instructions = (
                "Template Capture Mode\n\n"
                "1. Right-click on the TOP-LEFT corner of the button\n"
                "2. Right-click on the BOTTOM-RIGHT corner of the button\n"
                "3. The template will be captured automatically\n\n"
                "Press ESC to cancel"
            )

            label = tk.Label(
                self.capture_window,
                text=instructions,
                font=('Arial', 12),
                fg='white',
                bg='black',
                justify='center'
            )
            label.pack(expand=True, fill='both', padx=20, pady=20)

            # Bind escape key
            self.capture_window.bind('<Escape>', self._cancel_capture)
            self.capture_window.focus_set()

        except Exception as e:
            logger.error(f"Error creating capture instructions: {e}")

    def _setup_capture_hooks(self):
        """Set up mouse hooks for template capture."""
        try:
            import mouse

            # Set up right-click handler
            def on_right_click():
                if hasattr(self, 'capture_state') and self.capture_state.get('capturing'):
                    x, y = mouse.get_position()
                    self.capture_state['points'].append((x, y))

                    if len(self.capture_state['points']) == 1:
                        logger.info(f"First point captured: ({x}, {y})")
                        # Update instructions
                        if hasattr(self, 'capture_window'):
                            for widget in self.capture_window.winfo_children():
                                if isinstance(widget, tk.Label):
                                    widget.config(text=(
                                        "Template Capture Mode\n\n"
                                        f"✅ Top-left corner captured: ({x}, {y})\n"
                                        "2. Right-click on the BOTTOM-RIGHT corner of the button\n"
                                        "3. The template will be captured automatically\n\n"
                                        "Press ESC to cancel"
                                    ))
                    elif len(self.capture_state['points']) == 2:
                        logger.info(f"Second point captured: ({x}, {y})")
                        # Complete capture
                        self._complete_custom_template_capture()

            # Hook right mouse button
            mouse.on_right_click(on_right_click)

        except Exception as e:
            logger.error(f"Error setting up capture hooks: {e}")
            show_warning_message("Capture Error", f"Failed to set up mouse hooks: {e}")
            self._cancel_capture()

    def _complete_custom_template_capture(self):
        """Complete the custom template capture process."""
        try:
            if not hasattr(self, 'capture_state') or len(self.capture_state['points']) != 2:
                return

            # Get capture coordinates
            x1, y1 = self.capture_state['points'][0]
            x2, y2 = self.capture_state['points'][1]
            template_name = self.capture_state['template_name']

            # Clean up capture state
            self._cleanup_capture()

            # Capture template
            from custom_button_manager import capture_template_from_screen
            template = capture_template_from_screen(
                name=template_name,
                x1=x1, y1=y1, x2=x2, y2=y2,
                confidence=0.85,
                auto_click=True,
                enabled=True,
                cooldown=2.0
            )

            if template:
                self._refresh_templates_display()
                self.status_var.set(f"Template '{template_name}' captured successfully")
                show_info_message("Capture Complete",
                    f"Template '{template_name}' captured successfully!\n\n"
                    f"Region: ({min(x1,x2)}, {min(y1,y2)}) to ({max(x1,x2)}, {max(y1,y2)})\n"
                    f"Size: {abs(x2-x1)} x {abs(y2-y1)} pixels")
                logger.info(f"Captured custom template: {template_name}")
            else:
                show_warning_message("Capture Error", "Failed to capture template")

        except Exception as e:
            logger.error(f"Error completing custom template capture: {e}")
            show_warning_message("Capture Error", f"Failed to complete capture: {e}")
        finally:
            self._cleanup_capture()

    def _cancel_capture(self, event=None):
        """Cancel the template capture process."""
        try:
            self._cleanup_capture()
            self.status_var.set("Template capture cancelled")
            logger.info("Custom template capture cancelled")
        except Exception as e:
            logger.error(f"Error cancelling capture: {e}")

    def _cleanup_capture(self):
        """Clean up capture state and UI."""
        try:
            # Remove mouse hooks
            try:
                import mouse
                mouse.unhook_all()
            except:
                pass

            # Close capture window
            if hasattr(self, 'capture_window'):
                try:
                    self.capture_window.destroy()
                except:
                    pass
                delattr(self, 'capture_window')

            # Clear capture state
            if hasattr(self, 'capture_state'):
                delattr(self, 'capture_state')

            # Restore main window
            self.root.deiconify()

        except Exception as e:
            logger.error(f"Error cleaning up capture: {e}")

    def _reset_template_stats(self):
        """Reset template statistics."""
        try:
            from custom_button_manager import reset_template_statistics

            result = messagebox.askyesno(
                "Reset Statistics",
                "Are you sure you want to reset all template statistics?\n\n"
                "This will clear detection counts and timing data for all templates."
            )

            if result:
                reset_template_statistics()
                self._refresh_templates_display()
                self.status_var.set("Template statistics reset")
                logger.info("Template statistics reset")

        except Exception as e:
            logger.error(f"Error resetting template statistics: {e}")
            show_warning_message("Reset Error", f"Failed to reset statistics: {e}")

    def _test_all_custom_templates(self):
        """Test all enabled custom templates (F3 hotkey)."""
        try:
            from custom_button_manager import test_all_templates_once, get_all_custom_templates

            templates = get_all_custom_templates()
            enabled_templates = [t for t in templates.values() if t.enabled]

            if not enabled_templates:
                self.status_var.set("No enabled templates to test")
                logger.info("F3 pressed: No enabled templates to test")
                return

            self.status_var.set(f"Testing {len(enabled_templates)} templates...")
            logger.info(f"F3 pressed: Testing {len(enabled_templates)} enabled templates")

            # Test all templates
            result = test_all_templates_once()

            if result:
                self.status_var.set("Template test complete - some templates detected")
            else:
                self.status_var.set("Template test complete - no templates detected")

        except Exception as e:
            logger.error(f"Error testing all custom templates: {e}")
            self.status_var.set("Error testing templates")

    def _load_custom_templates_on_startup(self):
        """Load custom templates when the application starts."""
        try:
            from custom_button_manager import load_templates

            # Load templates from disk
            load_templates()

            # Refresh the display if the custom button tab exists
            if hasattr(self, 'templates_tree'):
                self._refresh_templates_display()
                logger.info("Custom templates loaded and display refreshed on startup")

        except Exception as e:
            logger.error(f"Error loading custom templates on startup: {e}")

    # Timed Action Management Methods
    def _toggle_timed_actions_system(self):
        """Toggle timed actions system on/off."""
        try:
            from timed_action import set_timed_actions_enabled, is_timed_actions_enabled, stop_all_timed_actions

            if self.timed_actions_enabled_var.get():
                set_timed_actions_enabled(True)
                self.status_var.set("Timed actions system enabled")
                logger.info("Timed actions system enabled")
            else:
                # Stop all running actions first
                stop_all_timed_actions()
                set_timed_actions_enabled(False)
                self.status_var.set("Timed actions system disabled")
                logger.info("Timed actions system disabled")

            self._refresh_timed_actions_display()

        except Exception as e:
            logger.error(f"Error toggling timed actions system: {e}")
            show_warning_message("Timed Actions Error", f"Failed to toggle system: {e}")
            self.timed_actions_enabled_var.set(False)

    def _refresh_timed_actions_display(self):
        """Refresh the timed actions display."""
        try:
            from timed_action import get_all_timed_actions, get_timed_actions_status

            # Clear existing items
            for item in self.timed_actions_tree.get_children():
                self.timed_actions_tree.delete(item)

            # Get actions
            actions = get_all_timed_actions()

            # Add actions to tree
            for name, action in actions.items():
                keys_str = ", ".join(action.keys)
                interval_str = action.get_interval_display()
                status = "🟢 Running" if action.is_running else ("🟡 Enabled" if action.enabled else "🔴 Disabled")
                executions = str(action.execution_count)

                self.timed_actions_tree.insert('', 'end', values=(name, keys_str, interval_str, status, executions))

            # Update statistics
            status = get_timed_actions_status()
            stats_text = (
                f"Total: {status['total_actions']} | "
                f"Running: {status['running_actions']} | "
                f"System: {'Enabled' if status['enabled'] else 'Disabled'}"
            )
            self.timed_action_stats_var.set(stats_text)

        except Exception as e:
            logger.error(f"Error refreshing timed actions display: {e}")
            self.timed_action_stats_var.set("Error loading actions")

    def _add_timed_action(self):
        """Add a new timed action."""
        try:
            self._show_timed_action_dialog()

        except Exception as e:
            logger.error(f"Error adding timed action: {e}")
            show_warning_message("Add Action Error", f"Failed to add action: {e}")

    def _edit_timed_action(self, event=None):
        """Edit the selected timed action."""
        try:
            selection = self.timed_actions_tree.selection()
            if not selection:
                show_info_message("No Selection", "Please select an action to edit")
                return

            # Get action name from selection
            item = self.timed_actions_tree.item(selection[0])
            action_name = item['values'][0]

            # Show edit dialog
            self._show_timed_action_dialog(action_name)

        except Exception as e:
            logger.error(f"Error editing timed action: {e}")
            show_warning_message("Edit Action Error", f"Failed to edit action: {e}")

    def _delete_timed_action(self):
        """Delete the selected timed action."""
        try:
            from timed_action import remove_timed_action

            selection = self.timed_actions_tree.selection()
            if not selection:
                show_info_message("No Selection", "Please select an action to delete")
                return

            # Get action name from selection
            item = self.timed_actions_tree.item(selection[0])
            action_name = item['values'][0]

            # Confirm deletion
            result = messagebox.askyesno(
                "Delete Action",
                f"Are you sure you want to delete the timed action '{action_name}'?\n\n"
                "This action cannot be undone."
            )

            if result:
                if remove_timed_action(action_name):
                    self._refresh_timed_actions_display()
                    self.status_var.set(f"Timed action '{action_name}' deleted")
                    logger.info(f"Deleted timed action: {action_name}")
                else:
                    show_warning_message("Delete Error", f"Failed to delete action '{action_name}'")

        except Exception as e:
            logger.error(f"Error deleting timed action: {e}")
            show_warning_message("Delete Action Error", f"Failed to delete action: {e}")

    def _start_timed_action(self):
        """Start the selected timed action."""
        try:
            from timed_action import start_timed_action

            selection = self.timed_actions_tree.selection()
            if not selection:
                show_info_message("No Selection", "Please select an action to start")
                return

            # Get action name from selection
            item = self.timed_actions_tree.item(selection[0])
            action_name = item['values'][0]

            if start_timed_action(action_name):
                self._refresh_timed_actions_display()
                self.status_var.set(f"Started timed action '{action_name}'")
                logger.info(f"Started timed action: {action_name}")
            else:
                show_warning_message("Start Error", f"Failed to start action '{action_name}'")

        except Exception as e:
            logger.error(f"Error starting timed action: {e}")
            show_warning_message("Start Action Error", f"Failed to start action: {e}")

    def _stop_timed_action(self):
        """Stop the selected timed action."""
        try:
            from timed_action import stop_timed_action

            selection = self.timed_actions_tree.selection()
            if not selection:
                show_info_message("No Selection", "Please select an action to stop")
                return

            # Get action name from selection
            item = self.timed_actions_tree.item(selection[0])
            action_name = item['values'][0]

            if stop_timed_action(action_name):
                self._refresh_timed_actions_display()
                self.status_var.set(f"Stopped timed action '{action_name}'")
                logger.info(f"Stopped timed action: {action_name}")
            else:
                show_warning_message("Stop Error", f"Failed to stop action '{action_name}'")

        except Exception as e:
            logger.error(f"Error stopping timed action: {e}")
            show_warning_message("Stop Action Error", f"Failed to stop action: {e}")

    def _stop_all_timed_actions(self):
        """Stop all running timed actions."""
        try:
            from timed_action import stop_all_timed_actions

            result = messagebox.askyesno(
                "Stop All Actions",
                "Are you sure you want to stop all running timed actions?"
            )

            if result:
                if stop_all_timed_actions():
                    self._refresh_timed_actions_display()
                    self.status_var.set("All timed actions stopped")
                    logger.info("Stopped all timed actions")
                else:
                    show_warning_message("Stop Error", "Failed to stop all actions")

        except Exception as e:
            logger.error(f"Error stopping all timed actions: {e}")
            show_warning_message("Stop All Error", f"Failed to stop all actions: {e}")

    def _export_timed_actions_config(self):
        """Export timed actions configuration."""
        try:
            from timed_action import export_timed_actions_config
            from tkinter import filedialog

            file_path = filedialog.asksaveasfilename(
                title="Export Timed Actions Configuration",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                if export_timed_actions_config(file_path):
                    self.status_var.set(f"Timed actions exported to {file_path}")
                    show_info_message("Export Success", f"Configuration exported to:\n{file_path}")
                else:
                    show_warning_message("Export Error", "Failed to export configuration")

        except Exception as e:
            logger.error(f"Error exporting timed actions config: {e}")
            show_warning_message("Export Error", f"Failed to export: {e}")

    def _import_timed_actions_config(self):
        """Import timed actions configuration."""
        try:
            from timed_action import import_timed_actions_config
            from tkinter import filedialog

            file_path = filedialog.askopenfilename(
                title="Import Timed Actions Configuration",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                if import_timed_actions_config(file_path):
                    self._refresh_timed_actions_display()
                    self.status_var.set(f"Timed actions imported from {file_path}")
                    show_info_message("Import Success", f"Configuration imported from:\n{file_path}")
                else:
                    show_warning_message("Import Error", "Failed to import configuration")

        except Exception as e:
            logger.error(f"Error importing timed actions config: {e}")
            show_warning_message("Import Error", f"Failed to import: {e}")

    def _show_timed_action_context_menu(self, event):
        """Show context menu for timed actions."""
        try:
            # Select the item under cursor
            item = self.timed_actions_tree.identify_row(event.y)
            if item:
                self.timed_actions_tree.selection_set(item)

                # Create context menu
                context_menu = tk.Menu(self.root, tearoff=0)
                context_menu.add_command(label="Edit Action", command=self._edit_timed_action)
                context_menu.add_command(label="Start Action", command=self._start_timed_action)
                context_menu.add_command(label="Stop Action", command=self._stop_timed_action)
                context_menu.add_separator()
                context_menu.add_command(label="Delete Action", command=self._delete_timed_action)

                # Show menu
                context_menu.post(event.x_root, event.y_root)

                # Clean up menu after use
                def cleanup():
                    try:
                        context_menu.grab_release()
                    except:
                        pass

                self.root.after(100, cleanup)

        except Exception as e:
            logger.error(f"Error showing timed action context menu: {e}")

    def _show_timed_action_dialog(self, action_name=None):
        """Show dialog to add/edit a timed action."""
        try:
            from timed_action import (
                get_timed_action, add_timed_action, update_timed_action,
                TimedAction, parse_interval_string, validate_keys, get_supported_keys
            )

            # Determine if this is edit or add mode
            is_edit = action_name is not None
            action = None

            if is_edit:
                action = get_timed_action(action_name)
                if action is None:
                    show_warning_message("Edit Error", f"Timed action '{action_name}' not found")
                    return

            # Create dialog
            dialog = tk.Toplevel(self.root)
            dialog.title(f"{'Edit' if is_edit else 'Add'} Timed Action")
            dialog.geometry("550x500")  # Made larger to ensure all content fits
            dialog.transient(self.root)
            dialog.grab_set()
            dialog.resizable(False, False)

            # Center dialog
            dialog.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 50,
                self.root.winfo_rooty() + 50
            ))

            # Action name
            ttk.Label(dialog, text="Action Name:", font=('Arial', 10, 'bold')).pack(pady=5)
            name_var = tk.StringVar(value=action_name if is_edit else "")
            name_entry = ttk.Entry(dialog, textvariable=name_var, width=40)
            name_entry.pack(pady=5)
            if not is_edit:
                name_entry.focus_set()

            # Keys to press
            ttk.Label(dialog, text="Keys to Press (comma-separated):", font=('Arial', 10, 'bold')).pack(pady=(10, 5))
            keys_var = tk.StringVar(value=", ".join(action.keys) if is_edit else "")
            keys_entry = ttk.Entry(dialog, textvariable=keys_var, width=40)
            keys_entry.pack(pady=5)

            # Supported keys info
            supported_frame = ttk.Frame(dialog)
            supported_frame.pack(pady=5, fill="x", padx=20)

            ttk.Label(supported_frame, text="Supported keys:", font=('Arial', 9)).pack(anchor="w")
            keys_text = tk.Text(supported_frame, height=4, width=60, font=('Consolas', 8))
            keys_text.pack(fill="x")

            # Insert supported keys
            supported_keys = get_supported_keys()
            keys_display = ", ".join(supported_keys[:50]) + "\n" + ", ".join(supported_keys[50:])
            keys_text.insert('1.0', keys_display)
            keys_text.config(state='disabled')

            # Interval
            ttk.Label(dialog, text="Interval:", font=('Arial', 10, 'bold')).pack(pady=(10, 5))
            interval_frame = ttk.Frame(dialog)
            interval_frame.pack(pady=5)

            interval_var = tk.StringVar(value=action.get_interval_display() if is_edit else "5s")
            interval_entry = ttk.Entry(interval_frame, textvariable=interval_var, width=15)
            interval_entry.pack(side=tk.LEFT, padx=5)

            ttk.Label(interval_frame, text="(e.g., 1000ms, 5s, 2m)").pack(side=tk.LEFT)

            # Enabled checkbox
            enabled_var = tk.BooleanVar(value=action.enabled if is_edit else True)
            ttk.Checkbutton(dialog, text="Enabled", variable=enabled_var).pack(pady=10)

            # Description
            ttk.Label(dialog, text="Description (optional):", font=('Arial', 10, 'bold')).pack(pady=(10, 5))
            desc_var = tk.StringVar(value=action.description if is_edit else "")
            desc_entry = ttk.Entry(dialog, textvariable=desc_var, width=40)
            desc_entry.pack(pady=5)

            # Buttons
            button_frame = ttk.Frame(dialog)
            button_frame.pack(pady=20, padx=20, fill="x")

            def save_action():
                try:
                    # Validate inputs
                    name = name_var.get().strip()
                    if not name:
                        show_warning_message("Validation Error", "Action name is required")
                        return

                    keys_str = keys_var.get().strip()
                    if not keys_str:
                        show_warning_message("Validation Error", "At least one key is required")
                        return

                    # Parse keys
                    keys = [k.strip() for k in keys_str.split(',') if k.strip()]
                    valid_keys, invalid_keys = validate_keys(keys)

                    if invalid_keys:
                        show_warning_message("Invalid Keys",
                            f"The following keys are not supported:\n{', '.join(invalid_keys)}")
                        return

                    # Parse interval
                    interval_ms = parse_interval_string(interval_var.get())

                    if is_edit:
                        # Check if name changed
                        if name != action_name:
                            # Name changed - need to remove old and add new
                            from timed_action import remove_timed_action

                            # Check if new name already exists
                            existing_action = get_timed_action(name)
                            if existing_action:
                                show_warning_message("Name Conflict", f"An action named '{name}' already exists")
                                return

                            # Remove old action and create new one
                            remove_timed_action(action_name)
                            new_action = TimedAction(
                                name=name,
                                keys=valid_keys,
                                interval_ms=interval_ms,
                                enabled=enabled_var.get(),
                                description=desc_var.get().strip()
                            )
                            add_timed_action(new_action)

                            self._refresh_timed_actions_display()
                            dialog.destroy()
                            self.status_var.set(f"Timed action renamed from '{action_name}' to '{name}'")
                            logger.info(f"Renamed timed action: {action_name} -> {name}")
                        else:
                            # Name unchanged - just update
                            success = update_timed_action(
                                action_name,
                                keys=valid_keys,
                                interval_ms=interval_ms,
                                enabled=enabled_var.get(),
                                description=desc_var.get().strip()
                            )

                            if success:
                                self._refresh_timed_actions_display()
                                dialog.destroy()
                                self.status_var.set(f"Timed action '{action_name}' updated")
                                logger.info(f"Updated timed action: {action_name}")
                            else:
                                show_warning_message("Update Error", "Failed to update action")
                    else:
                        # Create new action - check for duplicate names
                        existing_action = get_timed_action(name)
                        if existing_action:
                            show_warning_message("Name Conflict", f"An action named '{name}' already exists")
                            return

                        new_action = TimedAction(
                            name=name,
                            keys=valid_keys,
                            interval_ms=interval_ms,
                            enabled=enabled_var.get(),
                            description=desc_var.get().strip()
                        )

                        add_timed_action(new_action)
                        self._refresh_timed_actions_display()
                        dialog.destroy()
                        self.status_var.set(f"Timed action '{name}' created")
                        logger.info(f"Created timed action: {name}")

                except Exception as e:
                    logger.error(f"Error saving timed action: {e}")
                    show_warning_message("Save Error", f"Failed to save action: {e}")

            # Create buttons
            save_btn = create_styled_button(button_frame, "💾 Save", save_action, self.colors['success'],
                                          font=('Arial', 12, 'bold'), width=18)
            save_btn.pack(side=tk.LEFT, padx=15, pady=15)

            cancel_btn = create_styled_button(button_frame, "❌ Cancel", dialog.destroy, self.colors['danger'],
                                            font=('Arial', 12, 'bold'), width=18)
            cancel_btn.pack(side=tk.LEFT, padx=15, pady=15)

            # Make save button prominent and focused
            save_btn.focus_set()
            save_btn.configure(relief=tk.RAISED, borderwidth=3, height=2)

            # Bind keyboard shortcuts
            dialog.bind('<Return>', lambda e: save_action())
            dialog.bind('<Escape>', lambda e: dialog.destroy())

        except Exception as e:
            logger.error(f"Error showing timed action dialog: {e}")
            show_warning_message("Dialog Error", f"Failed to show dialog: {e}")

    def _show_template_edit_dialog(self, template_name):
        """Show dialog to edit a custom template."""
        try:
            from custom_button_manager import get_custom_template, update_custom_template

            template = get_custom_template(template_name)
            if not template:
                show_warning_message("Edit Error", f"Template '{template_name}' not found")
                return

            # Create edit dialog
            dialog = tk.Toplevel(self.root)
            dialog.title(f"Edit Template: {template_name}")
            dialog.geometry("500x500")  # Made larger to accommodate bigger buttons
            dialog.transient(self.root)
            dialog.grab_set()
            dialog.resizable(False, False)  # Prevent resizing to maintain layout

            # Center dialog
            dialog.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 100,
                self.root.winfo_rooty() + 100
            ))

            # Template name (read-only)
            ttk.Label(dialog, text="Template Name:", font=('Arial', 10, 'bold')).pack(pady=5)
            ttk.Label(dialog, text=template_name, font=('Arial', 10)).pack(pady=5)

            # Enabled checkbox
            enabled_var = tk.BooleanVar(value=template.enabled)
            ttk.Checkbutton(dialog, text="Enabled", variable=enabled_var).pack(pady=5)

            # Auto-click checkbox
            auto_click_var = tk.BooleanVar(value=template.auto_click)
            ttk.Checkbutton(dialog, text="Auto-click when detected", variable=auto_click_var).pack(pady=5)

            # Confidence threshold
            ttk.Label(dialog, text="Confidence Threshold:").pack(pady=5)
            confidence_var = tk.DoubleVar(value=template.confidence)
            confidence_frame = ttk.Frame(dialog)
            confidence_frame.pack(pady=5)
            ttk.Spinbox(confidence_frame, from_=0.1, to=1.0, increment=0.05,
                       textvariable=confidence_var, width=10).pack(side=tk.LEFT)
            ttk.Label(confidence_frame, text="(0.1 - 1.0)").pack(side=tk.LEFT, padx=5)

            # Cooldown
            ttk.Label(dialog, text="Cooldown (seconds):").pack(pady=5)
            cooldown_var = tk.DoubleVar(value=template.cooldown)
            cooldown_frame = ttk.Frame(dialog)
            cooldown_frame.pack(pady=5)
            ttk.Spinbox(cooldown_frame, from_=0.1, to=60.0, increment=0.5,
                       textvariable=cooldown_var, width=10).pack(side=tk.LEFT)
            ttk.Label(cooldown_frame, text="seconds").pack(side=tk.LEFT, padx=5)

            # Click offset
            ttk.Label(dialog, text="Click Offset (pixels):").pack(pady=5)
            offset_frame = ttk.Frame(dialog)
            offset_frame.pack(pady=5)

            ttk.Label(offset_frame, text="X:").pack(side=tk.LEFT)
            offset_x_var = tk.IntVar(value=template.click_offset_x)
            ttk.Spinbox(offset_frame, from_=-100, to=100, textvariable=offset_x_var, width=8).pack(side=tk.LEFT, padx=5)

            ttk.Label(offset_frame, text="Y:").pack(side=tk.LEFT, padx=(10, 0))
            offset_y_var = tk.IntVar(value=template.click_offset_y)
            ttk.Spinbox(offset_frame, from_=-100, to=100, textvariable=offset_y_var, width=8).pack(side=tk.LEFT, padx=5)

            # Statistics
            stats_frame = ttk.LabelFrame(dialog, text="Statistics", padding=5)
            stats_frame.pack(pady=10, padx=10, fill="x")

            stats_text = (
                f"Detections: {template.detection_count}\n"
                f"Last Detection: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(template.last_detection_time)) if template.last_detection_time > 0 else 'Never'}\n"
                f"Created: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(template.created_time))}"
            )
            ttk.Label(stats_frame, text=stats_text, font=('Consolas', 8)).pack()

            # Buttons
            button_frame = ttk.Frame(dialog)
            button_frame.pack(pady=20, padx=20, fill="x")

            def save_changes():
                try:
                    # Update template
                    success = update_custom_template(
                        template_name,
                        enabled=enabled_var.get(),
                        auto_click=auto_click_var.get(),
                        confidence=confidence_var.get(),
                        cooldown=cooldown_var.get(),
                        click_offset_x=offset_x_var.get(),
                        click_offset_y=offset_y_var.get()
                    )

                    if success:
                        self._refresh_templates_display()
                        dialog.destroy()
                        self.status_var.set(f"Template '{template_name}' updated")
                        logger.info(f"Updated custom template: {template_name}")
                    else:
                        show_warning_message("Save Error", "Failed to update template")

                except Exception as e:
                    logger.error(f"Error saving template changes: {e}")
                    show_warning_message("Save Error", f"Failed to save changes: {e}")

            def test_template():
                try:
                    from custom_button_manager import test_template_detection_once
                    result = test_template_detection_once(template_name)
                    if result:
                        show_info_message("Test Result", f"✅ Template '{template_name}' detected successfully!")
                    else:
                        show_info_message("Test Result", f"❌ Template '{template_name}' not detected")
                except Exception as e:
                    logger.error(f"Error testing template: {e}")
                    show_warning_message("Test Error", f"Failed to test template: {e}")

            # Create buttons with better spacing and visibility
            save_btn = create_styled_button(button_frame, "💾 Save", save_changes, self.colors['success'],
                                          font=('Arial', 12, 'bold'), width=15)
            save_btn.pack(side=tk.LEFT, padx=10, pady=10)

            test_btn = create_styled_button(button_frame, "🔍 Test", test_template, self.colors['warning'],
                                          font=('Arial', 12, 'bold'), width=15)
            test_btn.pack(side=tk.LEFT, padx=10, pady=10)

            cancel_btn = create_styled_button(button_frame, "❌ Cancel", dialog.destroy, self.colors['danger'],
                                            font=('Arial', 12, 'bold'), width=15)
            cancel_btn.pack(side=tk.LEFT, padx=10, pady=10)

            # Make sure the save button is focused and prominent
            save_btn.focus_set()
            save_btn.configure(relief=tk.RAISED, borderwidth=3, height=2)

            # Bind Enter key to save
            dialog.bind('<Return>', lambda e: save_changes())
            dialog.bind('<Escape>', lambda e: dialog.destroy())

        except Exception as e:
            logger.error(f"Error showing template edit dialog: {e}")
            show_warning_message("Dialog Error", f"Failed to show edit dialog: {e}")

    def _create_custom_button_tab(self):
        """Create the custom button management tab."""
        custom_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(custom_frame, text="Custom Buttons")

        # Custom button detection settings
        settings_frame = ttk.LabelFrame(custom_frame, text="Custom Button Detection Settings", padding=10)
        settings_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))
        custom_frame.columnconfigure(0, weight=1)

        # Enable custom button detection
        self.custom_button_enabled_var = tk.BooleanVar()
        custom_check = ttk.Checkbutton(settings_frame, text="Enable Custom Button Detection",
                                     variable=self.custom_button_enabled_var,
                                     command=self._toggle_custom_button_detection)
        custom_check.grid(row=0, column=0, sticky="w", padx=5, pady=5)

        # Check interval
        ttk.Label(settings_frame, text="Check Interval (seconds):").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.custom_check_interval_var = tk.DoubleVar(value=1.0)
        custom_interval_spinbox = ttk.Spinbox(settings_frame, from_=0.5, to=10, increment=0.5,
                                            textvariable=self.custom_check_interval_var, width=10)
        custom_interval_spinbox.grid(row=1, column=1, sticky="w", padx=5, pady=5)

        # Global cooldown
        ttk.Label(settings_frame, text="Global Cooldown (seconds):").grid(row=2, column=0, sticky="w", padx=5, pady=5)
        self.custom_cooldown_var = tk.DoubleVar(value=0.5)
        cooldown_spinbox = ttk.Spinbox(settings_frame, from_=0.1, to=5.0, increment=0.1,
                                     textvariable=self.custom_cooldown_var, width=10)
        cooldown_spinbox.grid(row=2, column=1, sticky="w", padx=5, pady=5)

        # Templates management section
        self._create_custom_templates_section(custom_frame)

    def _create_custom_templates_section(self, parent):
        """Create the custom templates management section."""
        # Templates section
        templates_frame = ttk.LabelFrame(parent, text="Custom Templates", padding=10)
        templates_frame.grid(row=1, column=0, sticky="nsew", pady=10)
        parent.rowconfigure(1, weight=1)
        templates_frame.columnconfigure(0, weight=1)

        # Templates list with scrollbar
        list_frame = ttk.Frame(templates_frame)
        list_frame.grid(row=0, column=0, sticky="nsew", pady=(0, 10))
        templates_frame.rowconfigure(0, weight=1)

        # Create treeview for templates
        columns = ('Name', 'Enabled', 'Confidence', 'Auto-Click', 'Detections')
        self.templates_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)

        # Configure columns
        self.templates_tree.heading('Name', text='Template Name')
        self.templates_tree.heading('Enabled', text='Enabled')
        self.templates_tree.heading('Confidence', text='Confidence')
        self.templates_tree.heading('Auto-Click', text='Auto-Click')
        self.templates_tree.heading('Detections', text='Detections')

        self.templates_tree.column('Name', width=150)
        self.templates_tree.column('Enabled', width=80)
        self.templates_tree.column('Confidence', width=100)
        self.templates_tree.column('Auto-Click', width=100)
        self.templates_tree.column('Detections', width=100)

        self.templates_tree.grid(row=0, column=0, sticky="nsew")

        # Scrollbar for treeview
        tree_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.templates_tree.yview)
        tree_scrollbar.grid(row=0, column=1, sticky="ns")
        self.templates_tree.configure(yscrollcommand=tree_scrollbar.set)

        list_frame.rowconfigure(0, weight=1)
        list_frame.columnconfigure(0, weight=1)

        # Bind events
        self.templates_tree.bind("<Double-1>", self._edit_custom_template)
        self.templates_tree.bind("<Button-3>", self._show_template_context_menu)

        # Template management buttons
        self._create_template_management_buttons(templates_frame)

    def _create_template_management_buttons(self, parent):
        """Create template management buttons."""
        # Button frame
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=1, column=0, sticky="ew", pady=(10, 0))

        # Create buttons in a grid layout
        buttons_config = [
            # Row 0
            [
                ("📸 Capture Template", self._capture_custom_template, self.colors.get('accent', '#3498db')),
                ("📁 Add Template", self._add_custom_template, self.colors.get('success', '#27ae60')),
                ("✏️ Edit Template", self._edit_custom_template, self.colors.get('accent', '#3498db')),
                ("🗑️ Delete Template", self._delete_custom_template, self.colors.get('danger', '#e74c3c'))
            ],
            # Row 1
            [
                ("🔍 Test Template", self._test_custom_template, self.colors.get('warning', '#f39c12')),
                ("✅ Enable All", self._enable_all_templates, self.colors.get('success', '#27ae60')),
                ("❌ Disable All", self._disable_all_templates, self.colors.get('info', '#3498db')),
                ("📤 Export Config", self._export_templates_config, self.colors.get('accent', '#3498db'))
            ],
            # Row 2
            [
                ("📥 Import Config", self._import_templates_config, self.colors.get('info', '#3498db')),
                ("📊 Reset Stats", self._reset_template_stats, self.colors.get('warning', '#f39c12')),
                ("", None, None),  # Empty slot
                ("", None, None)   # Empty slot
            ]
        ]

        for row_idx, button_row in enumerate(buttons_config):
            for col_idx, (text, command, color) in enumerate(button_row):
                try:
                    if text and command:  # Skip empty slots
                        btn = create_styled_button(button_frame, text, command, color)
                        btn.grid(row=row_idx, column=col_idx, padx=5, pady=5, sticky="ew")
                    button_frame.columnconfigure(col_idx, weight=1)
                except Exception as e:
                    logger.error(f"Error creating template button '{text}': {e}")

        # Template statistics frame
        stats_frame = ttk.LabelFrame(parent, text="Statistics", padding=5)
        stats_frame.grid(row=2, column=0, sticky="ew", pady=(10, 0))

        # Statistics display
        self.template_stats_var = tk.StringVar(value="No templates loaded")
        stats_label = ttk.Label(stats_frame, textvariable=self.template_stats_var,
                              font=('Consolas', 9))
        stats_label.grid(row=0, column=0, sticky="w")

        # Refresh templates display
        self._refresh_templates_display()

    def _create_timed_action_tab(self):
        """Create the timed action tab."""
        timed_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(timed_frame, text="Timed Actions")

        # Timed action settings
        settings_frame = ttk.LabelFrame(timed_frame, text="Timed Action Settings", padding=10)
        settings_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))
        timed_frame.columnconfigure(0, weight=1)

        # Enable timed actions
        self.timed_actions_enabled_var = tk.BooleanVar()
        timed_check = ttk.Checkbutton(settings_frame, text="Enable Timed Actions System",
                                    variable=self.timed_actions_enabled_var,
                                    command=self._toggle_timed_actions_system)
        timed_check.grid(row=0, column=0, sticky="w", padx=5, pady=5)

        # Max concurrent actions
        ttk.Label(settings_frame, text="Max Concurrent Actions:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.max_concurrent_actions_var = tk.IntVar(value=10)
        concurrent_spinbox = ttk.Spinbox(settings_frame, from_=1, to=20,
                                       textvariable=self.max_concurrent_actions_var, width=10)
        concurrent_spinbox.grid(row=1, column=1, sticky="w", padx=5, pady=5)

        # Timed actions management section
        self._create_timed_actions_section(timed_frame)

    def _create_timed_actions_section(self, parent):
        """Create the timed actions management section."""
        # Actions section
        actions_frame = ttk.LabelFrame(parent, text="Timed Actions", padding=10)
        actions_frame.grid(row=1, column=0, sticky="nsew", pady=10)
        parent.rowconfigure(1, weight=1)
        actions_frame.columnconfigure(0, weight=1)

        # Actions list with scrollbar
        list_frame = ttk.Frame(actions_frame)
        list_frame.grid(row=0, column=0, sticky="nsew", pady=(0, 10))
        actions_frame.rowconfigure(0, weight=1)

        # Create treeview for actions
        columns = ('Name', 'Keys', 'Interval', 'Status', 'Executions')
        self.timed_actions_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)

        # Configure columns
        self.timed_actions_tree.heading('Name', text='Action Name')
        self.timed_actions_tree.heading('Keys', text='Keys')
        self.timed_actions_tree.heading('Interval', text='Interval')
        self.timed_actions_tree.heading('Status', text='Status')
        self.timed_actions_tree.heading('Executions', text='Executions')

        self.timed_actions_tree.column('Name', width=120)
        self.timed_actions_tree.column('Keys', width=100)
        self.timed_actions_tree.column('Interval', width=80)
        self.timed_actions_tree.column('Status', width=80)
        self.timed_actions_tree.column('Executions', width=80)

        self.timed_actions_tree.grid(row=0, column=0, sticky="nsew")

        # Scrollbar for treeview
        actions_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.timed_actions_tree.yview)
        actions_scrollbar.grid(row=0, column=1, sticky="ns")
        self.timed_actions_tree.configure(yscrollcommand=actions_scrollbar.set)

        list_frame.rowconfigure(0, weight=1)
        list_frame.columnconfigure(0, weight=1)

        # Bind events
        self.timed_actions_tree.bind("<Double-1>", self._edit_timed_action)
        self.timed_actions_tree.bind("<Button-3>", self._show_timed_action_context_menu)

        # Action management buttons
        self._create_timed_action_buttons(actions_frame)

    def _create_timed_action_buttons(self, parent):
        """Create timed action management buttons."""
        # Button frame
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=1, column=0, sticky="ew", pady=(10, 0))

        # Create buttons in a grid layout
        buttons_config = [
            # Row 0
            [
                ("➕ Add Action", self._add_timed_action, self.colors.get('success', '#27ae60')),
                ("✏️ Edit Action", self._edit_timed_action, self.colors.get('accent', '#3498db')),
                ("🗑️ Delete Action", self._delete_timed_action, self.colors.get('danger', '#e74c3c')),
                ("▶️ Start Action", self._start_timed_action, self.colors.get('success', '#27ae60'))
            ],
            # Row 1
            [
                ("⏹️ Stop Action", self._stop_timed_action, self.colors.get('warning', '#f39c12')),
                ("⏹️ Stop All", self._stop_all_timed_actions, self.colors.get('danger', '#e74c3c')),
                ("📤 Export Config", self._export_timed_actions_config, self.colors.get('accent', '#3498db')),
                ("📥 Import Config", self._import_timed_actions_config, self.colors.get('info', '#3498db'))
            ]
        ]

        for row_idx, button_row in enumerate(buttons_config):
            for col_idx, (text, command, color) in enumerate(button_row):
                try:
                    if text and command:  # Skip empty slots
                        btn = create_styled_button(button_frame, text, command, color,
                                                 font=('Arial', 10, 'bold'), width=15)
                        btn.grid(row=row_idx, column=col_idx, padx=5, pady=5, sticky="ew")
                    button_frame.columnconfigure(col_idx, weight=1)
                except Exception as e:
                    logger.error(f"Error creating timed action button '{text}': {e}")

        # Action statistics frame
        stats_frame = ttk.LabelFrame(parent, text="Statistics", padding=5)
        stats_frame.grid(row=2, column=0, sticky="ew", pady=(10, 0))

        # Statistics display
        self.timed_action_stats_var = tk.StringVar(value="No actions loaded")
        stats_label = ttk.Label(stats_frame, textvariable=self.timed_action_stats_var,
                              font=('Consolas', 9))
        stats_label.grid(row=0, column=0, sticky="w")

        # Refresh actions display
        self._refresh_timed_actions_display()

    def _create_settings_tab(self):
        """Create the settings tab."""
        settings_frame = ttk.Frame(self.notebook, padding=20)
        self.notebook.add(settings_frame, text="Settings")
        
        # Game window settings
        game_frame = ttk.LabelFrame(settings_frame, text="Game Window Settings", padding=10)
        game_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))
        settings_frame.columnconfigure(0, weight=1)
        
        # Target game only
        self.target_game_var = tk.BooleanVar(value=game_window_settings['target_game_only'])
        target_check = ttk.Checkbutton(game_frame, text="Target Game Window Only", 
                                      variable=self.target_game_var, command=self._update_game_settings)
        target_check.grid(row=0, column=0, sticky="w", padx=5, pady=5)
        
        # Window title
        ttk.Label(game_frame, text="Game Window Title:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.window_title_var = tk.StringVar(value=game_window_settings['window_title'])
        title_entry = ttk.Entry(game_frame, textvariable=self.window_title_var, width=30)
        title_entry.grid(row=1, column=1, sticky="w", padx=5, pady=5)
        title_entry.bind('<KeyRelease>', lambda e: self._update_game_settings())

        # Game window control buttons
        button_frame = ttk.Frame(game_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10, padx=5)

        refresh_btn = create_styled_button(button_frame, "🔄 Refresh",
                                         self._refresh_game_window, self.colors.get('info', '#3498db'),
                                         font=('Arial', 10, 'bold'), width=12)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        detect_btn = create_styled_button(button_frame, "🔍 Detect Games",
                                        self._detect_game_windows, self.colors.get('accent', '#3498db'),
                                        font=('Arial', 10, 'bold'), width=12)
        detect_btn.pack(side=tk.LEFT, padx=5)

        # Current game window status
        self.game_window_status_var = tk.StringVar(value="No game window detected")
        status_label = ttk.Label(game_frame, textvariable=self.game_window_status_var,
                               font=('Arial', 9), foreground='gray')
        status_label.grid(row=3, column=0, columnspan=2, sticky="w", padx=5, pady=5)

        # Update initial status
        self._update_game_window_status()

    def _create_status_bar(self, parent):
        """Create the status bar."""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, sticky="ew", pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, relief="sunken", padding=5)
        status_label.grid(row=0, column=0, sticky="ew")

    def _setup_hooks(self):
        """Set up keyboard and mouse hooks for recording."""
        if HAVE_KEYBOARD:
            try:
                # Unregister any existing hotkeys to avoid duplicates
                keyboard.unhook_all()

                # Register hotkeys
                keyboard.add_hotkey('f8', self._toggle_recording)
                keyboard.add_hotkey('f9', self._toggle_playback)
                keyboard.add_hotkey('f10', self._toggle_minimalist_mode)
                keyboard.add_hotkey('f12', self._toggle_death_detection)
                keyboard.add_hotkey('f3', self._test_all_custom_templates)

                logger.info("Keyboard hotkeys registered successfully")
            except Exception as e:
                logger.error(f"Error setting up keyboard hooks: {e}")
                show_warning_message("Warning", f"Failed to set up keyboard hooks: {e}")

        # Create the overlay for mouse recording when needed
        self.overlay = None

    @property
    def recording(self):
        """Get the current recording state."""
        global recording
        return recording

    def _toggle_recording(self):
        """Toggle recording state."""
        global recording

        try:
            # Check current recording state from recording manager
            if self.recording_manager.is_recording:
                success = self.recording_manager.stop_recording()
                if success:
                    self.recording_var.set("Not Recording")
                    self.record_button.config(text="Start Recording (F8)")
                    recording = False
            else:
                success = self.recording_manager.start_recording()
                if success:
                    self.recording_var.set("Recording...")
                    self.record_button.config(text="Stop Recording (F8)")
                    recording = True

        except Exception as e:
            logger.error(f"Error toggling recording: {e}")
            show_warning_message("Recording Error", f"Failed to toggle recording: {e}")

    def _on_key_press(self, event):
        """Handle key press events during recording."""
        global recording, last_action_time

        # Ignore hotkeys
        if event.name in ['f8', 'f9', 'f10', 'f12']:
            return

        if recording:
            try:
                # Record the key press
                if record_key_press(event.name):
                    # Get updated last action time
                    last_action_time = get_last_action_time()

                    # Update the actions list in the UI
                    self._update_actions_list()

                    # Update status
                    self.status_var.set(f"Recorded key press: {event.name}")
                else:
                    logger.warning(f"Failed to record key press: {event.name}")
            except Exception as e:
                logger.error(f"Error handling key press: {e}")

    def _on_mouse_click(self, x, y, button):
        """Handle mouse click events during recording."""
        global recording, last_action_time

        if recording:
            try:
                # Map button to our format
                button_name = button.name if hasattr(button, 'name') else str(button)
                if button_name not in ['left', 'right', 'middle']:
                    button_name = 'left'  # Default to left click

                # Record the mouse click
                if record_mouse_click(button_name, x, y):
                    # Get updated last action time
                    last_action_time = get_last_action_time()

                    # Update the actions list in the UI
                    self._update_actions_list()

                    # Update status
                    self.status_var.set(f"Recorded {button_name} click at ({x}, {y})")

                    # Show visual feedback
                    if self.overlay:
                        show_click_feedback(self.overlay, x, y)
                else:
                    logger.warning(f"Failed to record mouse click: {button_name} at ({x}, {y})")
            except Exception as e:
                logger.error(f"Error handling mouse click: {e}")

    def _toggle_playback(self):
        """Toggle playback state."""
        global running, playback_thread

        if running and playback_thread and playback_thread.is_alive():
            # Stop playback
            self._stop_playback()
        else:
            # Start playback
            infinite = self.infinite_var.get()
            repeat_count = self.repeat_count_var.get()
            repeat_delay = self.repeat_delay_var.get()

            # Get actions from recording manager
            actions = self.recording_manager.recorded_actions
            if not actions:
                show_warning_message("Warning", "No actions recorded")
                return

            running = True
            # Update recorder state before starting playback
            update_recorder_state()

            playback_thread = threading.Thread(
                target=playback_actions,
                args=(repeat_count, repeat_delay, infinite)
            )

            playback_thread.daemon = True
            playback_thread.start()

            self.playback_button.config(text="Stop Playback")
            self.status_var.set("Playback started...")

            # Check playback status
            self.root.after(100, self._check_playback)

    def _stop_playback(self):
        """Stop playback."""
        global running

        running = False
        # Update recorder state
        update_recorder_state()
        self.playback_button.config(text="Start Playback (F9)")
        self.status_var.set("Playback stopped")

    def _check_playback(self):
        """Check if playback is still running."""
        global running, playback_thread

        if running and playback_thread and playback_thread.is_alive():
            # Still running, check again later
            self.root.after(100, self._check_playback)
        else:
            # Playback finished
            running = False
            # Update recorder state
            update_recorder_state()
            self.playback_button.config(text="Start Playback (F9)")
            self.status_var.set("Playback completed")

    def _toggle_death_detection(self):
        """Toggle death detection."""
        # Check current state and toggle
        current_state = is_death_detection_active()

        if current_state:
            # Currently active, so disable it
            if stop_death_detection():
                self.death_enabled_var.set(False)
                self.status_var.set("Death detection disabled (F12)")
                logger.info("Death detection stopped via hotkey")

                # Update minimalist mode if visible
                if self.minimalist_mode and self.minimalist_window.is_window_visible():
                    self.minimalist_window.force_refresh()
            else:
                self.status_var.set("Failed to stop death detection")
        else:
            # Currently inactive, so enable it
            # Update settings from UI (use current UI values or defaults)
            try:
                auto_click = self.auto_click_var.get()
                confidence = self.confidence_var.get() / 100.0
                check_interval = self.check_interval_var.get()
            except:
                # Fallback to defaults if UI variables not available
                auto_click = True
                confidence = 0.85
                check_interval = 2.0

            update_death_detection_settings(
                enabled=True,
                auto_click_revival=auto_click,
                confidence=confidence,
                check_interval=check_interval
            )

            if start_death_detection():
                self.death_enabled_var.set(True)
                self.status_var.set("Death detection enabled (F12)")
                logger.info("Death detection started via hotkey")

                # Update minimalist mode if visible
                if self.minimalist_mode and self.minimalist_window.is_window_visible():
                    self.minimalist_window.force_refresh()
            else:
                self.status_var.set("Failed to start death detection")

    def _update_game_settings(self):
        """Update game window settings."""
        game_window_settings['target_game_only'] = self.target_game_var.get()
        game_window_settings['window_title'] = self.window_title_var.get()
        # Update status when settings change
        self._update_game_window_status()

    def _get_window_title(self, hwnd):
        """Get the title of a window by handle."""
        try:
            import win32gui
            return win32gui.GetWindowText(hwnd)
        except:
            return None

    def _refresh_game_window(self):
        """Refresh the game window selection."""
        try:
            from win_api import find_game_window

            # Clear current handle to force re-detection
            import win_api
            win_api.game_window_handle = None

            # Attempt to find the game window
            if find_game_window():
                # Get the actual window title
                actual_title = self._get_window_title(win_api.game_window_handle)
                if actual_title:
                    # Update the title in settings if we found a window
                    self.window_title_var.set(actual_title)
                    game_window_settings['window_title'] = actual_title

                self.status_var.set("Game window refreshed successfully")
                show_info_message("Game Window Refresh",
                    f"Game window found and refreshed!\n\n"
                    f"Window Title: {actual_title or 'Unknown'}\n"
                    f"Handle: {win_api.game_window_handle}")
                logger.info(f"Game window refreshed: {actual_title}")
            else:
                self.status_var.set("No game window found")
                show_warning_message("Game Window Refresh",
                    "No game window found.\n\n"
                    "Make sure your game is running and try again.\n"
                    "You can also manually enter the window title.")
                logger.warning("Game window refresh failed - no window found")

            # Update status display
            self._update_game_window_status()

        except Exception as e:
            logger.error(f"Error refreshing game window: {e}")
            self.status_var.set("Error refreshing game window")
            show_warning_message("Refresh Error", f"Failed to refresh game window: {e}")

    def _update_game_window_status(self):
        """Update the game window status display."""
        try:
            from win_api import game_window_handle

            if game_window_handle:
                try:
                    # Try to get the window title to verify the handle is still valid
                    title = self._get_window_title(game_window_handle)
                    if title:
                        self.game_window_status_var.set(f"✅ Connected to: {title}")
                    else:
                        self.game_window_status_var.set("⚠️ Game window handle may be invalid")
                except:
                    self.game_window_status_var.set("❌ Game window handle is invalid")
            else:
                if game_window_settings['target_game_only']:
                    self.game_window_status_var.set("❌ No game window detected")
                else:
                    self.game_window_status_var.set("ℹ️ Global mode (not targeting specific window)")

        except Exception as e:
            logger.error(f"Error updating game window status: {e}")
            self.game_window_status_var.set("❌ Error checking game window status")

    def _detect_game_windows(self):
        """Detect and show available game windows."""
        try:
            import win32gui

            # List to store detected windows
            windows = []

            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    title = self._get_window_title(hwnd)
                    if title and len(title.strip()) > 0:
                        # Filter out system windows and empty titles
                        if not any(sys_word in title.lower() for sys_word in
                                 ['program manager', 'desktop', 'taskbar', 'start menu', 'cortana']):
                            windows.append((hwnd, title))
                return True

            # Enumerate all windows
            win32gui.EnumWindows(enum_windows_callback, windows)

            if not windows:
                show_info_message("Game Detection", "No suitable windows found.")
                return

            # Create selection dialog
            self._show_window_selection_dialog(windows)

        except Exception as e:
            logger.error(f"Error detecting game windows: {e}")
            show_warning_message("Detection Error", f"Failed to detect windows: {e}")

    def _show_window_selection_dialog(self, windows):
        """Show dialog to select from detected windows."""
        try:
            # Create dialog
            dialog = tk.Toplevel(self.root)
            dialog.title("Select Game Window")
            dialog.geometry("600x400")
            dialog.transient(self.root)
            dialog.grab_set()
            dialog.resizable(True, True)

            # Center dialog
            dialog.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 50,
                self.root.winfo_rooty() + 50
            ))

            # Instructions
            ttk.Label(dialog, text="Select your game window from the list below:",
                     font=('Arial', 12, 'bold')).pack(pady=10)

            # Create listbox with scrollbar
            list_frame = ttk.Frame(dialog)
            list_frame.pack(fill="both", expand=True, padx=20, pady=10)

            # Listbox
            listbox = tk.Listbox(list_frame, font=('Arial', 10))
            listbox.pack(side="left", fill="both", expand=True)

            # Scrollbar
            scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=listbox.yview)
            scrollbar.pack(side="right", fill="y")
            listbox.configure(yscrollcommand=scrollbar.set)

            # Populate listbox
            for hwnd, title in windows:
                listbox.insert(tk.END, f"{title} (Handle: {hwnd})")

            # Buttons
            button_frame = ttk.Frame(dialog)
            button_frame.pack(pady=20)

            def select_window():
                selection = listbox.curselection()
                if not selection:
                    show_warning_message("No Selection", "Please select a window from the list.")
                    return

                # Get selected window
                selected_index = selection[0]
                hwnd, title = windows[selected_index]

                # Update settings
                self.window_title_var.set(title)
                game_window_settings['window_title'] = title

                # Update win_api handle
                import win_api
                win_api.game_window_handle = hwnd

                # Update status
                self._update_game_window_status()

                # Close dialog
                dialog.destroy()

                # Show confirmation
                self.status_var.set(f"Game window set to: {title}")
                show_info_message("Window Selected",
                    f"Game window successfully set to:\n\n{title}\n\nHandle: {hwnd}")
                logger.info(f"Game window manually selected: {title} (Handle: {hwnd})")

            def refresh_list():
                # Clear current list
                listbox.delete(0, tk.END)

                # Re-detect windows
                new_windows = []
                def enum_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        title = self._get_window_title(hwnd)
                        if title and len(title.strip()) > 0:
                            if not any(sys_word in title.lower() for sys_word in
                                     ['program manager', 'desktop', 'taskbar', 'start menu', 'cortana']):
                                windows.append((hwnd, title))
                    return True

                import win32gui
                win32gui.EnumWindows(enum_callback, new_windows)

                # Update the windows list
                windows.clear()
                windows.extend(new_windows)

                # Repopulate listbox
                for hwnd, title in windows:
                    listbox.insert(tk.END, f"{title} (Handle: {hwnd})")

            select_btn = create_styled_button(button_frame, "✅ Select Window", select_window,
                                            self.colors['success'], font=('Arial', 11, 'bold'), width=15)
            select_btn.pack(side=tk.LEFT, padx=10)

            refresh_btn = create_styled_button(button_frame, "🔄 Refresh List", refresh_list,
                                             self.colors['info'], font=('Arial', 11, 'bold'), width=15)
            refresh_btn.pack(side=tk.LEFT, padx=10)

            cancel_btn = create_styled_button(button_frame, "❌ Cancel", dialog.destroy,
                                            self.colors['danger'], font=('Arial', 11, 'bold'), width=15)
            cancel_btn.pack(side=tk.LEFT, padx=10)

            # Bind double-click to select
            listbox.bind('<Double-1>', lambda e: select_window())

            # Bind escape to cancel
            dialog.bind('<Escape>', lambda e: dialog.destroy())

        except Exception as e:
            logger.error(f"Error showing window selection dialog: {e}")
            show_warning_message("Dialog Error", f"Failed to show selection dialog: {e}")

    def _toggle_minimalist_mode(self):
        """Toggle minimalist mode."""
        self.minimalist_mode = not self.minimalist_mode

        if self.minimalist_mode:
            # Switch to minimalist mode
            self.minimalist_window.show_window()
            self.root.withdraw()  # Hide main window
            self.status_var.set("Switched to minimalist mode")
            logger.info("Switched to minimalist mode")
        else:
            # Switch back to full mode
            self.minimalist_window.hide_window()
            self.root.deiconify()  # Show main window
            self.root.lift()  # Bring to front
            self.status_var.set("Switched to full mode")
            logger.info("Switched to full mode")

    def _update_actions_list(self):
        """Update the actions list display using recording manager."""
        try:
            self.recording_manager._update_actions_list()
        except Exception as e:
            logger.error(f"Error updating actions list: {e}")

    def _on_action_select(self, event):
        """Handle action selection to show details using recording manager."""
        try:
            self.recording_manager._on_action_select(event)
        except Exception as e:
            logger.error(f"Error handling action selection: {e}")
            self.action_info_var.set("Error displaying action details")

    # _format_action_details removed - now handled by recording manager

    def _edit_selected_action(self, event=None):
        """Edit the selected action using recording manager."""
        try:
            self.recording_manager.edit_selected_action()
        except Exception as e:
            logger.error(f"Error editing action: {e}")
            show_warning_message("Edit Error", f"Failed to edit action: {e}")

    # _edit_selected_action_dialog removed - now handled by recording manager

    def _show_action_context_menu(self, event):
        """Show context menu for actions."""
        # Create context menu
        context_menu = tk.Menu(self.root, tearoff=0)

        selected = self.actions_listbox.curselection()
        if selected:
            context_menu.add_command(label="Edit Action", command=self._edit_selected_action)
            context_menu.add_command(label="Delete Action", command=self._delete_selected_action)
            context_menu.add_command(label="Duplicate Action", command=self._duplicate_selected_action)
            context_menu.add_separator()
            context_menu.add_command(label="Move Up", command=self._move_action_up)
            context_menu.add_command(label="Move Down", command=self._move_action_down)
            context_menu.add_separator()
            context_menu.add_command(label="Insert Delay Before", command=self._insert_delay_action)

        context_menu.add_separator()
        context_menu.add_command(label="Import Actions", command=self._import_actions)
        context_menu.add_command(label="Export Actions", command=self._export_actions)
        context_menu.add_separator()
        context_menu.add_command(label="Save as Macro", command=self._save_as_macro)
        context_menu.add_command(label="Load Macro", command=self._load_macro)
        context_menu.add_separator()
        context_menu.add_command(label="Clear All Actions", command=self._clear_all_actions)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def _delete_selected_action(self):
        """Delete the selected action using recording manager."""
        try:
            self.recording_manager.delete_selected_action()
        except Exception as e:
            logger.error(f"Error deleting action: {e}")
            show_warning_message("Delete Error", f"Failed to delete action: {e}")

    def _duplicate_selected_action(self):
        """Duplicate the selected action using recording manager."""
        try:
            self.recording_manager.duplicate_selected_action()
        except Exception as e:
            logger.error(f"Error duplicating action: {e}")
            show_warning_message("Duplicate Error", f"Failed to duplicate action: {e}")

    def _move_action_up(self):
        """Move the selected action up using recording manager."""
        try:
            self.recording_manager.move_action_up()
        except Exception as e:
            logger.error(f"Error moving action up: {e}")
            show_warning_message("Move Error", f"Failed to move action: {e}")

    def _move_action_down(self):
        """Move the selected action down using recording manager."""
        try:
            self.recording_manager.move_action_down()
        except Exception as e:
            logger.error(f"Error moving action down: {e}")
            show_warning_message("Move Error", f"Failed to move action: {e}")

    def _insert_delay_action(self):
        """Insert a delay action using recording manager."""
        try:
            self.recording_manager.insert_delay_action()
        except Exception as e:
            logger.error(f"Error inserting delay: {e}")
            show_warning_message("Insert Error", f"Failed to insert delay: {e}")

    def _clear_all_actions(self):
        """Clear all recorded actions using recording manager."""
        try:
            self.recording_manager.clear_all_actions()
        except Exception as e:
            logger.error(f"Error clearing actions: {e}")
            show_warning_message("Clear Error", f"Failed to clear actions: {e}")

    def _export_actions(self):
        """Export actions using recording manager."""
        try:
            self.recording_manager.export_actions('json')
        except Exception as e:
            logger.error(f"Error exporting actions: {e}")
            show_warning_message("Export Error", f"Failed to export actions: {e}")

    def _export_actions_json(self):
        """Export actions to JSON using recording manager."""
        try:
            self.recording_manager.export_actions('json')
        except Exception as e:
            logger.error(f"Error exporting actions: {e}")
            show_warning_message("Export Error", f"Failed to export actions: {e}")

    def _export_actions_text(self):
        """Export actions to text using recording manager."""
        try:
            self.recording_manager.export_actions('text')
        except Exception as e:
            logger.error(f"Error exporting actions: {e}")
            show_warning_message("Export Error", f"Failed to export actions: {e}")

    # Duplicated export functions removed - now handled by recording manager

    def _import_actions(self):
        """Import actions using recording manager."""
        try:
            self.recording_manager.import_actions()
        except Exception as e:
            logger.error(f"Error importing actions: {e}")
            show_warning_message("Import Error", f"Failed to import actions: {e}")

    def _save_as_macro(self):
        """Save macro using recording manager."""
        try:
            self.recording_manager.save_as_macro()
        except Exception as e:
            logger.error(f"Error saving macro: {e}")
            show_warning_message("Save Error", f"Failed to save macro: {e}")

    def _load_macro(self):
        """Load macro using recording manager."""
        try:
            self.recording_manager.load_macro()
        except Exception as e:
            logger.error(f"Error loading macro: {e}")
            show_warning_message("Load Error", f"Failed to load macro: {e}")

    def _quick_save_actions(self):
        """Quick save actions using recording manager."""
        try:
            self.recording_manager.quick_save_actions()
        except Exception as e:
            logger.error(f"Error quick saving: {e}")
            show_warning_message("Quick Save Error", f"Failed to quick save: {e}")

    def _load_last_macro(self):
        """Load the last used macro if available using recording manager."""
        try:
            # Try to load using recording manager first
            if hasattr(self.recording_manager, 'load_last_macro'):
                success = self.recording_manager.load_last_macro()
                if success:
                    return

            # Fallback to old method
            recorded_actions_loaded, last_path = load_last_macro()
            if recorded_actions_loaded is not None:
                # Use recording manager to import the actions
                self.recording_manager.import_actions_from_list(recorded_actions_loaded, 'replace')
                self.status_var.set(f"Loaded last macro: {os.path.basename(last_path) if last_path else 'Unknown'}")

                # Update UI with loaded settings
                death_settings = get_death_detection_settings()
                self.death_enabled_var.set(death_settings['enabled'])
                self.check_interval_var.set(death_settings['check_interval'])
                self.target_game_var.set(game_window_settings['target_game_only'])
                self.window_title_var.set(game_window_settings['window_title'])

        except Exception as e:
            logger.error(f"Error loading last macro: {e}")
            show_warning_message("Load Error", f"Failed to load last macro: {e}")

    def _on_closing(self):
        """Handle application closing."""
        # Clean up minimalist window if it exists
        if self.minimalist_window:
            self.minimalist_window.destroy_window()

        # Stop any running processes
        global running
        running = False

        # Stop death detection
        if is_death_detection_active():
            stop_death_detection()

        # Destroy the main window
        self.root.destroy()

# Overlay Window class for overlay mode
class OverlayWindow:
    def __init__(self):
        # Create a transparent overlay window
        self.overlay = tk.Tk()
        self.overlay.title("Macro Recorder Overlay")
        self.overlay.attributes("-alpha", 0.01)  # Almost completely transparent
        self.overlay.attributes("-topmost", True)  # Always on top
        self.overlay.geometry("1x1+0+0")  # Minimal size
        self.overlay.overrideredirect(True)  # Remove window decorations

        # Create a control panel window
        self.control_panel = tk.Toplevel(self.overlay)
        self.control_panel.title("Macro Recorder Controls")
        self.control_panel.geometry("300x200+50+50")
        self.control_panel.attributes("-topmost", True)

        # Add buttons for recording and playback
        record_button = create_styled_button(
            self.control_panel,
            "Start Recording (F8)",
            self._toggle_recording,
            "#e74c3c"
        )
        record_button.pack(pady=10)

        playback_button = create_styled_button(
            self.control_panel,
            "Start/Stop Playback (F9)",
            self._toggle_playback,
            "#2ecc71"
        )
        playback_button.pack(pady=10)

        # Set up keyboard hooks
        if HAVE_KEYBOARD:
            try:
                keyboard.add_hotkey('f8', self._toggle_recording)
                keyboard.add_hotkey('f9', self._toggle_playback)
                keyboard.add_hotkey('f12', self._toggle_death_detection)
                logger.info("Keyboard hotkeys registered for overlay mode")
            except Exception as e:
                logger.error(f"Error setting up keyboard hooks in overlay mode: {e}")

    def _toggle_recording(self):
        """Toggle recording state."""
        global recording
        recording = not recording
        logger.info(f"Recording {'started' if recording else 'stopped'} from overlay")

    def _toggle_playback(self):
        """Toggle playback state."""
        global running, playback_thread

        if running and playback_thread and playback_thread.is_alive():
            self._stop_playback()
        else:
            running = True
            playback_thread = threading.Thread(
                target=playback_actions,
                args=(1, 0.5, False)  # Default values
            )
            playback_thread.daemon = True
            playback_thread.start()
            logger.info("Playback started from overlay")

    def _toggle_death_detection(self):
        """Toggle death detection from overlay."""
        if is_death_detection_active():
            stop_death_detection()
            logger.info("Death detection stopped from overlay")
        else:
            start_death_detection()
            logger.info("Death detection started from overlay")

# Main function
def main():
    """Main entry point."""
    try:
        # Check admin privileges and elevate if needed
        check_admin_and_elevate()

        # Check if overlay mode is enabled
        if game_window_settings.get('use_overlay_mode', False):
            # Create overlay window
            overlay = OverlayWindow()
            overlay.overlay.mainloop()
        else:
            # Create the normal window
            root = tk.Tk()
            MacroRecorderGUI(root)
            root.mainloop()
    except Exception as e:
        logger.error(f"Critical error in main: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        try:
            messagebox.showerror("Critical Error", f"Application crashed: {e}\n\nCheck the logs for details.")
        except:
            print(f"Critical error: {e}")
        raise

if __name__ == "__main__":
    main()
