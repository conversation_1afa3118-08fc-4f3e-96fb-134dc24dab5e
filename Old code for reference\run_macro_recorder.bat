@echo off
echo Running Game Macro Recorder as Administrator...
echo.

:: Check if Python is installed
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    pause
    exit /b
)

:: Check if the script exists
if not exist "game_macro_recorder.py" (
    echo game_macro_recorder.py not found in the current directory.
    pause
    exit /b
)

:: Install required libraries
echo Installing required libraries...
pip install pyautogui keyboard mouse pywin32 numpy pillow --quiet

:: Make sure critical modules are installed (sometimes fails silently)
pip install mouse --quiet
pip install numpy --quiet
pip install pywin32 --quiet

:: Run the script as administrator
powershell -Command "Start-Process python -ArgumentList 'game_macro_recorder.py' -Verb RunAs"

echo.
echo If a UAC prompt appears, please click "Yes" to allow the script to run with administrator privileges.
echo.
pause
