#!/usr/bin/env python3
"""
Test script to verify the timed action add/edit dialog functionality.
"""

import tkinter as tk
import sys
import time

def test_timed_action_dialog():
    """Test the timed action dialog functionality."""
    print("=" * 60)
    print("Timed Action Dialog Test")
    print("=" * 60)
    
    try:
        # Test imports
        print("1. Testing imports...")
        from timed_action import TimedAction, add_timed_action, get_timed_action, get_all_timed_actions
        from main import MacroRecorderGUI
        from ui_helpers import create_styled_button, show_info_message
        print("   ✅ All imports successful")
        
        # Create a test action for editing
        print("\n2. Creating test action for editing...")
        test_action = TimedAction(
            name="test_edit_action",
            keys=["space", "enter"],
            interval_ms=3000,
            enabled=True,
            description="Test action for edit dialog"
        )
        add_timed_action(test_action)
        print("   ✅ Test action created for editing")
        
        # Create a simple GUI to test the dialogs
        root = tk.Tk()
        root.title("Timed Action Dialog Test")
        root.geometry("600x400")
        root.withdraw()  # Hide the main window to avoid geometry conflicts

        # Color scheme (same as main app)
        colors = {
            'primary': '#2c3e50',
            'secondary': '#34495e',
            'accent': '#3498db',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'light': '#ecf0f1',
            'dark': '#2c3e50',
            'info': '#3498db'
        }

        # Create a minimal GUI instance to access the dialog method
        gui = MacroRecorderGUI(root)

        # Create a separate test window
        test_window = tk.Toplevel(root)
        test_window.title("Timed Action Dialog Test")
        test_window.geometry("600x400")
        
        def test_add_dialog():
            """Test the add dialog."""
            try:
                gui._show_timed_action_dialog()
                print("   ✅ Add dialog opened successfully")
            except Exception as e:
                print(f"   ❌ Error opening add dialog: {e}")
        
        def test_edit_dialog():
            """Test the edit dialog."""
            try:
                gui._show_timed_action_dialog("test_edit_action")
                print("   ✅ Edit dialog opened successfully")
            except Exception as e:
                print(f"   ❌ Error opening edit dialog: {e}")
        
        def test_edit_nonexistent():
            """Test editing a non-existent action."""
            try:
                gui._show_timed_action_dialog("nonexistent_action")
                print("   ✅ Non-existent action handled properly")
            except Exception as e:
                print(f"   ❌ Error handling non-existent action: {e}")
        
        def show_actions():
            """Show current actions."""
            actions = get_all_timed_actions()
            print(f"\n   Current actions: {list(actions.keys())}")
            for name, action in actions.items():
                print(f"   - {name}: keys={action.keys}, interval={action.get_interval_display()}")
        
        # Main window content
        title_label = tk.Label(test_window, text="Timed Action Dialog Test", font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)

        info_label = tk.Label(test_window, text="Test the add and edit dialogs for timed actions.\nLook for the '💾 Save' button in each dialog.",
                             font=('Arial', 12), justify='center')
        info_label.pack(pady=20)

        # Test buttons
        button_frame = tk.Frame(test_window)
        button_frame.pack(pady=20)

        add_btn = create_styled_button(button_frame, "➕ Test Add Dialog", test_add_dialog, colors['success'], width=20)
        add_btn.pack(pady=10)

        edit_btn = create_styled_button(button_frame, "✏️ Test Edit Dialog", test_edit_dialog, colors['accent'], width=20)
        edit_btn.pack(pady=10)

        nonexistent_btn = create_styled_button(button_frame, "❌ Test Non-existent", test_edit_nonexistent, colors['warning'], width=20)
        nonexistent_btn.pack(pady=10)

        show_btn = create_styled_button(button_frame, "📋 Show Actions", show_actions, colors['info'], width=20)
        show_btn.pack(pady=10)

        # Instructions
        instructions = tk.Label(test_window, text="Instructions:\n1. Click each test button\n2. Look for the '💾 Save' button in dialogs\n3. Test save functionality\n4. Check console output\n5. Close this window when done",
                               font=('Arial', 10), justify='left')
        instructions.pack(pady=20)
        
        print("\n3. Visual test window created")
        print("   ✅ Click the test buttons to verify dialog functionality")
        
        # Start the GUI
        root.mainloop()
        
        # Clean up
        from timed_action import remove_timed_action
        remove_timed_action("test_edit_action")
        print("\n4. Test cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_dialog_components():
    """Test individual dialog components."""
    print("\n" + "=" * 60)
    print("Dialog Components Test")
    print("=" * 60)
    
    try:
        # Test dialog creation without showing
        print("1. Testing dialog component creation...")
        from timed_action import (
            get_timed_action, TimedAction, parse_interval_string, 
            validate_keys, get_supported_keys
        )
        
        # Test interval parsing
        test_intervals = ["1000ms", "5s", "2m", "invalid"]
        print("   ✅ Testing interval parsing:")
        for interval in test_intervals:
            result = parse_interval_string(interval)
            print(f"      '{interval}' -> {result}ms")
        
        # Test key validation
        test_keys = ["space", "enter", "invalid_key", "a", "f1"]
        valid_keys, invalid_keys = validate_keys(test_keys)
        print(f"   ✅ Key validation: valid={valid_keys}, invalid={invalid_keys}")
        
        # Test supported keys
        supported = get_supported_keys()
        print(f"   ✅ Supported keys: {len(supported)} total")
        
        # Test action creation
        test_action = TimedAction(
            name="component_test",
            keys=["space"],
            interval_ms=2000,
            enabled=True,
            description="Component test"
        )
        print(f"   ✅ Action creation: {test_action.name}, {test_action.get_interval_display()}")
        
        print("\n🎉 Dialog components test passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Components test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("Starting Timed Action Dialog Tests...")
    
    # Test dialog components first
    components_success = test_dialog_components()
    
    # Test visual dialog
    if components_success:
        dialog_success = test_timed_action_dialog()
    else:
        dialog_success = False
    
    # Overall result
    if components_success and dialog_success:
        print("\n🎉 ALL DIALOG TESTS PASSED! Add/Edit dialogs are working correctly!")
        print("\nKey features verified:")
        print("- ✅ Add dialog opens with save button")
        print("- ✅ Edit dialog opens with save button") 
        print("- ✅ Non-existent action handling")
        print("- ✅ Input validation")
        print("- ✅ Interval parsing")
        print("- ✅ Key validation")
    else:
        print("\n❌ Some dialog tests failed. Please check the output above.")
    
    sys.exit(0 if (components_success and dialog_success) else 1)
