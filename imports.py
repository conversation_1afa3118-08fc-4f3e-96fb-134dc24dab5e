"""
Imports Module - Centralized library imports, availability flags, and constants
"""
import time
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import ctypes
import json
import os
import numpy as np
import win32gui
import win32con
import win32api
import logging
import cv2  # Import OpenCV for template matching

# Import our improved direct input module
import direct_input

# Import tooltip module
import tooltip

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("game_macro_recorder")

# Try to import required libraries
try:
    import pyautogui
    pyautogui.FAILSAFE = False  # Disable fail-safe
    HAVE_PYAUTOGUI = True
except ImportError:
    HAVE_PYAUTOGUI = False
    messagebox.showerror("Error", "PyAutoGUI not installed. Please install it with: pip install pyautogui")

try:
    import keyboard
    HAVE_KEYBOARD = True
except ImportError:
    HAVE_KEYBOARD = False
    messagebox.showerror("Error", "Keyboard module not installed. Please install it with: pip install keyboard")

# Try to import mouse module for global mouse hooks
try:
    import mouse
    HAVE_MOUSE = True
    logger.info("Mouse module imported successfully")
except ImportError:
    HAVE_MOUSE = False
    logger.warning("Mouse module not available, will try alternative methods")

try:
    import win32api
    import win32con
    HAVE_WIN32 = True
except ImportError:
    HAVE_WIN32 = False
    print("Win32 API not available. Some features may not work.")

# Virtual key codes
VK_CODES = {
    'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45, 'f': 0x46, 'g': 0x47, 'h': 0x48, 'i': 0x49,
    'j': 0x4A, 'k': 0x4B, 'l': 0x4C, 'm': 0x4D, 'n': 0x4E, 'o': 0x4F, 'p': 0x50, 'q': 0x51, 'r': 0x52,
    's': 0x53, 't': 0x54, 'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58, 'y': 0x59, 'z': 0x5A,
    '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34, '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,
    'f1': 0x70, 'f2': 0x71, 'f3': 0x72, 'f4': 0x73, 'f5': 0x74, 'f6': 0x75, 'f7': 0x76, 'f8': 0x77,
    'f9': 0x78, 'f10': 0x79, 'f11': 0x7A, 'f12': 0x7B,
    'space': 0x20, 'enter': 0x0D, 'tab': 0x09, 'esc': 0x1B, 'backspace': 0x08,
    'shift': 0x10, 'ctrl': 0x11, 'alt': 0x12,
    'up': 0x26, 'down': 0x28, 'left': 0x25, 'right': 0x27,
}
