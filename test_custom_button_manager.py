#!/usr/bin/env python3
"""
Test script for the Custom Button Management system.
"""

import sys
import os
import time
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_custom_button_manager():
    """Test the custom button management functionality."""
    print("=" * 60)
    print("Custom Button Management Test")
    print("=" * 60)

    try:
        # Test imports
        print("1. Testing imports...")
        from custom_button_manager import (
            CustomTemplate, add_custom_template, get_all_custom_templates,
            remove_custom_template, update_custom_template, save_templates,
            load_templates, get_custom_button_settings, is_custom_button_active,
            start_custom_button_detection, stop_custom_button_detection,
            get_template_statistics, export_templates_config, import_templates_config,
            capture_template_from_screen, test_template_detection_once, test_all_templates_once,
            reset_template_statistics
        )
        print("   ✅ All imports successful (including new functions)")
        
        # Test template creation
        print("\n2. Testing template creation...")
        
        # Create a test template (without actual image file for now)
        test_template = CustomTemplate(
            name="test_button",
            image_path="test_template.png",
            confidence=0.8,
            auto_click=True,
            enabled=True,
            cooldown=2.0
        )
        
        print(f"   ✅ Template created: {test_template.name}")
        print(f"      Confidence: {test_template.confidence}")
        print(f"      Auto-click: {test_template.auto_click}")
        print(f"      Enabled: {test_template.enabled}")
        print(f"      Cooldown: {test_template.cooldown}")
        
        # Test template serialization
        print("\n3. Testing template serialization...")
        template_dict = test_template.to_dict()
        print(f"   ✅ Template serialized: {len(template_dict)} fields")
        
        restored_template = CustomTemplate.from_dict(template_dict)
        print(f"   ✅ Template restored: {restored_template.name}")
        
        # Test template management
        print("\n4. Testing template management...")
        
        # Add template
        add_custom_template(test_template)
        templates = get_all_custom_templates()
        print(f"   ✅ Template added. Total templates: {len(templates)}")
        
        # Update template
        success = update_custom_template("test_button", confidence=0.9, enabled=False)
        print(f"   ✅ Template updated: {success}")
        
        # Get updated template
        updated_template = templates.get("test_button")
        if updated_template:
            print(f"      New confidence: {updated_template.confidence}")
            print(f"      New enabled state: {updated_template.enabled}")
        
        # Test statistics
        print("\n5. Testing statistics...")
        stats = get_template_statistics()
        print(f"   ✅ Statistics retrieved:")
        print(f"      Total templates: {stats['total_templates']}")
        print(f"      Enabled templates: {stats['enabled_templates']}")
        print(f"      Total detections: {stats['total_detections']}")
        
        # Test settings
        print("\n6. Testing settings...")
        settings = get_custom_button_settings()
        print(f"   ✅ Settings retrieved: {len(settings)} settings")
        print(f"      Check interval: {settings['check_interval']}")
        print(f"      Global cooldown: {settings['global_cooldown']}")
        
        # Test save/load
        print("\n7. Testing save/load...")
        save_success = save_templates()
        print(f"   ✅ Templates saved: {save_success}")
        
        load_success = load_templates()
        print(f"   ✅ Templates loaded: {load_success}")
        
        # Test detection state
        print("\n8. Testing detection state...")
        initial_state = is_custom_button_active()
        print(f"   ✅ Initial detection state: {initial_state}")
        
        # Test export/import (to temporary file)
        print("\n9. Testing export/import...")
        temp_export_path = "temp_templates_export.json"
        
        try:
            export_success = export_templates_config(temp_export_path)
            print(f"   ✅ Export successful: {export_success}")
            
            if export_success and os.path.exists(temp_export_path):
                # Clear templates and import
                remove_custom_template("test_button")
                import_success = import_templates_config(temp_export_path)
                print(f"   ✅ Import successful: {import_success}")
                
                # Verify import worked
                imported_templates = get_all_custom_templates()
                print(f"   ✅ Templates after import: {len(imported_templates)}")
                
        finally:
            # Clean up temp file
            if os.path.exists(temp_export_path):
                os.remove(temp_export_path)
        
        # Test new functionality
        print("\n10. Testing new functionality...")

        # Test template testing functions
        test_result = test_template_detection_once("test_button")
        print(f"   ✅ Single template test: {test_result}")

        all_test_result = test_all_templates_once()
        print(f"   ✅ All templates test: {all_test_result}")

        # Test statistics reset
        reset_template_statistics()
        print("   ✅ Template statistics reset")

        # Verify reset worked
        stats_after_reset = get_template_statistics()
        print(f"   ✅ Total detections after reset: {stats_after_reset['total_detections']}")

        # Test persistence (save and reload)
        print("\n11. Testing persistence...")
        save_success = save_templates()
        print(f"   ✅ Templates saved: {save_success}")

        # Clear templates in memory
        global _custom_templates
        from custom_button_manager import _custom_templates
        original_count = len(_custom_templates)
        _custom_templates.clear()
        print(f"   ✅ Templates cleared from memory (was {original_count})")

        # Reload templates
        load_success = load_templates()
        reloaded_count = len(_custom_templates)
        print(f"   ✅ Templates reloaded: {load_success} (count: {reloaded_count})")

        # Clean up test template
        print("\n12. Cleaning up...")
        remove_success = remove_custom_template("test_button")
        print(f"   ✅ Test template removed: {remove_success}")

        print("\n" + "=" * 60)
        print("🎉 All Custom Button Management tests passed!")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_gui_integration():
    """Test GUI integration (basic import test)."""
    print("\n" + "=" * 60)
    print("GUI Integration Test")
    print("=" * 60)
    
    try:
        print("1. Testing GUI imports...")
        
        # Test if main.py can import custom_button_manager
        import main
        print("   ✅ Main GUI module imported successfully")
        
        # Check if the custom button tab creation method exists
        if hasattr(main.MacroRecorderGUI, '_create_custom_button_tab'):
            print("   ✅ Custom button tab creation method found")
        else:
            print("   ❌ Custom button tab creation method not found")
            return False
        
        # Check if custom button management methods exist
        required_methods = [
            '_toggle_custom_button_detection',
            '_refresh_templates_display',
            '_add_custom_template',
            '_edit_custom_template',
            '_delete_custom_template',
            '_test_custom_template',
            '_capture_custom_template',
            '_reset_template_stats',
            '_test_all_custom_templates',
            '_load_custom_templates_on_startup'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(main.MacroRecorderGUI, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"   ❌ Missing methods: {missing_methods}")
            return False
        else:
            print("   ✅ All required GUI methods found")
        
        print("\n🎉 GUI Integration test passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ GUI Integration test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("Starting Custom Button Management Tests...\n")
    
    # Run core functionality tests
    core_success = test_custom_button_manager()
    
    # Run GUI integration tests
    gui_success = test_gui_integration()
    
    # Overall result
    if core_success and gui_success:
        print("\n🎉 ALL TESTS PASSED! Custom Button Management system is ready!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the output above.")
        sys.exit(1)
