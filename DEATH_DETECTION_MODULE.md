# Death Detection Module Documentation

## Overview

The death detection functionality has been successfully separated into its own dedicated module (`death_detection.py`) while maintaining 100% compatibility with existing functionality. This separation improves code organization, maintainability, and allows for easier testing and development of death detection features.

## Module Structure

### File: `death_detection.py`

The death detection module is completely self-contained and provides all functionality related to automatic character revival detection and clicking.

## Key Features

### 1. Template Matching
- **Automatic revival button detection** using OpenCV template matching
- **Configurable confidence threshold** for reliable detection
- **Template capture functionality** for easy setup

### 2. Automatic Revival
- **Auto-click revival button** when detected
- **Game window targeting** for precise clicking
- **Configurable cooldown periods** to prevent spam clicking

### 3. Thread Management
- **Background processing** that doesn't block the main application
- **Safe thread startup and shutdown** with proper cleanup
- **Status monitoring** and reporting

## API Reference

### Settings Management

```python
# Get current death detection settings
settings = get_death_detection_settings()

# Update specific settings
update_death_detection_settings(
    enabled=True,
    confidence=0.85,
    check_interval=2.0,
    auto_click_revival=True
)
```

### State Management

```python
# Check if death detection is active
is_active = is_death_detection_active()

# Start death detection
success = start_death_detection()

# Stop death detection
success = stop_death_detection()

# Get comprehensive status
status = get_death_detection_status()
```

### Template Management

```python
# Capture a new revival button template
success = capture_revival_button_template()

# Manually click revival button (for testing)
success = click_revival_button()
```

## Configuration Options

### Core Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `enabled` | bool | False | Enable/disable death detection |
| `confidence` | float | 0.85 | Template matching confidence (0.0-1.0) |
| `check_interval` | float | 2.0 | Seconds between detection checks |
| `auto_click_revival` | bool | True | Automatically click revival button |
| `detection_cooldown` | int | 10 | Seconds to wait after death detection |

### Template Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `use_template_matching` | bool | True | Use template matching for detection |
| `template_path` | str | "templates/revival_button.png" | Path to revival button template |

### Legacy Settings (Compatibility)

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `color` | tuple | (80, 80, 80) | Legacy color detection (not used) |
| `threshold` | int | 200 | Legacy pixel threshold (not used) |
| `region` | tuple | None | Legacy detection region (not used) |
| `click_position` | tuple | None | Legacy click position (not used) |

## Integration with Main Application

### UI Integration

The death detection module integrates seamlessly with the main GUI:

```python
# In main.py
from death_detection import *

# Toggle death detection from UI
def _toggle_death_detection(self):
    enabled = self.death_enabled_var.get()
    
    update_death_detection_settings(
        enabled=enabled,
        auto_click_revival=self.auto_click_var.get(),
        confidence=self.confidence_var.get() / 100.0,
        check_interval=self.check_interval_var.get()
    )
    
    if enabled:
        start_death_detection()
    else:
        stop_death_detection()
```

### Configuration Integration

The module integrates with the configuration system:

```python
# In config.py
def save_actions(filename, recorded_actions):
    from death_detection import get_death_detection_settings
    death_settings = get_death_detection_settings()
    # Save death detection settings with actions
    
def load_actions(filename):
    from death_detection import update_death_detection_settings
    # Load and apply death detection settings
```

## Dependencies

### Required Modules
- `imports.py` - For logger and OpenCV imports
- `recorder.py` - For mouse click functionality
- `win_api.py` - For game window management
- `config.py` - For game window settings

### External Libraries
- **OpenCV** (`cv2`) - For template matching
- **PyAutoGUI** (`pyautogui`) - For screenshot capture
- **NumPy** (`numpy`) - For image processing
- **Win32GUI** (`win32gui`) - For window management

## Usage Examples

### Basic Setup

```python
# Import the module
from death_detection import *

# Configure settings
update_death_detection_settings(
    enabled=True,
    confidence=0.80,
    check_interval=1.5,
    auto_click_revival=True
)

# Start death detection
if start_death_detection():
    print("Death detection started successfully")
else:
    print("Failed to start death detection")
```

### Template Capture

```python
# Capture a new revival button template
# (Make sure the revival button is visible on screen)
if capture_revival_button_template():
    print("Template captured successfully")
    print(f"Template saved to: {get_death_detection_settings()['template_path']}")
else:
    print("Failed to capture template")
```

### Status Monitoring

```python
# Get detailed status information
status = get_death_detection_status()
print(f"Active: {status['active']}")
print(f"Thread alive: {status['thread_alive']}")
print(f"Settings: {status['settings']}")
```

## Error Handling

The module includes comprehensive error handling:

### Common Issues and Solutions

1. **Template Not Found**
   - Error: "Template file not found"
   - Solution: Capture a new template using `capture_revival_button_template()`

2. **Low Confidence Detection**
   - Error: "Revival button not found (confidence too low)"
   - Solution: Lower the confidence threshold or capture a better template

3. **PyAutoGUI Not Available**
   - Error: "PyAutoGUI not available for template matching"
   - Solution: Install PyAutoGUI: `pip install pyautogui`

4. **OpenCV Not Available**
   - Error: Import errors related to cv2
   - Solution: Install OpenCV: `pip install opencv-python`

## Thread Safety

The death detection module is designed to be thread-safe:

- **Global state variables** are properly managed
- **Thread lifecycle** is controlled through safe start/stop functions
- **Resource cleanup** is handled automatically
- **Exception handling** prevents thread crashes

## Performance Considerations

### Optimization Features

1. **Configurable check intervals** - Adjust frequency based on needs
2. **Cooldown periods** - Prevent excessive processing after detection
3. **Efficient template matching** - Uses optimized OpenCV algorithms
4. **Background processing** - Doesn't block the main application

### Resource Usage

- **CPU**: Low impact during normal operation
- **Memory**: Minimal footprint with template caching
- **Disk**: Only template image files
- **Network**: No network usage

## Testing

### Unit Testing

```python
# Test module import
import death_detection

# Test basic functionality
assert death_detection.is_death_detection_active() == False
death_detection.update_death_detection_settings(enabled=True)
settings = death_detection.get_death_detection_settings()
assert settings['enabled'] == True
```

### Integration Testing

The module is tested as part of the main test suite in `test_modules.py`:

```bash
python test_modules.py
```

## Migration Notes

### From Previous Version

If upgrading from a version where death detection was part of other modules:

1. **No code changes required** - All existing functionality is preserved
2. **Settings are automatically migrated** - Configuration files remain compatible
3. **UI remains unchanged** - All user interface elements work the same way

### Backward Compatibility

- ✅ All existing configuration files work without modification
- ✅ All UI controls function identically
- ✅ All hotkeys and shortcuts remain the same
- ✅ All saved macros load correctly with death detection settings

## Future Enhancements

The modular structure makes it easy to add new features:

### Planned Features
- **Multiple template support** - Detect different revival buttons
- **Region-based detection** - Limit detection to specific screen areas
- **Advanced image processing** - Better detection algorithms
- **Statistics and logging** - Track detection success rates

### Extension Points
- **Custom detection algorithms** - Easy to add new detection methods
- **Plugin architecture** - Support for third-party detection plugins
- **API endpoints** - Remote control and monitoring capabilities

## Conclusion

The death detection module separation provides:

- ✅ **Improved maintainability** - Focused, single-responsibility module
- ✅ **Better testability** - Isolated functionality for easier testing
- ✅ **Enhanced extensibility** - Easy to add new features
- ✅ **Preserved compatibility** - No breaking changes to existing functionality
- ✅ **Cleaner architecture** - Better separation of concerns

The module is production-ready and maintains all existing functionality while providing a solid foundation for future enhancements.
