"""
Game Macro Recorder - Advanced Auto Clicker with Recording Capabilities
Records and replays sequences of keyboard and mouse actions
"""
import time
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import ctypes
import json
import os
import numpy as np
import win32gui
import win32con
import win32api
import logging
import cv2  # Import OpenCV for template matching

# Import our improved direct input module
import direct_input

# Import tooltip module
import tooltip

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("game_macro_recorder")

# Check if running as admin
def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

# If not running as admin, show a warning
if not is_admin():
    messagebox.showwarning("Warning", "This script is not running as administrator. Some games may block inputs from non-admin processes. Please run the script as administrator for best results.")

# Try to import required libraries
try:
    import pyautogui
    pyautogui.FAILSAFE = False  # Disable fail-safe
    HAVE_PYAUTOGUI = True
except ImportError:
    HAVE_PYAUTOGUI = False
    messagebox.showerror("Error", "PyAutoGUI not installed. Please install it with: pip install pyautogui")

try:
    import keyboard
    HAVE_KEYBOARD = True
except ImportError:
    HAVE_KEYBOARD = False
    messagebox.showerror("Error", "Keyboard module not installed. Please install it with: pip install keyboard")

# Try to import mouse module for global mouse hooks
try:
    import mouse
    HAVE_MOUSE = True
    logger.info("Mouse module imported successfully")
except ImportError:
    HAVE_MOUSE = False
    logger.warning("Mouse module not available, will try alternative methods")

try:
    import win32api
    import win32con
    HAVE_WIN32 = True
except ImportError:
    HAVE_WIN32 = False
    print("Win32 API not available. Some features may not work.")

# Load user32.dll for direct Windows API calls
user32 = ctypes.WinDLL('user32', use_last_error=True)

# Constants for SendInput
INPUT_KEYBOARD = 1
INPUT_MOUSE = 0
KEYEVENTF_KEYUP = 0x0002
KEYEVENTF_EXTENDEDKEY = 0x0001

# Virtual key codes
VK_CODES = {
    'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45, 'f': 0x46, 'g': 0x47, 'h': 0x48, 'i': 0x49,
    'j': 0x4A, 'k': 0x4B, 'l': 0x4C, 'm': 0x4D, 'n': 0x4E, 'o': 0x4F, 'p': 0x50, 'q': 0x51, 'r': 0x52,
    's': 0x53, 't': 0x54, 'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58, 'y': 0x59, 'z': 0x5A,
    '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34, '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,
    'f1': 0x70, 'f2': 0x71, 'f3': 0x72, 'f4': 0x73, 'f5': 0x74, 'f6': 0x75, 'f7': 0x76, 'f8': 0x77,
    'f9': 0x78, 'f10': 0x79, 'f11': 0x7A, 'f12': 0x7B,
    'space': 0x20, 'enter': 0x0D, 'tab': 0x09, 'esc': 0x1B, 'backspace': 0x08,
    'shift': 0x10, 'ctrl': 0x11, 'alt': 0x12,
    'up': 0x26, 'down': 0x28, 'left': 0x25, 'right': 0x27,
}

# Define structures for SendInput
class MOUSEINPUT(ctypes.Structure):
    _fields_ = (("dx", ctypes.c_long),
                ("dy", ctypes.c_long),
                ("mouseData", ctypes.c_ulong),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong)))

class KEYBDINPUT(ctypes.Structure):
    _fields_ = (("wVk", ctypes.c_ushort),
                ("wScan", ctypes.c_ushort),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong)))

class HARDWAREINPUT(ctypes.Structure):
    _fields_ = (("uMsg", ctypes.c_ulong),
                ("wParamL", ctypes.c_short),
                ("wParamH", ctypes.c_ushort))

class INPUT_UNION(ctypes.Union):
    _fields_ = (("mi", MOUSEINPUT),
                ("ki", KEYBDINPUT),
                ("hi", HARDWAREINPUT))

class INPUT(ctypes.Structure):
    _fields_ = (("type", ctypes.c_ulong),
                ("union", INPUT_UNION))

# Global variables
running = False
recording = False
playback_thread = None
recorded_actions = []
last_action_time = None
death_detection_active = False
death_detection_thread = None
game_window_handle = None

# Game window settings
game_window_settings = {
    'window_title': 'Rappelz',  # The window title to look for
    'target_game_only': True,   # Whether to target only the game window
    'stay_on_top': False,       # Whether the app should stay on top of other windows
    'prevent_focus_steal': False, # Prevent the app from stealing focus from the game
    'use_overlay_mode': False    # Use a transparent overlay window that doesn't steal focus
}

# Death detection settings
death_detection_settings = {
    'enabled': False,
    'color': (80, 80, 80),  # Gray color in the death dialog
    'last_death_time': 0,  # Last time death was detected
    'threshold': 200,  # Minimum number of matching pixels to trigger detection
    'region': None,  # Custom region for detection (x1, y1, x2, y2)
    'use_template_matching': True,  # Whether to use template matching for revival button
    'confidence': 0.85,  # Confidence threshold for template matching (85%)
    'template_path': os.path.join(os.path.dirname(os.path.abspath(__file__)), "templates", "revival_button.png"),
    'auto_click_revival': True,  # Whether to automatically click the revival button
    'detection_cooldown': 10,  # Seconds to wait before checking again after death
    'check_interval': 2.0,  # Seconds between checks (for compatibility with existing UI)
    'click_position': None  # Position to click when character dies (x, y) (for compatibility with existing UI)
}

# Direct Windows API key press function
def send_key_direct(key):
    """Send a key press using direct Windows API calls."""
    global game_window_handle

    # If targeting only the game window, make sure it's active
    if game_window_settings['target_game_only']:
        # Find the game window if needed
        if not game_window_handle:
            if not find_game_window():
                logger.warning(f"Game window not found, can't send key: {key}")
                return

        # Check if the game window is active
        try:
            foreground_window = win32gui.GetForegroundWindow()
            if foreground_window != game_window_handle:
                # Game window is not active, activate it first
                win32gui.SetForegroundWindow(game_window_handle)
                time.sleep(0.2)  # Give time for window to come to foreground

                # Double-check that the window is now active
                foreground_window = win32gui.GetForegroundWindow()
                if foreground_window != game_window_handle:
                    logger.warning(f"Failed to activate game window, can't send key: {key}")
                    return
        except Exception as e:
            logger.error(f"Error activating game window: {e}")
            return

        # Use PostMessage to send the key directly to the game window
        # This ensures it only affects the game window
        if isinstance(key, str):
            # Convert string key to virtual key code
            if len(key) == 1:
                vk_code = VK_CODES.get(key.lower(), ord(key.upper()))
            else:
                vk_code = VK_CODES.get(key.lower(), 0)
        else:
            vk_code = key

        try:
            # Send key down message directly to the game window
            win32gui.PostMessage(game_window_handle, win32con.WM_KEYDOWN, vk_code, 0)
            time.sleep(0.05)  # Small delay between down and up
            # Send key up message directly to the game window
            win32gui.PostMessage(game_window_handle, win32con.WM_KEYUP, vk_code, 0)
            logger.info(f"Sent key directly to game window: {key} (VK: {vk_code})")
            return
        except Exception as e:
            logger.error(f"Error sending key to game window: {e}")
            return

    # If not targeting only the game window, use our improved direct input module
    try:
        direct_input.press_key(key, 0.05)
        logger.info(f"Sent key using direct_input: {key}")
    except Exception as e:
        logger.error(f"Error sending key with direct_input: {e}")
        # Fall back to other methods if direct input fails
        try:
            if HAVE_PYAUTOGUI:
                pyautogui.press(key)
                logger.info(f"Sent key using PyAutoGUI: {key}")
            elif HAVE_KEYBOARD:
                keyboard.press_and_release(key)
                logger.info(f"Sent key using Keyboard module: {key}")
        except Exception as fallback_error:
            logger.error(f"Fallback methods also failed: {fallback_error}")

# Direct Windows API mouse click function
def send_mouse_click_direct(button='left', x=None, y=None):
    """Send a mouse click using direct Windows API calls."""
    global game_window_handle

    # If targeting only the game window, make sure it's active
    if game_window_settings['target_game_only']:
        # Find the game window if needed
        if not game_window_handle:
            if not find_game_window():
                logger.warning(f"Game window not found, can't send mouse click: {button}")
                return

        # Check if the game window is active
        try:
            foreground_window = win32gui.GetForegroundWindow()
            if foreground_window != game_window_handle:
                # Game window is not active, activate it first
                win32gui.SetForegroundWindow(game_window_handle)
                time.sleep(0.2)  # Give time for window to come to foreground

                # Double-check that the window is now active
                foreground_window = win32gui.GetForegroundWindow()
                if foreground_window != game_window_handle:
                    logger.warning(f"Failed to activate game window, can't send mouse click: {button}")
                    return
        except Exception as e:
            logger.error(f"Error activating game window: {e}")
            return

        # If we have coordinates, make sure they're within the game window
        if x is not None and y is not None:
            window_rect = get_game_window_rect()
            if window_rect:
                # Check if the coordinates are within the game window
                if (x < window_rect['x'] or x > window_rect['x'] + window_rect['width'] or
                    y < window_rect['y'] or y > window_rect['y'] + window_rect['height']):
                    logger.warning(f"Coordinates ({x}, {y}) are outside the game window, skipping click")
                    return

                # Convert screen coordinates to client coordinates
                client_x = x - window_rect['x']
                client_y = y - window_rect['y']

                # Send mouse messages directly to the game window
                try:
                    # Move cursor to position
                    win32gui.PostMessage(game_window_handle, win32con.WM_MOUSEMOVE, 0,
                                        win32api.MAKELONG(client_x, client_y))
                    time.sleep(0.05)

                    # Send mouse down message
                    if button.lower() == 'left':
                        win32gui.PostMessage(game_window_handle, win32con.WM_LBUTTONDOWN,
                                           win32con.MK_LBUTTON, win32api.MAKELONG(client_x, client_y))
                        time.sleep(0.05)
                        win32gui.PostMessage(game_window_handle, win32con.WM_LBUTTONUP,
                                           0, win32api.MAKELONG(client_x, client_y))
                    elif button.lower() == 'right':
                        win32gui.PostMessage(game_window_handle, win32con.WM_RBUTTONDOWN,
                                           win32con.MK_RBUTTON, win32api.MAKELONG(client_x, client_y))
                        time.sleep(0.05)
                        win32gui.PostMessage(game_window_handle, win32con.WM_RBUTTONUP,
                                           0, win32api.MAKELONG(client_x, client_y))
                    elif button.lower() == 'middle':
                        win32gui.PostMessage(game_window_handle, win32con.WM_MBUTTONDOWN,
                                           win32con.MK_MBUTTON, win32api.MAKELONG(client_x, client_y))
                        time.sleep(0.05)
                        win32gui.PostMessage(game_window_handle, win32con.WM_MBUTTONUP,
                                           0, win32api.MAKELONG(client_x, client_y))

                    logger.info(f"Sent {button} click directly to game window at ({x}, {y}) (client: {client_x}, {client_y})")
                    return
                except Exception as e:
                    logger.error(f"Error sending mouse click to game window: {e}")
                    # Fall back to standard method
            else:
                logger.warning("Could not get game window rect, falling back to standard method")
        else:
            logger.info("No coordinates provided for game window click, falling back to standard method")

    # If not targeting only the game window or if the above failed, use our improved direct input module
    try:
        if x is not None and y is not None:
            # Move to position and click
            direct_input.mouse_move_absolute(x, y)
            time.sleep(0.05)  # Small delay to ensure cursor moves
            direct_input.mouse_click(button)
        else:
            # Click at current position
            direct_input.mouse_click(button)

        pos_str = f" at ({x}, {y})" if x is not None and y is not None else ""
        logger.info(f"Sent {button} click using direct_input{pos_str}")
    except Exception as e:
        logger.error(f"Error sending mouse click with direct_input: {e}")
        # Fall back to other methods if direct input fails
        try:
            if x is not None and y is not None:
                # First, use SetCursorPos which is more reliable for games
                try:
                    ctypes.windll.user32.SetCursorPos(int(x), int(y))
                    time.sleep(0.05)  # Small delay to ensure cursor moves
                except Exception as cursor_error:
                    logger.error(f"Error setting cursor position with SetCursorPos: {cursor_error}")

                    # Try PyAutoGUI as a backup
                    if HAVE_PYAUTOGUI:
                        try:
                            pyautogui.moveTo(x, y)
                            time.sleep(0.05)  # Small delay
                        except Exception as pyautogui_error:
                            logger.error(f"Error moving mouse with PyAutoGUI: {pyautogui_error}")

            # Try PyAutoGUI for clicking
            if HAVE_PYAUTOGUI:
                if button == 'left':
                    pyautogui.click()
                elif button == 'right':
                    pyautogui.rightClick()
                elif button == 'middle':
                    pyautogui.middleClick()

                pos_str = f" at ({x}, {y})" if x is not None and y is not None else ""
                logger.info(f"Sent {button} click using PyAutoGUI{pos_str}")
        except Exception as fallback_error:
            logger.error(f"Fallback methods also failed: {fallback_error}")

# Record a key press
def record_key_press(key):
    """Record a key press action."""
    global last_action_time, recorded_actions

    try:
        # Validate input
        if not isinstance(key, str) or not key:
            logger.warning(f"Invalid key: {key}")
            return False

        # Calculate delay
        current_time = time.time()
        if last_action_time is not None:
            delay = current_time - last_action_time
        else:
            delay = 0

        # Create action record
        action = {
            'type': 'key',
            'key': key,
            'delay': delay
        }

        # Add to recorded actions
        recorded_actions.append(action)
        last_action_time = current_time

        # Log the action
        logger.info(f"Recorded key press: {key} (delay: {delay:.2f}s)")

        return True
    except Exception as e:
        logger.error(f"Error recording key press: {e}")
        return False

# Record a mouse click
def record_mouse_click(button, x, y):
    """Record a mouse click action."""
    global last_action_time, recorded_actions

    try:
        # Validate inputs
        if not isinstance(button, str) or not button.lower() in ['left', 'right', 'middle']:
            logger.warning(f"Invalid button type: {button}, defaulting to 'left'")
            button = 'left'

        if not isinstance(x, (int, float)) or not isinstance(y, (int, float)):
            logger.warning(f"Invalid coordinates: ({x}, {y}), using current mouse position")
            # Get current mouse position as fallback
            try:
                x, y = pyautogui.position()
            except:
                x, y = 0, 0

        # Calculate delay
        current_time = time.time()
        if last_action_time is not None:
            delay = current_time - last_action_time
        else:
            delay = 0

        # Create action record
        action = {
            'type': 'mouse',
            'button': button,
            'x': int(x),  # Convert to int to ensure serialization works
            'y': int(y),
            'delay': delay
        }

        # Add to recorded actions
        recorded_actions.append(action)
        last_action_time = current_time

        # Log the action
        logger.info(f"Recorded {button} click at ({x}, {y}) (delay: {delay:.2f}s)")

        return True
    except Exception as e:
        logger.error(f"Error recording mouse click: {e}")
        return False

# Playback recorded actions
def playback_actions(repeat_count=1, repeat_delay=0, infinite=False):
    """Play back recorded actions."""
    global running, game_window_handle

    if not recorded_actions:
        print("No actions recorded")
        return

    # If targeting only the game window, find it first
    if game_window_settings['target_game_only']:
        if not find_game_window():
            print("Game window not found, can't start playback")
            return

        # Activate the game window
        try:
            win32gui.SetForegroundWindow(game_window_handle)
            time.sleep(0.2)  # Give time for window to come to foreground
        except Exception as e:
            print(f"Error activating game window: {e}")

    running = True
    iteration = 0

    # Main playback loop
    while running:
        # Check if we've reached the repeat count (unless infinite)
        if not infinite and iteration >= repeat_count:
            break

        # Increment iteration counter
        iteration += 1

        # Display iteration information
        if infinite:
            print(f"Playback iteration: {iteration} (infinite mode)")
        else:
            print(f"Playback iteration: {iteration} of {repeat_count}")

        # Play each action in the sequence
        for action in recorded_actions:
            if not running:
                break

            # Wait for the specified delay
            time.sleep(action['delay'])

            # Perform the action
            if action['type'] == 'key':
                # Send key press using our improved function
                send_key_direct(action['key'])

            elif action['type'] == 'mouse':
                # Get the exact coordinates from the recorded action
                x = action.get('x')
                y = action.get('y')

                if x is not None and y is not None:
                    # If targeting only the game window, make sure we're clicking inside it
                    if game_window_settings['target_game_only']:
                        # Make sure the game window is still active
                        if not game_window_handle:
                            if not find_game_window():
                                logger.warning("Game window not found, can't perform click")
                                continue

                        # Activate the game window if needed
                        try:
                            foreground_window = win32gui.GetForegroundWindow()
                            if foreground_window != game_window_handle:
                                win32gui.SetForegroundWindow(game_window_handle)
                                time.sleep(0.2)  # Give time for window to come to foreground
                        except Exception as e:
                            logger.error(f"Error checking/activating game window: {e}")

                    logger.info(f"Clicking at exact coordinates: ({x}, {y})")

                # Send mouse click using our improved function
                send_mouse_click_direct(action['button'], x, y)

        # Wait between repetitions (if not the last one)
        if running and (infinite or iteration < repeat_count):
            time.sleep(repeat_delay)

    # Make sure to set running to False when playback completes
    running = False
    logger.info("Playback completed")

    # Log the current state for debugging
    logger.debug(f"Playback completed - running: {running}, thread: {threading.current_thread().name}")

# Find the game window
def find_game_window():
    """Find the game window by title."""
    global game_window_handle

    try:
        # Find the window by title
        window_title = game_window_settings['window_title']
        game_window_handle = win32gui.FindWindow(None, window_title)

        if game_window_handle:
            logger.info(f"Found game window: {window_title} (handle: {game_window_handle})")
            return True
        else:
            logger.warning(f"Game window not found: {window_title}")
            return False
    except Exception as e:
        logger.error(f"Error finding game window: {e}")
        return False

# Get game window position and size
def get_game_window_rect():
    """Get the position and size of the game window."""
    global game_window_handle

    if not game_window_handle:
        if not find_game_window():
            return None

    try:
        # Get window rect
        rect = win32gui.GetWindowRect(game_window_handle)
        x1, y1, x2, y2 = rect
        width = x2 - x1
        height = y2 - y1

        return {
            'x': x1,
            'y': y1,
            'width': width,
            'height': height,
            'center_x': x1 + width // 2,
            'center_y': y1 + height // 2
        }
    except Exception as e:
        logger.error(f"Error getting game window rect: {e}")
        return None

# Convert screen coordinates to game window coordinates
def screen_to_game_coords(x, y):
    """Convert screen coordinates to game window coordinates."""
    if not game_window_settings['target_game_only']:
        return x, y

    window_rect = get_game_window_rect()
    if not window_rect:
        return x, y

    # Check if the coordinates are within the game window
    if (x < window_rect['x'] or x > window_rect['x'] + window_rect['width'] or
        y < window_rect['y'] or y > window_rect['y'] + window_rect['height']):
        # If outside the game window, convert to the center of the game window
        return window_rect['center_x'], window_rect['center_y']

    return x, y

# Revival button clicking function
def click_revival_button():
    """Click the revival button using template matching.
    Returns True if successful, False otherwise."""
    try:
        # Check if template exists
        template_path = death_detection_settings['template_path']
        if not os.path.exists(template_path):
            logger.warning(f"Revival button template not found: {template_path}")
            return False
        
        logger.info(f"Looking for revival button using template: {template_path}")
        
        # Get game window position
        window_rect = get_game_window_rect()
        if not window_rect:
            logger.warning("Could not get game window rect for button click")
            return False
        
        # Take screenshot of the game window
        screenshot = pyautogui.screenshot(region=(
            window_rect['x'], 
            window_rect['y'], 
            window_rect['width'], 
            window_rect['height']
        ))
        
        # Convert to OpenCV format
        screenshot = np.array(screenshot)
        screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)
        
        # Load the button template
        template = cv2.imread(template_path)
        
        if template is None:
            logger.error(f"Failed to load template image: {template_path}")
            return False
        
        # Template matching
        result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        logger.info(f"Template matching result: confidence = {max_val}")
        
        # Only proceed if confidence is above the threshold
        if max_val >= death_detection_settings['confidence']:
            # Get button position
            button_x = window_rect['x'] + max_loc[0] + template.shape[1] // 2
            button_y = window_rect['y'] + max_loc[1] + template.shape[0] // 2
            
            # Make sure game window is in focus
            if game_window_handle:
                win32gui.SetForegroundWindow(game_window_handle)
                time.sleep(0.1)  # Small delay to ensure window is focused
            
            # Calculate position relative to game window
            rel_x = button_x - window_rect['x']
            rel_y = button_y - window_rect['y']
            
            logger.info(f"Clicking revival button at position: ({button_x}, {button_y})")
            
            # Make sure game window is active and in foreground
            if game_window_handle:
                try:
                    # Bring window to foreground
                    win32gui.SetForegroundWindow(game_window_handle)
                    # Wait for window to be in foreground
                    time.sleep(0.3)
                except Exception as e:
                    logger.error(f"Error activating game window: {e}")
            
            # More reliable mouse clicking methods
            # Method 1: Win32 mouse_event (most reliable for games)
            try:
                # First move cursor to the position
                win32api.SetCursorPos((button_x, button_y))
                time.sleep(0.1)  # Wait for cursor to move
                
                # Send mouse down and up events with delay between
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                time.sleep(0.1)  # Hold button down briefly
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                
                logger.info(f"Clicked revival button at ({button_x}, {button_y}) using win32api.mouse_event")
            except Exception as e:
                logger.error(f"Win32 mouse_event click failed: {e}")
                
                # Try fallback methods if first method fails
                try:
                    # Method 2: Use ctypes for direct input
                    ctypes.windll.user32.SetCursorPos(int(button_x), int(button_y))
                    time.sleep(0.1)
                    ctypes.windll.user32.mouse_event(0x0002, 0, 0, 0, 0)  # MOUSEEVENTF_LEFTDOWN
                    time.sleep(0.1)
                    ctypes.windll.user32.mouse_event(0x0004, 0, 0, 0, 0)  # MOUSEEVENTF_LEFTUP
                    logger.info(f"Clicked revival button using ctypes direct input")
                except Exception as e2:
                    logger.error(f"Ctypes mouse click failed: {e2}")
                    
                    # Method 3: PyAutoGUI as last resort
                    try:
                        pyautogui.moveTo(button_x, button_y, duration=0.1)
                        pyautogui.click()
                        logger.info(f"Clicked revival button using PyAutoGUI")
                    except Exception as e3:
                        logger.error(f"All click methods failed: {e3}")
            
            # Add a small delay after clicking to allow game to process the click
            time.sleep(0.5)
            
            return True
        else:
            # If match confidence is below threshold, don't click at all
            logger.warning("Revival button template not found with sufficient confidence")
            if max_val > 0:
                logger.info(f"Match confidence {max_val} is below threshold {death_detection_settings['confidence']}")
                logger.info("Not clicking as match quality is insufficient")
            return False
    except Exception as e:
        logger.error(f"Error clicking revival button: {e}")
        return False

# Function to capture revival button template
def capture_revival_button_template():
    """Capture and save the revival button template using the current mouse position.
    Returns True if successful, False otherwise."""
    try:
        # Create templates directory if it doesn't exist
        templates_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "templates")
        os.makedirs(templates_dir, exist_ok=True)
        
        # Get mouse position
        x, y = pyautogui.position()
        
        # Capture a region around the cursor for the button
        template_width, template_height = 200, 40  # Adjust as needed
        region = (x - template_width // 2, y - template_height // 2, template_width, template_height)
        
        screenshot = pyautogui.screenshot(region=region)
        
        # Save the template
        template_path = death_detection_settings['template_path']
        screenshot.save(template_path)
        
        logger.info(f"Revival button template saved to: {template_path}")
        return True
    except Exception as e:
        logger.error(f"Error capturing revival button template: {e}")
        return False

# Death detection function
def detect_death():
    """Detect if character is dead by checking for the revival button using template matching."""
    global death_detection_active, game_window_handle

    if not HAVE_PYAUTOGUI:
        logger.warning("PyAutoGUI not available, death detection disabled")
        return

    # Create templates directory if it doesn't exist
    templates_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "templates")
    os.makedirs(templates_dir, exist_ok=True)

    logger.info("Death detection started")

    while death_detection_active:
        try:
            if death_detection_settings['enabled']:
                current_time = time.time()

                # Don't check too frequently to avoid multiple detections
                if current_time - death_detection_settings['last_death_time'] < death_detection_settings['detection_cooldown']:
                    time.sleep(1)
                    continue

                # If targeting only the game window, find it first
                if game_window_settings['target_game_only']:
                    if not game_window_handle:
                        if not find_game_window():
                            # If game window not found, skip this check
                            logger.warning("Game window not found, skipping death detection check")
                            time.sleep(1)
                            continue

                    # Get game window position and size
                    window_rect = get_game_window_rect()
                    if not window_rect:
                        # If can't get window rect, skip this check
                        logger.warning("Could not get game window rect, skipping death detection check")
                        time.sleep(1)
                        continue
                
                # Check for revival button using template matching
                if death_detection_settings['use_template_matching']:
                    if os.path.exists(death_detection_settings['template_path']):
                        if click_revival_button():
                            logger.info("Revival button found and clicked successfully")
                            death_detection_settings['last_death_time'] = current_time
                    else:
                        logger.warning("Template file not found. Please capture a template of the revival button.")
                
                # Wait between checks
                time.sleep(death_detection_settings['check_interval'])
            else:
                # Death detection disabled, just wait
                time.sleep(1)
        except Exception as e:
            logger.error(f"Error in death detection: {e}")
            time.sleep(1)

    logger.info("Death detection stopped")

# Save recorded actions and settings to file
def save_actions(filename):
    """Save recorded actions and settings to a file."""
    if not recorded_actions:
        messagebox.showwarning("Warning", "No actions recorded")
        return False

    try:
        # Create a data structure with both actions and settings
        data = {
            'actions': recorded_actions,
            'death_detection': {
                'enabled': death_detection_settings['enabled'],
                'check_interval': death_detection_settings['check_interval'],
                'click_position': death_detection_settings['click_position'],
                'color': death_detection_settings['color'],
                'threshold': death_detection_settings['threshold'],
                'region': death_detection_settings['region']
            },
            'game_window': {
                'target_game_only': game_window_settings['target_game_only'],
                'window_title': game_window_settings['window_title'],
                'stay_on_top': game_window_settings['stay_on_top'],
                'prevent_focus_steal': game_window_settings['prevent_focus_steal'],
                'use_overlay_mode': game_window_settings['use_overlay_mode']
            }
        }

        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        messagebox.showerror("Error", f"Failed to save actions: {e}")
        return False

# Load recorded actions and settings from file
def load_actions(filename):
    """Load recorded actions and settings from a file."""
    global recorded_actions, death_detection_settings

    try:
        with open(filename, 'r') as f:
            data = json.load(f)

        # Check if this is a new format file with both actions and settings
        if isinstance(data, dict) and 'actions' in data:
            recorded_actions = data['actions']

            # Load death detection settings if available
            if 'death_detection' in data:
                dd = data['death_detection']
                death_detection_settings['enabled'] = dd.get('enabled', False)
                death_detection_settings['check_interval'] = dd.get('check_interval', 2.0)
                death_detection_settings['click_position'] = dd.get('click_position', None)
                death_detection_settings['color'] = dd.get('color', (41, 76, 122))
                death_detection_settings['threshold'] = dd.get('threshold', 500)
                death_detection_settings['region'] = dd.get('region', None)

            # Load game window settings if available
            if 'game_window' in data:
                gw = data['game_window']
                game_window_settings['target_game_only'] = gw.get('target_game_only', True)
                game_window_settings['window_title'] = gw.get('window_title', 'Rappelz')
                game_window_settings['stay_on_top'] = gw.get('stay_on_top', False)
                game_window_settings['prevent_focus_steal'] = gw.get('prevent_focus_steal', True)
                game_window_settings['use_overlay_mode'] = gw.get('use_overlay_mode', True)
        else:
            # Old format file with just actions
            recorded_actions = data

        return True
    except Exception as e:
        messagebox.showerror("Error", f"Failed to load actions: {e}")
        return False

# GUI class
class MacroRecorderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Rappelz Macro Recorder")
        self.root.resizable(True, True)

        # Set minimum size
        self.root.minsize(600, 700)

        # Minimalist mode variables
        self.minimalist_mode = False
        self.minimalist_window = None

        # Default minimalist window settings
        self.minimalist_settings = {
            'x': 10,
            'y': 10,
            'transparency': 0.9
        }

        # Load saved minimalist settings if available
        self._load_minimalist_settings()

        # Apply window settings
        if game_window_settings['stay_on_top']:
            self.root.attributes("-topmost", True)
        else:
            self.root.attributes("-topmost", False)

        # Apply focus prevention if enabled
        if game_window_settings['prevent_focus_steal']:
            try:
                # Set window style to prevent focus stealing
                hwnd = int(self.root.winfo_id())

                # Get current window style
                style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)

                # Add the WS_EX_NOACTIVATE style to prevent focus stealing
                new_style = style | win32con.WS_EX_NOACTIVATE

                # Set the new style
                win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, new_style)

                logger.info("Applied focus prevention settings on startup")
            except Exception as e:
                logger.error(f"Error applying focus prevention on startup: {e}")

        # Set theme colors - modern dark theme
        self.colors = {
            'bg': '#2c3e50',             # Dark blue-gray background
            'frame_bg': '#34495e',       # Slightly lighter blue-gray
            'accent': '#3498db',         # Bright blue accent
            'accent_dark': '#2980b9',    # Darker blue
            'text': '#ecf0f1',           # Light gray text
            'text_dark': '#bdc3c7',      # Darker gray text
            'success': '#2ecc71',        # Green
            'warning': '#f39c12',        # Orange
            'error': '#e74c3c',          # Red
            'highlight': '#9b59b6',      # Purple highlight
            'secondary': '#1abc9c',      # Teal secondary accent
            'tertiary': '#e67e22'        # Dark orange tertiary accent
        }

        # Configure style
        self._configure_style()

        # Create GUI
        self._create_widgets()

        # Set up keyboard and mouse hooks for recording
        self._setup_hooks()
        
        # Attempt to load the last used macro
        self.root.after(1000, self._load_last_macro)  # Delay slightly to ensure UI is fully loaded

    def _create_widgets(self):
        """Create the application widgets using a notebook-based approach instead of scrolling."""
        # Configure root grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # Create a notebook for tabbed interface - more stable than scrolling
        notebook = ttk.Notebook(self.root)
        notebook.grid(row=0, column=0, sticky="nsew")

        # Main content frame
        main_frame = ttk.Frame(notebook, padding="20")
        main_frame.columnconfigure(0, weight=1)

        # Add the main frame to the notebook
        notebook.add(main_frame, text="Main")

        # Create additional tabs for different sections
        recording_tab = ttk.Frame(notebook, padding="20")
        recording_tab.columnconfigure(0, weight=1)
        notebook.add(recording_tab, text="Recording")

        playback_tab = ttk.Frame(notebook, padding="20")
        playback_tab.columnconfigure(0, weight=1)
        notebook.add(playback_tab, text="Playback")

        # New dedicated tab for death detection
        death_detection_tab = ttk.Frame(notebook, padding="20")
        death_detection_tab.columnconfigure(0, weight=1)
        notebook.add(death_detection_tab, text="Death Detection")
        
        # Use the dedicated function to create the death detection tab content
        self._create_death_detection_tab(death_detection_tab)

        settings_tab = ttk.Frame(notebook, padding="20")
        settings_tab.columnconfigure(0, weight=1)
        notebook.add(settings_tab, text="Settings")

        # App title in main tab
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        title_frame.columnconfigure(1, weight=1)

        # App logo/icon (using a more detailed icon)
        logo_canvas = tk.Canvas(title_frame, width=32, height=32, highlightthickness=0, bg=self.colors['bg'])
        logo_canvas.grid(row=0, column=0, padx=(0, 10))

        # Draw a more detailed icon
        # Outer circle
        logo_canvas.create_oval(2, 2, 30, 30, outline=self.colors['accent_dark'], width=2, fill=self.colors['bg'])
        # Inner circle
        logo_canvas.create_oval(8, 8, 24, 24, outline=self.colors['accent'], width=2, fill=self.colors['accent'])
        # Mouse cursor
        logo_canvas.create_line(16, 6, 16, 26, fill="white", width=2)
        logo_canvas.create_line(6, 16, 26, 16, fill="white", width=2)

        # App title
        title_label = ttk.Label(
            title_frame,
            text="Rappelz Macro Recorder",
            font=('Arial', 16, 'bold'),
            foreground=self.colors['accent_dark']
        )
        title_label.grid(row=0, column=1, sticky="w")

        # Admin status
        admin_frame = ttk.Frame(title_frame)
        admin_frame.grid(row=0, column=2, sticky="e")

        admin_status = is_admin()
        admin_label = ttk.Label(
            admin_frame,
            text=f"{'Administrator ✓' if admin_status else 'Not Administrator ✗'}",
            style='Success.TLabel' if admin_status else 'Error.TLabel'
        )
        admin_label.grid(row=0, column=0, sticky="e")

        # Welcome message in main tab
        welcome_frame = ttk.Frame(main_frame, padding=20)
        welcome_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=20)

        welcome_text = (
            "Welcome to Rappelz Macro Recorder!\n\n"
            "This tool helps you automate repetitive tasks in Rappelz.\n\n"
            "• Use the Recording tab to record your actions\n"
            "• Use the Playback tab to replay your recorded actions\n"
            "• Use the Death Detection tab to set up automatic revival\n"
            "• Use the Settings tab to configure game window settings\n\n"
            "Hotkeys:\n"
            "• F8: Start/stop recording\n"
            "• F9: Start playback\n"
            "• F10: Toggle minimalist mode\n"
            "• F11: Stop playback"
        )

        welcome_label = ttk.Label(
            welcome_frame,
            text=welcome_text,
            font=('Arial', 11),
            justify="center",
            wraplength=500
        )
        welcome_label.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)

        # Recording tab content
        # Recording header
        recording_header = ttk.Label(recording_tab, text="Recording Controls", style="Heading.TLabel")
        recording_header.grid(row=0, column=0, sticky="w", padx=5, pady=(5, 15))

        # Recording frame
        recording_frame = ttk.LabelFrame(recording_tab, text="", padding=(15, 10))
        recording_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
        recording_frame.columnconfigure(0, weight=1)
        recording_frame.columnconfigure(1, weight=1)
        recording_frame.columnconfigure(2, weight=1)

        # Recording status and controls in a horizontal frame
        controls_frame = ttk.Frame(recording_frame)
        controls_frame.grid(row=0, column=0, columnspan=3, sticky="ew", padx=5, pady=5)
        controls_frame.columnconfigure(0, weight=1)
        controls_frame.columnconfigure(1, weight=1)

        # Left side - status indicator
        status_frame = ttk.Frame(controls_frame)
        status_frame.grid(row=0, column=0, sticky="w")

        # Status indicator with better visual
        status_box = ttk.Frame(status_frame, padding=5, relief="solid", borderwidth=1)
        status_box.grid(row=0, column=0, sticky="w", padx=5, pady=5)

        self.recording_var = tk.StringVar(value="Not Recording")

        # Status indicator layout
        status_indicator_frame = ttk.Frame(status_box)
        status_indicator_frame.grid(row=0, column=0, sticky="w")

        # Larger, more visible status indicator
        self.status_indicator = tk.Canvas(status_indicator_frame, width=20, height=20,
                                        highlightthickness=0, bg=self.colors['error'])
        self.status_indicator.grid(row=0, column=0, padx=(0, 10))

        # Recording status text with better font
        recording_label = ttk.Label(status_indicator_frame, textvariable=self.recording_var,
                                  style="Error.TLabel", font=('Arial', 11, 'bold'))
        recording_label.grid(row=0, column=1, sticky="w")

        # Right side - buttons
        button_frame = ttk.Frame(controls_frame)
        button_frame.grid(row=0, column=1, sticky="e")

        # Button container for better layout
        buttons_container = ttk.Frame(button_frame)
        buttons_container.grid(row=0, column=0, sticky="e", padx=5, pady=5)

        # Record button - larger and more prominent (using tk.Button instead of ttk.Button for better styling)
        self.record_button = tk.Button(buttons_container, text="Start Recording (F8)",
                                     command=self._toggle_recording,
                                     bg=self.colors['accent'], fg="white",
                                     font=('Arial', 10, 'bold'),
                                     width=20, relief=tk.RAISED, borderwidth=2)
        self.record_button.grid(row=0, column=0, padx=5, pady=5)
        tooltip.add_tooltip(self.record_button,
                          "Start recording your keyboard and mouse actions.\nPress F8 or click this button again to stop recording.")

        # Clear button
        clear_button = tk.Button(buttons_container, text="Clear Recording",
                               command=self._clear_recording,
                               bg=self.colors['warning'], fg="white",
                               font=('Arial', 10, 'bold'),
                               width=20, relief=tk.RAISED, borderwidth=2)
        clear_button.grid(row=0, column=1, padx=5, pady=5)
        tooltip.add_tooltip(clear_button,
                          "Clear all recorded actions. This cannot be undone.")

        # Recorded actions section
        actions_frame = ttk.Frame(recording_tab)
        actions_frame.grid(row=2, column=0, sticky="nsew", padx=5, pady=15)
        actions_frame.columnconfigure(0, weight=1)
        actions_frame.rowconfigure(1, weight=1)

        # Actions header with count
        self.actions_header_var = tk.StringVar(value="Recorded Actions: 0")
        ttk.Label(actions_frame, textvariable=self.actions_header_var, font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky="w", padx=5, pady=5)

        # Main actions container with listbox and buttons
        actions_container = ttk.Frame(actions_frame)
        actions_container.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
        actions_container.columnconfigure(0, weight=1)
        actions_container.rowconfigure(0, weight=1)

        # Enhanced listbox frame with better styling
        listbox_frame = ttk.Frame(actions_container, borderwidth=2, relief="groove")
        listbox_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        listbox_frame.columnconfigure(0, weight=1)
        listbox_frame.rowconfigure(0, weight=1)

        # Create a canvas for custom drawing and better styling
        actions_canvas_frame = ttk.Frame(listbox_frame)
        actions_canvas_frame.grid(row=0, column=0, sticky="nsew")
        actions_canvas_frame.columnconfigure(0, weight=1)
        actions_canvas_frame.rowconfigure(0, weight=1)

        # Recorded actions listbox with enhanced styling
        self.actions_listbox = tk.Listbox(
            actions_canvas_frame,
            height=15,  # Increased height for tab layout
            borderwidth=0,
            highlightthickness=0,
            font=('Consolas', 11),  # Slightly larger monospaced font for better readability
            bg=self.colors['frame_bg'],  # Dark background
            fg=self.colors['text'],  # Light text
            selectbackground=self.colors['accent'],  # Highlight color
            selectforeground='white',  # Text color when selected
            activestyle='dotbox',  # Better active item indicator
            exportselection=False,  # Keep selection when focus changes
            selectmode=tk.BROWSE,  # Single selection mode
            relief=tk.FLAT  # Flat appearance for modern look
        )
        self.actions_listbox.grid(row=0, column=0, sticky="nsew")

        # Add custom styling to make it look more modern
        self.actions_listbox.config(
            selectborderwidth=0,  # No border when selected
            highlightcolor=self.colors['accent_dark']  # Highlight color
        )

        # Bind double-click to edit action
        self.actions_listbox.bind("<Double-1>", self._edit_selected_action)

        # Bind right-click to show context menu
        self.actions_listbox.bind("<Button-3>", self._show_action_context_menu)

        # Bind single click for selection
        self.actions_listbox.bind("<ButtonRelease-1>", self._on_action_select)

        # Enhanced scrollbar with custom styling
        scrollbar = ttk.Scrollbar(actions_canvas_frame, orient="vertical", command=self.actions_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky="ns")
        self.actions_listbox.configure(yscrollcommand=scrollbar.set)

        # Enhanced action editing buttons with icons and better styling
        edit_buttons_frame = ttk.Frame(actions_container)
        edit_buttons_frame.grid(row=0, column=1, sticky="ns", padx=5, pady=5)

        # Button style configuration
        button_width = 12
        button_height = 1
        button_font = ('Arial', 9, 'bold')
        button_relief = tk.RAISED
        button_border = 2
        button_padx = 5
        button_pady = 5

        # Function to create a button with an icon
        def create_icon_button(parent, text, icon, command, bg_color, row):
            button_frame = ttk.Frame(parent)
            button_frame.grid(row=row, column=0, padx=button_padx, pady=button_pady, sticky="ew")

            # Create the button
            button = tk.Button(
                button_frame,
                text=text,
                command=command,
                bg=bg_color,
                fg="white",
                font=button_font,
                width=button_width,
                height=button_height,
                relief=button_relief,
                borderwidth=button_border,
                compound=tk.LEFT,  # Icon on the left, text on the right
                padx=5
            )
            button.grid(row=0, column=0, sticky="ew")

            # Draw icon on the button
            if icon == "edit":
                # Edit icon (pencil)
                button.config(text="  Edit")
                button_icon = "✏️"
            elif icon == "delete":
                # Delete icon (trash)
                button.config(text="  Delete")
                button_icon = "🗑️"
            elif icon == "up":
                # Up arrow
                button.config(text="  Move Up")
                button_icon = "↑"
            elif icon == "down":
                # Down arrow
                button.config(text="  Move Down")
                button_icon = "↓"
            elif icon == "add":
                # Plus sign
                button.config(text="  Add New")
                button_icon = "+"
            elif icon == "clear":
                # Clear icon (X)
                button.config(text="  Clear All")
                button_icon = "✖"

            # Add icon text at the beginning of the button
            button.config(text=f"{button_icon} {button.cget('text')}")

            # Add tooltip
            if icon == "edit":
                tooltip.add_tooltip(button, "Edit the selected action (double-click also works)")
            elif icon == "delete":
                tooltip.add_tooltip(button, "Delete the selected action")
            elif icon == "up":
                tooltip.add_tooltip(button, "Move the selected action up in the list")
            elif icon == "down":
                tooltip.add_tooltip(button, "Move the selected action down in the list")
            elif icon == "add":
                tooltip.add_tooltip(button, "Add a new action manually")
            elif icon == "clear":
                tooltip.add_tooltip(button, "Clear all recorded actions")

            return button

        # Create all buttons with icons
        create_icon_button(
            edit_buttons_frame, "Edit", "edit",
            self._edit_selected_action, self.colors['accent'], 0
        )

        create_icon_button(
            edit_buttons_frame, "Delete", "delete",
            self._delete_selected_action, self.colors['error'], 1
        )

        create_icon_button(
            edit_buttons_frame, "Move Up", "up",
            self._move_action_up, self.colors['accent_dark'], 2
        )

        create_icon_button(
            edit_buttons_frame, "Move Down", "down",
            self._move_action_down, self.colors['accent_dark'], 3
        )

        create_icon_button(
            edit_buttons_frame, "Add New", "add",
            self._add_new_action, self.colors['success'], 4
        )

        # Add Clear All button
        create_icon_button(
            edit_buttons_frame, "Clear All", "clear",
            self._clear_recording, self.colors['error'], 5
        )

        # Instructions for recording
        recording_instructions = ttk.Frame(recording_tab, padding=10, relief="solid", borderwidth=1)
        recording_instructions.grid(row=3, column=0, sticky="ew", padx=5, pady=15)

        instructions_text = (
            "Recording Instructions:\n"
            "1. Press F8 or click 'Start Recording' to begin recording\n"
            "2. Perform the actions you want to record (key presses and mouse clicks)\n"
            "3. Press F8 again to stop recording\n"
            "4. Your recorded actions will appear in the list above"
        )

        ttk.Label(recording_instructions, text=instructions_text, wraplength=500, justify="left").grid(
            row=0, column=0, sticky="w", padx=5, pady=5
        )

        # Playback tab content
        # Playback header
        playback_header = ttk.Label(playback_tab, text="Playback Controls", style="Heading.TLabel")
        playback_header.grid(row=0, column=0, sticky="w", padx=5, pady=(5, 15))

        # Playback frame
        playback_frame = ttk.LabelFrame(playback_tab, text="", padding=(15, 10))
        playback_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
        playback_frame.columnconfigure(0, weight=1)
        playback_frame.columnconfigure(1, weight=1)

        # Left column - settings
        settings_frame = ttk.Frame(playback_frame)
        settings_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)

        # Infinite repeat option with styled checkbox
        repeat_option_frame = ttk.Frame(settings_frame)
        repeat_option_frame.grid(row=0, column=0, sticky="w", pady=(0, 10))

        self.infinite_var = tk.BooleanVar(value=False)
        infinite_check = ttk.Checkbutton(repeat_option_frame, text="Infinite Repeat", variable=self.infinite_var,
                                       command=self._toggle_infinite, style="TCheckbutton")
        infinite_check.grid(row=0, column=0, sticky="w")

        # Settings grid
        settings_grid = ttk.Frame(settings_frame)
        settings_grid.grid(row=1, column=0, sticky="w")

        # Repeat count with label and spinbox in a nicer layout
        ttk.Label(settings_grid, text="Repeat Count:", font=('Arial', 10)).grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.repeat_count_var = tk.IntVar(value=1)
        count_frame = ttk.Frame(settings_grid)
        count_frame.grid(row=0, column=1, sticky="w", padx=5, pady=5)

        self.repeat_spinbox = ttk.Spinbox(count_frame, from_=1, to=1000, increment=1,
                                        textvariable=self.repeat_count_var, width=10)
        self.repeat_spinbox.grid(row=0, column=0, sticky="w")

        # Repeat delay with label and spinbox
        ttk.Label(settings_grid, text="Delay Between Repeats (s):", font=('Arial', 10)).grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.repeat_delay_var = tk.DoubleVar(value=0.5)
        delay_frame = ttk.Frame(settings_grid)
        delay_frame.grid(row=1, column=1, sticky="w", padx=5, pady=5)

        delay_spinbox = ttk.Spinbox(delay_frame, from_=0, to=60, increment=0.5,
                                  textvariable=self.repeat_delay_var, width=10)
        delay_spinbox.grid(row=0, column=0, sticky="w")

        # Right column - buttons
        button_frame = ttk.Frame(playback_frame)
        button_frame.grid(row=0, column=1, sticky="e", padx=5, pady=5)

        # Button container with vertical layout
        buttons_container = ttk.Frame(button_frame)
        buttons_container.grid(row=0, column=0, sticky="e")

        # Playback button (using tk.Button instead of ttk.Button for better styling)
        self.playback_button = tk.Button(buttons_container, text="Start Playback (F9)",
                                       command=self._toggle_playback,
                                       bg=self.colors['success'], fg="white",
                                       font=('Arial', 10, 'bold'),
                                       width=20, relief=tk.RAISED, borderwidth=2)
        self.playback_button.grid(row=0, column=0, padx=5, pady=5)
        tooltip.add_tooltip(self.playback_button,
                          "Play back your recorded actions.\nUses the repeat and delay settings above.")

        # Stop button
        stop_button = tk.Button(buttons_container, text="Stop (F11)",
                              command=self._stop_playback,
                              bg=self.colors['error'], fg="white",
                              font=('Arial', 10, 'bold'),
                              width=20, relief=tk.RAISED, borderwidth=2)
        stop_button.grid(row=1, column=0, padx=5, pady=5)
        tooltip.add_tooltip(stop_button,
                          "Stop the current playback immediately.\nYou can also press F11 to stop.")

        # Instructions for playback
        playback_instructions = ttk.Frame(playback_tab, padding=10, relief="solid", borderwidth=1)
        playback_instructions.grid(row=2, column=0, sticky="ew", padx=5, pady=15)

        instructions_text = (
            "Playback Instructions:\n"
            "1. Set the number of times to repeat the recorded actions\n"
            "2. Or check 'Infinite Repeat' to repeat indefinitely\n"
            "3. Set the delay between repetitions if needed\n"
            "4. Press F9 or click 'Start Playback' to begin\n"
            "5. Press F11 or click 'Stop' to stop playback at any time"
        )

        ttk.Label(playback_instructions, text=instructions_text, wraplength=500, justify="left").grid(
            row=0, column=0, sticky="w", padx=5, pady=5
        )

        # Settings tab content
        # Settings header
        settings_header = ttk.Label(settings_tab, text="Game Settings", style="Heading.TLabel")
        settings_header.grid(row=0, column=0, sticky="w", padx=5, pady=(5, 15))

        # Game Window Settings frame
        game_window_frame = ttk.LabelFrame(settings_tab, text="Game Window", padding=(15, 10))
        game_window_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
        game_window_frame.columnconfigure(0, weight=1)
        game_window_frame.columnconfigure(1, weight=1)

        # Left column - settings
        gw_settings_frame = ttk.Frame(game_window_frame)
        gw_settings_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)

        # Target game only option with icon/indicator
        target_frame = ttk.Frame(gw_settings_frame)
        target_frame.grid(row=0, column=0, sticky="w", pady=(0, 10))

        self.target_game_var = tk.BooleanVar(value=game_window_settings['target_game_only'])

        # Create a nice checkbox with description
        target_check_frame = ttk.Frame(target_frame)
        target_check_frame.grid(row=0, column=0, sticky="w")

        target_game_check = ttk.Checkbutton(target_check_frame, text="Target Game Window Only",
                                          variable=self.target_game_var,
                                          command=self._toggle_target_game,
                                          style="TCheckbutton")
        target_game_check.grid(row=0, column=0, sticky="w")

        # Add a description below the checkbox
        description = "When enabled, actions will only affect the game window, allowing you to use other applications"
        ttk.Label(target_frame, text=description, foreground="#666666", wraplength=400).grid(row=1, column=0, sticky="w", padx=(20, 0))

        # Prevent focus stealing option
        focus_frame = ttk.Frame(gw_settings_frame)
        focus_frame.grid(row=1, column=0, sticky="w", pady=10)

        self.prevent_focus_var = tk.BooleanVar(value=game_window_settings['prevent_focus_steal'])

        # Create a nice checkbox with description
        focus_check_frame = ttk.Frame(focus_frame)
        focus_check_frame.grid(row=0, column=0, sticky="w")

        focus_check = ttk.Checkbutton(focus_check_frame, text="Prevent Focus Stealing",
                                    variable=self.prevent_focus_var,
                                    command=self._toggle_focus_steal,
                                    style="TCheckbutton")
        focus_check.grid(row=0, column=0, sticky="w")

        # Add a description below the checkbox
        focus_desc = "When enabled, the app won't steal focus from the game when clicked"
        ttk.Label(focus_frame, text=focus_desc, foreground="#666666", wraplength=400).grid(row=1, column=0, sticky="w", padx=(20, 0))

        # Stay on top option
        top_frame = ttk.Frame(gw_settings_frame)
        top_frame.grid(row=2, column=0, sticky="w", pady=10)

        self.stay_on_top_var = tk.BooleanVar(value=game_window_settings['stay_on_top'])

        # Create a nice checkbox with description
        top_check_frame = ttk.Frame(top_frame)
        top_check_frame.grid(row=0, column=0, sticky="w")

        top_check = ttk.Checkbutton(top_check_frame, text="Stay On Top",
                                  variable=self.stay_on_top_var,
                                  command=self._toggle_stay_on_top,
                                  style="TCheckbutton")
        top_check.grid(row=0, column=0, sticky="w")

        # Add a description below the checkbox
        top_desc = "When enabled, the app will stay on top of other windows"
        ttk.Label(top_frame, text=top_desc, foreground="#666666", wraplength=400).grid(row=1, column=0, sticky="w", padx=(20, 0))

        # Overlay mode option
        overlay_frame = ttk.Frame(gw_settings_frame)
        overlay_frame.grid(row=3, column=0, sticky="w", pady=10)

        self.overlay_mode_var = tk.BooleanVar(value=game_window_settings['use_overlay_mode'])

        # Create a nice checkbox with description
        overlay_check_frame = ttk.Frame(overlay_frame)
        overlay_check_frame.grid(row=0, column=0, sticky="w")

        overlay_check = ttk.Checkbutton(overlay_check_frame, text="Use Overlay Mode",
                                      variable=self.overlay_mode_var,
                                      command=self._toggle_overlay_mode,
                                      style="TCheckbutton")
        overlay_check.grid(row=0, column=0, sticky="w")

        # Add a description below the checkbox
        overlay_desc = "When enabled, uses a transparent overlay that doesn't steal focus (requires restart)"
        ttk.Label(overlay_frame, text=overlay_desc, foreground="#666666", wraplength=400).grid(row=1, column=0, sticky="w", padx=(20, 0))

        # Game window title with better layout
        title_frame = ttk.Frame(gw_settings_frame)
        title_frame.grid(row=4, column=0, sticky="w", pady=10)

        ttk.Label(title_frame, text="Game Window Title:", font=('Arial', 10)).grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.window_title_var = tk.StringVar(value=game_window_settings['window_title'])
        window_title_entry = ttk.Entry(title_frame, textvariable=self.window_title_var, width=25)
        window_title_entry.grid(row=0, column=1, sticky="w", padx=5, pady=5)

        # Right column - find button
        find_button_frame = ttk.Frame(game_window_frame)
        find_button_frame.grid(row=0, column=1, sticky="e", padx=5, pady=5)

        # Find window button (using tk.Button instead of ttk.Button for better styling)
        find_window_button = tk.Button(find_button_frame, text="Find Game Window",
                                     command=self._find_game_window,
                                     bg=self.colors['accent'], fg="white",
                                     font=('Arial', 10, 'bold'),
                                     width=20, relief=tk.RAISED, borderwidth=2)
        find_window_button.grid(row=0, column=0, padx=5, pady=5)

        # Apply settings button
        apply_settings_button = tk.Button(find_button_frame, text="Apply All Settings",
                                       command=self._apply_game_settings,
                                       bg=self.colors['success'], fg="white",
                                       font=('Arial', 10, 'bold'),
                                       width=20, relief=tk.RAISED, borderwidth=2)
        apply_settings_button.grid(row=1, column=0, padx=5, pady=5)

        # Note about Death Detection
        death_note_frame = ttk.Frame(settings_tab, padding=10)
        death_note_frame.grid(row=2, column=0, sticky="ew", padx=5, pady=(15, 5))

        # Create a note about the Death Detection tab
        note_text = "Death Detection settings have been moved to their own dedicated tab."
        note_label = ttk.Label(death_note_frame, text=note_text, style="Highlight.TLabel", font=('Arial', 11))
        note_label.grid(row=0, column=0, sticky="w", padx=5, pady=5)

        # Save/Load section in main tab
        save_load_frame = ttk.LabelFrame(main_frame, text="Save & Load", padding=(15, 10))
        save_load_frame.grid(row=2, column=0, sticky="nsew", padx=5, pady=20)

        # Button container with horizontal layout
        button_container = ttk.Frame(save_load_frame)
        button_container.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        button_container.columnconfigure(0, weight=1)
        button_container.columnconfigure(1, weight=1)

        # Save button with icon
        save_frame = ttk.Frame(button_container)
        save_frame.grid(row=0, column=0, sticky="e", padx=20, pady=5)

        # Save icon
        save_icon = tk.Canvas(save_frame, width=24, height=24, highlightthickness=0, bg=self.colors['bg'])
        save_icon.create_rectangle(5, 5, 19, 19, outline=self.colors['accent'], width=2)
        save_icon.create_rectangle(8, 8, 16, 16, fill=self.colors['accent'])
        save_icon.grid(row=0, column=0, padx=(0, 10))

        # Save button (using tk.Button instead of ttk.Button for better styling)
        save_button = tk.Button(save_frame, text="Save Macro",
                              command=self._save_macro,
                              bg=self.colors['accent'], fg="white",
                              font=('Arial', 10, 'bold'),
                              width=15, relief=tk.RAISED, borderwidth=2)
        save_button.grid(row=0, column=1, padx=5, pady=5)

        # Load button with icon
        load_frame = ttk.Frame(button_container)
        load_frame.grid(row=0, column=1, sticky="w", padx=20, pady=5)

        # Load icon
        load_icon = tk.Canvas(load_frame, width=24, height=24, highlightthickness=0, bg=self.colors['bg'])
        load_icon.create_rectangle(5, 5, 19, 19, outline=self.colors['accent'], width=2)
        load_icon.create_line(8, 12, 16, 12, fill=self.colors['accent'], width=2)
        load_icon.create_line(12, 8, 12, 16, fill=self.colors['accent'], width=2)
        load_icon.grid(row=0, column=0, padx=(0, 10))

        # Load button
        load_button = tk.Button(load_frame, text="Load Macro",
                              command=self._load_macro,
                              bg=self.colors['accent'], fg="white",
                              font=('Arial', 10, 'bold'),
                              width=15, relief=tk.RAISED, borderwidth=2)
        load_button.grid(row=0, column=1, padx=5, pady=5)

        # Death Detection tab content
        self._create_death_detection_tab(death_detection_tab)

        # Modern status bar at the bottom
        status_frame = ttk.Frame(self.root, relief=tk.FLAT, borderwidth=0)
        status_frame.grid(row=1, column=0, sticky="ew")
        status_frame.columnconfigure(0, weight=1)

        # Create a gradient effect for the status bar
        status_bg = tk.Canvas(status_frame, height=28, bg=self.colors['frame_bg'], highlightthickness=0)
        status_bg.grid(row=0, column=0, sticky="ew", columnspan=3)

        # Add a thin line at the top of the status bar
        status_bg.create_line(0, 0, 10000, 0, fill=self.colors['accent'], width=1)

        # Status message with icon
        status_container = ttk.Frame(status_bg, style='TFrame')
        status_container.place(x=10, y=4)

        # Status icon - circular with gradient
        self.status_icon = tk.Canvas(status_container, width=12, height=12, highlightthickness=0, bg=self.colors['frame_bg'])
        self.status_icon.grid(row=0, column=0, padx=(0, 5))

        # Draw a circular status indicator
        self.status_icon.create_oval(1, 1, 11, 11, fill=self.colors['success'], outline=self.colors['accent'])

        # Status message
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(status_container, textvariable=self.status_var, style="Status.TLabel")
        status_label.grid(row=0, column=1, sticky="w")

        # Action count in the middle
        action_container = ttk.Frame(status_bg)
        action_container.place(x=250, y=4)

        # Action count icon
        action_icon = tk.Canvas(action_container, width=12, height=12, highlightthickness=0, bg=self.colors['frame_bg'])
        action_icon.grid(row=0, column=0, padx=(0, 5))
        action_icon.create_rectangle(1, 1, 11, 11, fill=self.colors['secondary'], outline=self.colors['accent'])

        # Action count
        self.action_count_var = tk.StringVar(value=f"Actions: {len(recorded_actions)}")
        action_count_label = ttk.Label(action_container, textvariable=self.action_count_var, style="Status.TLabel")
        action_count_label.grid(row=0, column=1, sticky="w")

        # Hotkey reminder on the right
        hotkey_frame = ttk.Frame(status_bg)
        hotkey_frame.place(relx=0.98, y=4, anchor="e")

        hotkey_text = "F8: Record | F9: Play | F11: Stop"
        hotkey_label = ttk.Label(hotkey_frame, text=hotkey_text, style="Status.TLabel", font=('Arial', 8))
        hotkey_label.grid(row=0, column=0, sticky="e")

        # Update action count periodically
        def update_action_count():
            self.action_count_var.set(f"Actions: {len(recorded_actions)}")
            self.root.after(1000, update_action_count)

        update_action_count()

    # Removed _configure_canvas, _on_mousewheel, and _toggle_instructions methods as they're no longer needed

    def _configure_style(self):
        """Configure the ttk styles for the application."""
        style = ttk.Style()

        # Set the theme to a more compatible one
        try:
            style.theme_use('clam')  # Use clam theme which supports more styling options
        except:
            pass  # If theme not available, use default

        # Configure the root window background
        self.root.configure(background=self.colors['bg'])

        # Configure TFrame
        style.configure('TFrame', background=self.colors['bg'])

        # Configure TLabelframe
        style.configure('TLabelframe', background=self.colors['frame_bg'])
        style.configure('TLabelframe.Label', background=self.colors['bg'], foreground=self.colors['text'], font=('Arial', 10, 'bold'))

        # Configure TLabel
        style.configure('TLabel', background=self.colors['bg'], foreground=self.colors['text'], font=('Arial', 10))

        # Configure TNotebook (tabs)
        style.configure('TNotebook', background=self.colors['bg'], borderwidth=0)
        style.configure('TNotebook.Tab', background=self.colors['frame_bg'], foreground=self.colors['text'],
                        padding=[10, 5], font=('Arial', 10, 'bold'))
        style.map('TNotebook.Tab',
                 background=[('selected', self.colors['accent'])],
                 foreground=[('selected', 'white')])

        # Create custom button styles
        style.configure('TButton', background=self.colors['accent'], foreground='white',
                       font=('Arial', 10, 'bold'), borderwidth=2)
        style.map('TButton',
                 background=[('active', self.colors['accent_dark'])],
                 relief=[('pressed', 'sunken'), ('!pressed', 'raised')])

        # Accent button (blue)
        style.configure('Accent.TButton', background=self.colors['accent'], foreground='white',
                       font=('Arial', 10, 'bold'))
        style.map('Accent.TButton', background=[('active', self.colors['accent_dark'])])

        # Success button (green)
        style.configure('Success.TButton', background=self.colors['success'], foreground='white',
                       font=('Arial', 10, 'bold'))
        style.map('Success.TButton', background=[('active', '#27ae60')])  # Darker green

        # Warning button (orange)
        style.configure('Warning.TButton', background=self.colors['warning'], foreground='white',
                       font=('Arial', 10, 'bold'))
        style.map('Warning.TButton', background=[('active', '#d35400')])  # Darker orange

        # Error button (red)
        style.configure('Error.TButton', background=self.colors['error'], foreground='white',
                       font=('Arial', 10, 'bold'))
        style.map('Error.TButton', background=[('active', '#c0392b')])  # Darker red

        # Configure TCheckbutton
        style.configure('TCheckbutton', background=self.colors['bg'], foreground=self.colors['text'],
                       font=('Arial', 10))
        style.map('TCheckbutton',
                 background=[('active', self.colors['bg'])],
                 foreground=[('active', self.colors['accent'])])

        # Configure TRadiobutton
        style.configure('TRadiobutton', background=self.colors['bg'], foreground=self.colors['text'],
                       font=('Arial', 10))
        style.map('TRadiobutton',
                 background=[('active', self.colors['bg'])],
                 foreground=[('active', self.colors['accent'])])

        # Configure TEntry
        style.configure('TEntry', fieldbackground=self.colors['frame_bg'], foreground=self.colors['text'],
                       font=('Arial', 10), borderwidth=1)
        style.map('TEntry',
                 fieldbackground=[('focus', '#1c2833')],  # Darker background when focused
                 bordercolor=[('focus', self.colors['accent'])])  # Accent border when focused

        # Configure TSpinbox
        style.configure('TSpinbox', fieldbackground=self.colors['frame_bg'], foreground=self.colors['text'],
                       font=('Arial', 10), borderwidth=1, arrowsize=13)
        style.map('TSpinbox',
                 fieldbackground=[('focus', '#1c2833')],
                 bordercolor=[('focus', self.colors['accent'])])

        # Configure Combobox
        style.configure('TCombobox', fieldbackground=self.colors['frame_bg'], foreground=self.colors['text'],
                       font=('Arial', 10), arrowsize=13)
        style.map('TCombobox',
                 fieldbackground=[('focus', '#1c2833')],
                 bordercolor=[('focus', self.colors['accent'])])

        # Configure Listbox
        self.root.option_add('*TListbox*Background', self.colors['frame_bg'])
        self.root.option_add('*TListbox*foreground', self.colors['text'])
        self.root.option_add('*TListbox*font', ('Arial', 10))

        # Configure Heading style for section headers
        style.configure('Heading.TLabel', font=('Arial', 14, 'bold'), foreground=self.colors['accent'],
                       background=self.colors['bg'])

        # Configure Status style for status messages
        style.configure('Status.TLabel', font=('Arial', 10), foreground=self.colors['text_dark'],
                       background=self.colors['bg'])

        # Configure Success status
        style.configure('Success.TLabel', foreground=self.colors['success'], font=('Arial', 10, 'bold'),
                       background=self.colors['bg'])

        # Configure Warning status
        style.configure('Warning.TLabel', foreground=self.colors['warning'], font=('Arial', 10, 'bold'),
                       background=self.colors['bg'])

        # Configure Error status
        style.configure('Error.TLabel', foreground=self.colors['error'], font=('Arial', 10, 'bold'),
                       background=self.colors['bg'])

        # Configure Highlight label
        style.configure('Highlight.TLabel', foreground=self.colors['highlight'], font=('Arial', 10, 'bold'),
                       background=self.colors['bg'])

        # Configure Secondary label
        style.configure('Secondary.TLabel', foreground=self.colors['secondary'], font=('Arial', 10, 'bold'),
                       background=self.colors['bg'])

    def _setup_hooks(self):
        """Set up keyboard and mouse hooks for recording."""
        if HAVE_KEYBOARD:
            try:
                # Unregister any existing hotkeys to avoid duplicates
                keyboard.unhook_all()

                # Register hotkeys
                keyboard.add_hotkey('f8', self._toggle_recording)
                keyboard.add_hotkey('f9', self._toggle_playback)
                keyboard.add_hotkey('f11', self._stop_playback)
                keyboard.add_hotkey('f10', self._toggle_minimalist_mode)

                # Set up keyboard hook for recording
                # We'll handle this differently - only enable the hook when recording
                self.keyboard_hook = None

                logger.info("Keyboard hotkeys registered successfully")
            except Exception as e:
                logger.error(f"Error setting up keyboard hooks: {e}")
                messagebox.showwarning("Warning", f"Failed to set up keyboard hooks: {e}")

        # Create the overlay for mouse recording when needed
        self.overlay = None

    def _on_key_press(self, event):
        """Handle key press events during recording."""
        global recording

        # Ignore hotkeys
        if event.name in ['f8', 'f9', 'f11']:
            return

        if recording:
            try:
                # Record the key press
                if record_key_press(event.name):
                    # Update the actions list in the UI
                    self._update_actions_list()

                    # Update status
                    self.status_var.set(f"Recorded key press: {event.name}")
                else:
                    logger.warning(f"Failed to record key press: {event.name}")
            except Exception as e:
                logger.error(f"Error handling key press: {e}")

    def _create_mouse_recorder(self):
        """Create a transparent overlay for mouse recording."""
        # Instead of using an overlay that captures clicks, we'll use a different approach
        # We'll create a small floating indicator that doesn't interfere with the game

        # Create a small window that will show recording status
        self.overlay = tk.Toplevel(self.root)

        # Make it small and position it in the corner
        self.overlay.geometry("300x80+10+10")

        # Make it semi-transparent and remove window decorations
        self.overlay.attributes("-alpha", 0.7)
        self.overlay.attributes("-topmost", True)
        self.overlay.overrideredirect(True)  # Remove window decorations

        # Configure the window
        self.overlay.configure(bg="red")

        # Add a label to indicate recording mode
        recording_label = tk.Label(
            self.overlay,
            text="RECORDING ACTIVE\nPress F8 to stop",
            font=('Arial', 12, 'bold'),
            fg="white",
            bg="red",
            padx=10,
            pady=5
        )
        recording_label.pack(fill=tk.BOTH, expand=True)

        # Set up multiple methods to capture mouse clicks

        # Method 1: Use mouse module for global hooks
        if HAVE_MOUSE:
            try:
                # Set up mouse hook for clicks only
                mouse.on_click(lambda x, y, button, pressed:
                    self._direct_mouse_handler(x, y, button, pressed) if pressed else None)
                logger.info("Mouse click hook enabled")
            except Exception as e:
                logger.error(f"Error setting up mouse click hook: {e}")

        # Method 2: Use pynput as a fallback
        try:
            from pynput import mouse as pynput_mouse
            self.mouse_listener = pynput_mouse.Listener(
                on_click=self._pynput_mouse_handler
            )
            self.mouse_listener.start()
            logger.info("Pynput mouse listener started")
        except ImportError:
            logger.warning("Pynput module not available")

        # Method 3: Use keyboard module to capture mouse buttons
        if HAVE_KEYBOARD:
            try:
                # Use keyboard module to capture mouse buttons
                keyboard.on_press_key("mouse1", lambda _: self._keyboard_mouse_handler("left"))
                keyboard.on_press_key("mouse2", lambda _: self._keyboard_mouse_handler("right"))
                keyboard.on_press_key("mouse3", lambda _: self._keyboard_mouse_handler("middle"))
                logger.info("Keyboard mouse button hooks enabled")
            except Exception as e:
                logger.error(f"Error setting up keyboard mouse hooks: {e}")

        # Show warning if no methods are available
        if not HAVE_MOUSE and not HAVE_KEYBOARD:
            messagebox.showwarning("Warning", "Mouse recording may not work properly. Install 'mouse' or 'pynput' package.")

        # Log creation of indicator
        logger.info("Mouse recording indicator created")

        # Initially withdraw the overlay (hide it)
        self.overlay.withdraw()

    def _direct_mouse_handler(self, x, y, button, pressed):
        """Handle direct mouse click events from the mouse module."""
        global recording

        # Only process press events when recording
        if not recording or not pressed:
            return

        try:
            # Map button to our format
            button_map = {
                'left': 'left',
                'right': 'right',
                'middle': 'middle'
            }
            button_str = str(button)
            mapped_button = button_map.get(button_str, 'left')

            # Process the click
            self._process_mouse_click(x, y, mapped_button)

            # Log success
            logger.info(f"Direct mouse click detected: {mapped_button} at ({x}, {y})")
            return True
        except Exception as e:
            logger.error(f"Error in direct mouse handler: {e}")
            return False

    def _keyboard_mouse_handler(self, button):
        """Handle mouse button events captured through keyboard module."""
        global recording

        # Only process when recording
        if not recording:
            return

        try:
            # Get current mouse position
            x, y = pyautogui.position()

            # Process the click
            self._process_mouse_click(x, y, button)

            # Log success
            logger.info(f"Keyboard mouse click detected: {button} at ({x}, {y})")
            return True
        except Exception as e:
            logger.error(f"Error in keyboard mouse handler: {e}")
            return False

    def _pynput_mouse_handler(self, x, y, button, pressed):
        """Handle mouse events from pynput."""
        global recording

        # Only process press events when recording
        if not recording or not pressed:
            return

        try:
            # Map pynput button to our format
            button_map = {
                'Button.left': 'left',
                'Button.right': 'right',
                'Button.middle': 'middle'
            }
            button_str = str(button)
            mapped_button = button_map.get(button_str, 'left')

            # Process the click
            self._process_mouse_click(x, y, mapped_button)
        except Exception as e:
            logger.error(f"Error in pynput mouse handler: {e}")

    def _process_mouse_click(self, x, y, button):
        """Process a mouse click from any source."""
        global recording, last_action_time

        if recording:
            try:
                # Record the action
                logger.info(f"Recorded {button} click at coordinates: ({x}, {y})")

                # Show visual feedback (in a way that doesn't interfere with the game)
                self.root.after(1, lambda: self._show_click_feedback(x, y))

                # Record the mouse click
                record_mouse_click(button, x, y)

                # Update the actions list in the UI
                self.root.after(1, self._update_actions_list)

                # Update status
                self.status_var.set(f"Recorded {button} click at ({x}, {y})")
            except Exception as e:
                logger.error(f"Error recording mouse click: {e}")

    # Keep the original method for backward compatibility
    def _on_mouse_click(self, x, y, button):
        """Legacy method for handling mouse click events."""
        self._process_mouse_click(x, y, button)

    def _show_click_feedback(self, x, y):
        """Show visual feedback at the click location without interfering with the game."""
        try:
            # Instead of creating a new window that might steal focus,
            # we'll update our existing overlay to show the click location
            if hasattr(self, 'overlay') and self.overlay is not None:
                # Update the text in the overlay to show the click location
                for widget in self.overlay.winfo_children():
                    if isinstance(widget, tk.Label):
                        widget.config(text=f"RECORDING ACTIVE\nLast click: {x}, {y}")
                        break

                # Flash the overlay briefly to indicate a click was recorded
                orig_bg = self.overlay.cget('bg')
                self.overlay.config(bg="green")
                self.root.after(200, lambda: self.overlay.config(bg=orig_bg))
        except Exception as e:
            # Don't let feedback errors interrupt recording
            logger.error(f"Error showing click feedback: {e}")

    def _toggle_recording(self):
        """Toggle recording state."""
        global recording, last_action_time, recorded_actions

        recording = not recording

        if recording:
            # Start recording
            self.recording_var.set("Recording... (Press F8 to stop)")
            self.record_button.config(text="Stop Recording (F8)")
            self.status_var.set("Recording started")

            # Update minimalist mode indicators if active
            if self.minimalist_mode and self.minimalist_window:
                self._update_minimalist_indicators()

            # Reset recording state
            last_action_time = None

            # Set up keyboard hook for recording
            if HAVE_KEYBOARD:
                try:
                    # Remove any existing hook
                    if hasattr(self, 'keyboard_hook') and self.keyboard_hook:
                        keyboard.unhook(self.keyboard_hook)

                    # Set up a new hook
                    self.keyboard_hook = keyboard.on_press(self._on_key_press)
                    logger.info("Keyboard recording hook enabled")
                except Exception as e:
                    logger.error(f"Error setting up keyboard recording hook: {e}")
                    messagebox.showwarning("Warning", f"Failed to set up keyboard recording: {e}")

            # Show the overlay for mouse recording
            try:
                # Create a new overlay each time to ensure it's fresh
                if hasattr(self, 'overlay') and self.overlay is not None:
                    try:
                        self.overlay.destroy()
                    except:
                        pass

                # Create and show the overlay
                self._create_mouse_recorder()
                self.overlay.deiconify()

                # Make sure it's on top
                self.overlay.lift()
                self.overlay.focus_force()

                logger.info("Mouse recording overlay displayed")
            except Exception as e:
                logger.error(f"Error showing mouse recording overlay: {e}")
                messagebox.showerror("Error", f"Failed to start mouse recording: {e}")

            # Update status icon
            try:
                self.status_icon.delete("all")  # Clear previous drawing
                self.status_icon.create_oval(1, 1, 11, 11, fill=self.colors['success'], outline=self.colors['accent'])
            except Exception as e:
                logger.error(f"Error updating status icon: {e}")

            # Update recording status label if it exists
            try:
                # Find all widgets recursively
                def find_labels(widget):
                    result = []
                    if isinstance(widget, ttk.Label) and hasattr(widget, 'cget'):
                        try:
                            if widget.cget("textvariable") == str(self.recording_var):
                                result.append(widget)
                        except:
                            pass

                    # Check children
                    for child in widget.winfo_children():
                        result.extend(find_labels(child))

                    return result

                # Find and update labels
                for label in find_labels(self.root):
                    label.config(style="Success.TLabel")
            except Exception as e:
                logger.error(f"Error updating recording labels: {e}")
        else:
            # Stop recording
            self.recording_var.set("Not Recording")
            self.record_button.config(text="Start Recording (F8)")
            self.status_var.set(f"Recording stopped. {len(recorded_actions)} actions recorded.")

            # Update minimalist mode indicators if active
            if self.minimalist_mode and self.minimalist_window:
                self._update_minimalist_indicators()

            # Remove keyboard hook
            if HAVE_KEYBOARD:
                try:
                    if hasattr(self, 'keyboard_hook') and self.keyboard_hook:
                        keyboard.unhook(self.keyboard_hook)
                        self.keyboard_hook = None
                        logger.info("Keyboard recording hook disabled")
                except Exception as e:
                    logger.error(f"Error removing keyboard hook: {e}")

            # Clean up mouse hooks and destroy the overlay
            try:
                # Clean up all mouse hooks
                try:
                    # Remove mouse module hooks
                    if HAVE_MOUSE:
                        try:
                            # Unhook all mouse hooks
                            mouse.unhook_all()
                            logger.info("Mouse hooks disabled")
                        except Exception as e:
                            logger.error(f"Error removing mouse hooks: {e}")

                    # Stop pynput listener if it exists
                    if hasattr(self, 'mouse_listener') and self.mouse_listener:
                        try:
                            self.mouse_listener.stop()
                            logger.info("Pynput mouse listener stopped")
                        except Exception as e:
                            logger.error(f"Error stopping pynput listener: {e}")

                    # Remove keyboard mouse hooks
                    if HAVE_KEYBOARD:
                        try:
                            # Unhook specific mouse button keys
                            keyboard.unhook_key("mouse1")
                            keyboard.unhook_key("mouse2")
                            keyboard.unhook_key("mouse3")
                            logger.info("Keyboard mouse hooks disabled")
                        except Exception as e:
                            logger.error(f"Error removing keyboard mouse hooks: {e}")
                except Exception as e:
                    logger.error(f"Error during mouse hook cleanup: {e}")

                # Hide and destroy the overlay
                if hasattr(self, 'overlay') and self.overlay is not None:
                    self.overlay.withdraw()
                    self.overlay.destroy()
                    self.overlay = None
                    logger.info("Mouse recording overlay destroyed")
            except Exception as e:
                logger.error(f"Error cleaning up recording resources: {e}")

            # Update status icon
            try:
                self.status_icon.delete("all")  # Clear previous drawing
                self.status_icon.create_oval(1, 1, 11, 11, fill=self.colors['error'], outline=self.colors['accent'])
            except Exception as e:
                logger.error(f"Error updating status icon: {e}")

            # Update recording status label if it exists
            try:
                # Find all widgets recursively (reusing the find_labels function)
                # Find and update labels
                for label in find_labels(self.root):
                    label.config(style="Error.TLabel")
            except Exception as e:
                logger.error(f"Error updating recording labels: {e}")

            # Update actions list
            self._update_actions_list()

    def _clear_recording(self):
        """Clear recorded actions."""
        global recorded_actions

        if messagebox.askyesno("Confirm", "Are you sure you want to clear all recorded actions?"):
            recorded_actions = []
            self.actions_listbox.delete(0, tk.END)
            self.status_var.set("Recording cleared")

    def _toggle_minimalist_mode(self):
        """Toggle minimalist mode that shows a small status window."""
        if self.minimalist_mode and self.minimalist_window:
            # Save current position and settings before closing
            if self.minimalist_window:
                self.minimalist_settings['x'] = self.minimalist_window.winfo_x()
                self.minimalist_settings['y'] = self.minimalist_window.winfo_y()
                self.minimalist_settings['transparency'] = self.transparency_value
                self._save_minimalist_settings()

            # Close minimalist window and exit minimalist mode
            self.minimalist_window.destroy()
            self.minimalist_window = None
            self.minimalist_mode = False

            # Show main window
            self.root.deiconify()
            self.root.focus_force()

            logger.info("Exited minimalist mode")
            self.status_var.set("Exited minimalist mode")
        else:
            # Enter minimalist mode
            self._create_minimalist_window()

            # Hide main window
            self.root.withdraw()

            logger.info(f"Entered minimalist mode with settings: {self.minimalist_settings}")

    def _create_minimalist_window(self):
        """Create a small minimalist window with essential status indicators."""
        # Create a new window
        self.minimalist_window = tk.Toplevel(self.root)
        self.minimalist_window.title("Macro Status")

        # Get transparency value from settings
        self.transparency_value = self.minimalist_settings['transparency']

        # Make it small and position it using saved coordinates
        x = self.minimalist_settings['x']
        y = self.minimalist_settings['y']
        self.minimalist_window.geometry(f"220x160+{x}+{y}")

        # Make it always on top and remove window decorations
        self.minimalist_window.attributes("-topmost", True)
        self.minimalist_window.overrideredirect(True)
        self.minimalist_window.attributes("-alpha", self.transparency_value)

        # Configure the window
        self.minimalist_window.configure(bg=self.colors['frame_bg'])

        # Add a title bar for dragging
        title_bar = tk.Frame(self.minimalist_window, bg=self.colors['accent'], height=20)
        title_bar.pack(fill=tk.X)

        # Add title text
        title_label = tk.Label(
            title_bar,
            text="Macro Status (F10 to exit)",
            bg=self.colors['accent'],
            fg="white",
            font=('Arial', 8, 'bold')
        )
        title_label.pack(side=tk.LEFT, padx=5)

        # Add a close button
        close_button = tk.Button(
            title_bar,
            text="×",
            bg=self.colors['accent'],
            fg="white",
            font=('Arial', 8, 'bold'),
            bd=0,
            command=self._toggle_minimalist_mode
        )
        close_button.pack(side=tk.RIGHT)

        # Make the entire window draggable
        title_bar.bind("<ButtonPress-1>", self._start_minimalist_drag)
        title_bar.bind("<ButtonRelease-1>", self._stop_minimalist_drag)
        title_bar.bind("<B1-Motion>", self._on_minimalist_drag)

        # Create a frame for status indicators
        status_frame = tk.Frame(self.minimalist_window, bg=self.colors['frame_bg'], padx=10, pady=5)
        status_frame.pack(fill=tk.BOTH, expand=True)

        # Add a drag hint
        drag_hint = tk.Label(
            status_frame,
            text="Click and drag anywhere to move",
            bg=self.colors['frame_bg'],
            fg=self.colors['text_dark'],
            font=('Arial', 7, 'italic')
        )
        drag_hint.pack(fill=tk.X, pady=(0, 5))

        # Recording status indicator
        recording_frame = tk.Frame(status_frame, bg=self.colors['frame_bg'])
        recording_frame.pack(fill=tk.X, pady=2)

        recording_indicator = tk.Canvas(recording_frame, width=12, height=12, bg=self.colors['frame_bg'], highlightthickness=0)
        recording_indicator.pack(side=tk.LEFT, padx=(0, 5))

        recording_label = tk.Label(
            recording_frame,
            text="Recording: Inactive",
            bg=self.colors['frame_bg'],
            fg=self.colors['text'],
            font=('Arial', 8)
        )
        recording_label.pack(side=tk.LEFT)

        # Playback status indicator
        playback_frame = tk.Frame(status_frame, bg=self.colors['frame_bg'])
        playback_frame.pack(fill=tk.X, pady=2)

        playback_indicator = tk.Canvas(playback_frame, width=12, height=12, bg=self.colors['frame_bg'], highlightthickness=0)
        playback_indicator.pack(side=tk.LEFT, padx=(0, 5))

        playback_label = tk.Label(
            playback_frame,
            text="Playback: Inactive",
            bg=self.colors['frame_bg'],
            fg=self.colors['text'],
            font=('Arial', 8)
        )
        playback_label.pack(side=tk.LEFT)

        # Death detection status indicator
        death_frame = tk.Frame(status_frame, bg=self.colors['frame_bg'])
        death_frame.pack(fill=tk.X, pady=2)

        death_indicator = tk.Canvas(death_frame, width=12, height=12, bg=self.colors['frame_bg'], highlightthickness=0)
        death_indicator.pack(side=tk.LEFT, padx=(0, 5))

        death_label = tk.Label(
            death_frame,
            text="Death Detection: Inactive",
            bg=self.colors['frame_bg'],
            fg=self.colors['text'],
            font=('Arial', 8)
        )
        death_label.pack(side=tk.LEFT)

        # Add transparency control
        transparency_frame = tk.Frame(status_frame, bg=self.colors['frame_bg'])
        transparency_frame.pack(fill=tk.X, pady=(10, 2))

        transparency_label = tk.Label(
            transparency_frame,
            text="Transparency:",
            bg=self.colors['frame_bg'],
            fg=self.colors['text'],
            font=('Arial', 8)
        )
        transparency_label.pack(side=tk.LEFT)

        # Transparency slider
        self.transparency_var = tk.DoubleVar(value=self.transparency_value * 100)
        transparency_slider = ttk.Scale(
            transparency_frame,
            from_=20,
            to=100,
            orient="horizontal",
            variable=self.transparency_var,
            command=self._update_transparency
        )
        transparency_slider.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0))

        # Make all frames and widgets draggable
        for widget in [status_frame, recording_frame, playback_frame, death_frame,
                      transparency_frame, drag_hint, recording_label, playback_label,
                      death_label, transparency_label, transparency_slider]:
            widget.bind("<ButtonPress-1>", self._start_minimalist_drag)
            widget.bind("<ButtonRelease-1>", self._stop_minimalist_drag)
            widget.bind("<B1-Motion>", self._on_minimalist_drag)

        # Store references to update later
        self.mini_recording_indicator = recording_indicator
        self.mini_recording_label = recording_label
        self.mini_playback_indicator = playback_indicator
        self.mini_playback_label = playback_label
        self.mini_death_indicator = death_indicator
        self.mini_death_label = death_label

        # Set initial indicator states
        self._update_minimalist_indicators()

        # Start periodic updates
        self._schedule_minimalist_updates()

        # Set minimalist mode flag
        self.minimalist_mode = True

    def _start_minimalist_drag(self, event):
        """Start dragging the minimalist window."""
        self.minimalist_window.x = event.x
        self.minimalist_window.y = event.y

    def _on_minimalist_drag(self, event):
        """Handle dragging of the minimalist window."""
        x = self.minimalist_window.winfo_x() + event.x - self.minimalist_window.x
        y = self.minimalist_window.winfo_y() + event.y - self.minimalist_window.y
        self.minimalist_window.geometry(f"+{x}+{y}")

    def _stop_minimalist_drag(self, _):
        """Stop dragging the minimalist window and save position."""
        # Save the new position
        if self.minimalist_mode and self.minimalist_window:
            self.minimalist_settings['x'] = self.minimalist_window.winfo_x()
            self.minimalist_settings['y'] = self.minimalist_window.winfo_y()
            self._save_minimalist_settings()

    def _update_minimalist_indicators(self):
        """Update the status indicators in the minimalist window."""
        if not self.minimalist_mode or not self.minimalist_window:
            return

        # Make sure we're accessing the global variables
        global recording, running, playback_thread, death_detection_settings, death_detection_active

        # Update recording indicator with more visible colors
        try:
            if recording:
                self.mini_recording_indicator.delete("all")
                self.mini_recording_indicator.create_oval(1, 1, 11, 11, fill="#FF0000", outline="#FFFFFF")  # Bright red with white outline
                self.mini_recording_label.config(text="Recording: Active", fg="#FF0000")  # Red text
            else:
                self.mini_recording_indicator.delete("all")
                self.mini_recording_indicator.create_oval(1, 1, 11, 11, fill="#808080", outline="#FFFFFF")  # Gray with white outline
                self.mini_recording_label.config(text="Recording: Inactive", fg=self.colors['text'])
        except Exception as e:
            logger.error(f"Error updating recording indicator: {e}")

        # Update playback indicator with more visible colors
        try:
            # Log the current state of playback variables for debugging
            thread_exists = playback_thread is not None
            thread_alive = playback_thread.is_alive() if thread_exists else False
            logger.debug(f"Playback status - running: {running}, thread exists: {thread_exists}, thread alive: {thread_alive}")

            if running and playback_thread and playback_thread.is_alive():
                self.mini_playback_indicator.delete("all")
                self.mini_playback_indicator.create_oval(1, 1, 11, 11, fill="#00FF00", outline="#FFFFFF")  # Bright green with white outline
                self.mini_playback_label.config(text="Playback: Active", fg="#00AA00")  # Green text
                logger.debug("Set playback indicator to ACTIVE")
            else:
                self.mini_playback_indicator.delete("all")
                self.mini_playback_indicator.create_oval(1, 1, 11, 11, fill="#808080", outline="#FFFFFF")  # Gray with white outline
                self.mini_playback_label.config(text="Playback: Inactive", fg=self.colors['text'])
                logger.debug("Set playback indicator to INACTIVE")
        except Exception as e:
            logger.error(f"Error updating playback indicator: {e}")

        # Update death detection indicator with more visible colors
        try:
            if death_detection_settings['enabled'] and death_detection_active:
                self.mini_death_indicator.delete("all")
                self.mini_death_indicator.create_oval(1, 1, 11, 11, fill="#0000FF", outline="#FFFFFF")  # Bright blue with white outline
                self.mini_death_label.config(text="Death Detection: Active", fg="#0000FF")  # Blue text
            elif death_detection_settings['enabled']:
                self.mini_death_indicator.delete("all")
                self.mini_death_indicator.create_oval(1, 1, 11, 11, fill="#FFFF00", outline="#FFFFFF")  # Yellow with white outline
                self.mini_death_label.config(text="Death Detection: Enabled", fg="#AA7700")  # Yellow-orange text
            else:
                self.mini_death_indicator.delete("all")
                self.mini_death_indicator.create_oval(1, 1, 11, 11, fill="#808080", outline="#FFFFFF")  # Gray with white outline
                self.mini_death_label.config(text="Death Detection: Inactive", fg=self.colors['text'])
        except Exception as e:
            logger.error(f"Error updating death detection indicator: {e}")

        # Log the current status for debugging
        logger.debug(f"Status - Recording: {recording}, Playback: {running}, Death Detection: {death_detection_settings['enabled']} (Active: {death_detection_active})")

    def _schedule_minimalist_updates(self):
        """Schedule periodic updates for the minimalist window."""
        if self.minimalist_mode and self.minimalist_window:
            self._update_minimalist_indicators()
            # Update more frequently (every 200ms) for better responsiveness
            self.minimalist_window.after(200, self._schedule_minimalist_updates)

    def _update_transparency(self, value):
        """Update the transparency of the minimalist window."""
        if self.minimalist_mode and self.minimalist_window:
            # Convert slider value (20-100) to alpha value (0.2-1.0)
            self.transparency_value = float(value) / 100
            self.minimalist_window.attributes("-alpha", self.transparency_value)

            # Save the transparency setting
            self.minimalist_settings['transparency'] = self.transparency_value
            self._save_minimalist_settings()

            logger.info(f"Updated minimalist window transparency to {self.transparency_value:.2f}")

    def _save_minimalist_settings(self):
        """Save minimalist window settings to a file."""
        try:
            # If window exists, update position
            if self.minimalist_mode and self.minimalist_window:
                self.minimalist_settings['x'] = self.minimalist_window.winfo_x()
                self.minimalist_settings['y'] = self.minimalist_window.winfo_y()

            # Save settings to a file
            settings_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "minimalist_settings.json")
            with open(settings_file, 'w') as f:
                json.dump(self.minimalist_settings, f)

            logger.info(f"Saved minimalist settings: {self.minimalist_settings}")
        except Exception as e:
            logger.error(f"Error saving minimalist settings: {e}")

    def _load_minimalist_settings(self):
        """Load minimalist window settings from a file."""
        try:
            settings_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "minimalist_settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, 'r') as f:
                    loaded_settings = json.load(f)

                    # Update settings with loaded values
                    for key, value in loaded_settings.items():
                        if key in self.minimalist_settings:
                            self.minimalist_settings[key] = value

                logger.info(f"Loaded minimalist settings: {self.minimalist_settings}")
        except Exception as e:
            logger.error(f"Error loading minimalist settings: {e}")
            # Keep default settings on error

    def _toggle_infinite(self):
        """Toggle infinite repeat mode."""
        if self.infinite_var.get():
            # Disable repeat count spinbox when infinite is checked
            self.repeat_spinbox.config(state="disabled")
        else:
            # Enable repeat count spinbox when infinite is unchecked
            self.repeat_spinbox.config(state="normal")

    def _toggle_target_game(self):
        """Toggle targeting only the game window."""
        global game_window_settings

        game_window_settings['target_game_only'] = self.target_game_var.get()

        if game_window_settings['target_game_only']:
            self.status_var.set("Actions will only affect the game window")
            # Try to find the game window
            self._find_game_window()
        else:
            self.status_var.set("Actions will affect the entire screen")

    def _toggle_focus_steal(self):
        """Toggle prevent focus stealing setting."""
        global game_window_settings
        game_window_settings['prevent_focus_steal'] = self.prevent_focus_var.get()

        # Apply the setting immediately
        if game_window_settings['prevent_focus_steal']:
            # Set window attributes to prevent focus stealing
            self._apply_focus_prevention()
            self.status_var.set("Focus stealing prevention enabled")
        else:
            # Reset window attributes
            self._remove_focus_prevention()
            self.status_var.set("Focus stealing prevention disabled")

    def _toggle_stay_on_top(self):
        """Toggle stay on top setting."""
        global game_window_settings
        game_window_settings['stay_on_top'] = self.stay_on_top_var.get()

        # Apply the setting immediately
        if game_window_settings['stay_on_top']:
            self.root.attributes("-topmost", True)
            self.status_var.set("Window will stay on top")
        else:
            self.root.attributes("-topmost", False)
            self.status_var.set("Window will not stay on top")

    def _toggle_overlay_mode(self):
        """Toggle overlay mode setting."""
        global game_window_settings
        game_window_settings['use_overlay_mode'] = self.overlay_mode_var.get()

        # Show a message about needing to restart
        if game_window_settings['use_overlay_mode']:
            messagebox.showinfo(
                "Overlay Mode Enabled",
                "Overlay mode will be enabled the next time you start the application.\n\n"
                "This mode creates a transparent window that doesn't steal focus from the game."
            )
            self.status_var.set("Overlay mode will be enabled on restart")
        else:
            messagebox.showinfo(
                "Overlay Mode Disabled",
                "Overlay mode will be disabled the next time you start the application.\n\n"
                "The application will use a normal window."
            )
            self.status_var.set("Overlay mode will be disabled on restart")

    def _create_death_detection_tab(self, parent):
        """Create the death detection tab UI with scrolling support."""
        # Configure parent to expand
        parent.rowconfigure(0, weight=1)
        parent.columnconfigure(0, weight=1)

        # Create a canvas for scrolling
        canvas = tk.Canvas(parent, bg=self.colors['bg'], highlightthickness=0)
        canvas.grid(row=0, column=0, sticky="nsew")

        # Add scrollbar
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollbar.grid(row=0, column=1, sticky="ns")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Main content frame inside canvas
        main_frame = ttk.Frame(canvas)
        main_frame.columnconfigure(0, weight=1)

        # Create window in canvas
        canvas_window = canvas.create_window((0, 0), window=main_frame, anchor="nw", tags="main_frame")

        # Configure canvas to resize with window
        def configure_canvas(event):
            # Update the width of the canvas window
            canvas.itemconfig(canvas_window, width=event.width)
            # Update the scrollregion to encompass the inner frame
            canvas.configure(scrollregion=canvas.bbox("all"))

        canvas.bind('<Configure>', configure_canvas)

        # Configure main_frame to update scroll region when its size changes
        def configure_scroll_region(_):
            canvas.configure(scrollregion=canvas.bbox("all"))

        main_frame.bind('<Configure>', configure_scroll_region)

        # Add mousewheel scrolling - handle different OS platforms
        def _on_mousewheel_windows(event):
            # Windows mouse wheel event
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _on_mousewheel_linux(event):
            # Linux mouse wheel event
            if event.num == 5:
                canvas.yview_scroll(1, "units")
            elif event.num == 4:
                canvas.yview_scroll(-1, "units")

        # Bind for Windows
        canvas.bind_all("<MouseWheel>", _on_mousewheel_windows)
        # Bind for Linux
        canvas.bind_all("<Button-4>", _on_mousewheel_linux)
        canvas.bind_all("<Button-5>", _on_mousewheel_linux)

        # Store references to unbind when tab changes
        self.mousewheel_bindings = [
            ("<MouseWheel>", _on_mousewheel_windows),
            ("<Button-4>", _on_mousewheel_linux),
            ("<Button-5>", _on_mousewheel_linux)
        ]

        # Clean up bindings when tab changes
        def _on_tab_change(_):
            for binding, _ in self.mousewheel_bindings:
                try:
                    canvas.unbind_all(binding)
                except:
                    pass

        # Store the canvas and callback for later access
        self.death_detection_canvas = canvas
        self.tab_change_callback = _on_tab_change

        # Bind tab change event to the notebook
        parent.master.bind("<<NotebookTabChanged>>", _on_tab_change)

        # Death Detection header with icon
        header_frame = ttk.Frame(main_frame)
        header_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=(5, 15))

        # Create a small icon for death detection
        icon_canvas = tk.Canvas(header_frame, width=24, height=24, highlightthickness=0, bg=self.colors['bg'])
        icon_canvas.grid(row=0, column=0, padx=(0, 10))

        # Draw a skull icon
        icon_canvas.create_oval(2, 2, 22, 22, outline=self.colors['error'], width=2)
        icon_canvas.create_oval(7, 7, 11, 11, fill=self.colors['error'])  # Left eye
        icon_canvas.create_oval(13, 7, 17, 11, fill=self.colors['error'])  # Right eye
        icon_canvas.create_arc(7, 12, 17, 20, start=0, extent=-180, fill=self.colors['error'])  # Mouth

        # Header text
        header_label = ttk.Label(header_frame, text="Death Detection", style="Heading.TLabel")
        header_label.grid(row=0, column=1, sticky="w")

        # Main content frame
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
        content_frame.columnconfigure(0, weight=1)
        
        # Status indicator for death detection
        status_frame = ttk.Frame(content_frame)
        status_frame.grid(row=0, column=0, sticky="w", padx=5, pady=5)
        
        self.death_status_var = tk.StringVar(value="Inactive")
        self.death_status_color = tk.Canvas(status_frame, width=12, height=12, highlightthickness=0, bg=self.colors['bg'])
        self.death_status_color.grid(row=0, column=0, padx=(0, 5))
        self.death_status_color.create_oval(1, 1, 11, 11, fill=self.colors['error'], outline=self.colors['accent'])
        
        status_label = ttk.Label(status_frame, textvariable=self.death_status_var, style="Error.TLabel")
        status_label.grid(row=0, column=1, sticky="w")
        
        # Main controls section
        controls_section = ttk.LabelFrame(content_frame, text="Death Detection Settings", padding=15)
        controls_section.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        controls_section.columnconfigure(0, weight=1)
        
        # Enable checkbox
        self.death_enabled_var = tk.BooleanVar(value=death_detection_settings['enabled'])
        enable_check = ttk.Checkbutton(
            controls_section,
            text="Enable Death Detection",
            variable=self.death_enabled_var,
            command=self._toggle_death_detection,
            style="TCheckbutton"
        )
        enable_check.grid(row=0, column=0, sticky="w", padx=5, pady=5)
        
        # Check interval setting
        interval_frame = ttk.Frame(controls_section)
        interval_frame.grid(row=1, column=0, sticky="w", padx=5, pady=5)
        
        ttk.Label(interval_frame, text="Check Interval (seconds):", style="TLabel").grid(row=0, column=0, sticky="w")
        
        self.check_interval_var = tk.DoubleVar(value=death_detection_settings['check_interval'])
        interval_spinbox = ttk.Spinbox(
            interval_frame,
            from_=0.5,
            to=10.0,
            increment=0.5,
            textvariable=self.check_interval_var,
            width=10
        )
        interval_spinbox.grid(row=0, column=1, sticky="w", padx=(10, 0))
        
        # Confidence threshold setting
        confidence_frame = ttk.Frame(controls_section)
        confidence_frame.grid(row=2, column=0, sticky="w", padx=5, pady=5)
        
        ttk.Label(confidence_frame, text="Confidence Threshold (%):", style="TLabel").grid(row=0, column=0, sticky="w")
        
        self.confidence_var = tk.DoubleVar(value=death_detection_settings.get('confidence', 0.85) * 100)
        confidence_scale = ttk.Scale(
            confidence_frame,
            from_=50,
            to=100,
            orient="horizontal",
            variable=self.confidence_var,
            length=200
        )
        confidence_scale.grid(row=0, column=1, sticky="w", padx=(10, 0))
        
        confidence_label = ttk.Label(confidence_frame, textvariable=self.confidence_var, style="TLabel")
        confidence_label.grid(row=0, column=2, sticky="w", padx=(5, 0))
        
        # Auto click checkbox
        self.auto_click_var = tk.BooleanVar(value=death_detection_settings['auto_click_revival'])
        auto_click_check = ttk.Checkbutton(
            controls_section,
            text="Automatically click revival button when detected",
            variable=self.auto_click_var,
            style="TCheckbutton"
        )
        auto_click_check.grid(row=3, column=0, sticky="w", padx=5, pady=5)
        
        # Template selection section
        template_section = ttk.LabelFrame(content_frame, text="Revival Button Template", padding=15)
        template_section.grid(row=2, column=0, sticky="ew", padx=5, pady=10)
        template_section.columnconfigure(0, weight=1)
        
        # Template path display
        template_frame = ttk.Frame(template_section)
        template_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        
        ttk.Label(template_frame, text="Template File:", style="TLabel").grid(row=0, column=0, sticky="w")
        
        # Template path display
        self.template_path_var = tk.StringVar(value="Not set")
        if os.path.exists(death_detection_settings['template_path']):
            template_name = os.path.basename(death_detection_settings['template_path'])
            self.template_path_var.set(template_name)
        
        template_path_label = ttk.Label(template_frame, textvariable=self.template_path_var, style="Secondary.TLabel")
        template_path_label.grid(row=0, column=1, sticky="w", padx=(10, 0))
        
        # Template image preview frame
        self.template_preview_frame = ttk.LabelFrame(template_section, text="Template Preview", padding=10)
        self.template_preview_frame.grid(row=1, column=0, sticky="ew", pady=10, padx=5)
        self.template_preview_frame.columnconfigure(0, weight=1)
        
        # Template image display
        self.template_display = ttk.Label(self.template_preview_frame, text="No template image loaded")
        self.template_display.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        
        # Load template image if it exists
        template_path = death_detection_settings['template_path']
        if os.path.exists(template_path):
            try:
                # Use PIL to open and resize the image for display
                from PIL import Image, ImageTk
                img = Image.open(template_path)
                
                # Resize while maintaining aspect ratio
                max_size = (200, 200)
                img.thumbnail(max_size, Image.LANCZOS)
                
                # Convert to PhotoImage for Tkinter
                photo = ImageTk.PhotoImage(img)
                
                # Update the display
                self.template_display.configure(image=photo, text="")
                self.template_display.image = photo  # Keep a reference to prevent garbage collection
                logger.info(f"Displayed template image: {template_path}")
            except Exception as e:
                logger.error(f"Error loading template image: {e}")
                self.template_display.configure(text=f"Error loading image: {e}")
        
        # Template buttons
        template_buttons_frame = ttk.Frame(template_section)
        template_buttons_frame.grid(row=2, column=0, sticky="w", pady=10)
        
        # Capture template button
        capture_template_button = tk.Button(
            template_buttons_frame,
            text="Capture New Template",
            command=self._capture_revival_template,
            bg=self.colors['accent'],
            fg="white",
            font=('Arial', 10, 'bold'),
            width=20,
            relief=tk.RAISED,
            borderwidth=2
        )
        capture_template_button.grid(row=0, column=0, padx=(0, 10))
        tooltip.add_tooltip(capture_template_button, "Capture a screenshot of the revival button to use as a template")
        
        # Browse template button
        browse_template_button = tk.Button(
            template_buttons_frame,
            text="Browse for Template",
            command=self._browse_revival_template,
            bg=self.colors['accent_dark'],
            fg="white",
            font=('Arial', 10, 'bold'),
            width=20,
            relief=tk.RAISED,
            borderwidth=2
        )
        browse_template_button.grid(row=0, column=1)
        tooltip.add_tooltip(browse_template_button, "Browse for an existing template image file")

        # Separator before apply button
        ttk.Separator(content_frame, orient="horizontal").grid(row=3, column=0, sticky="ew", padx=5, pady=15)

        # Apply button at the bottom
        apply_frame = ttk.Frame(content_frame, padding=(0, 5, 0, 0))
        apply_frame.grid(row=4, column=0, sticky="e", padx=5, pady=5)
        apply_frame.columnconfigure(0, weight=1)

        apply_button = tk.Button(
            apply_frame,
            text="Apply Death Detection Settings",
            command=self._apply_death_settings,
            bg=self.colors['success'],
            fg="white",
            font=('Arial', 12, 'bold'),
            width=30,
            relief=tk.RAISED,
            borderwidth=2
        )
        apply_button.grid(row=0, column=0)
        tooltip.add_tooltip(apply_button, "Apply all death detection settings")

        # Instructions
        instructions_frame = ttk.Frame(content_frame, padding=10, relief="solid", borderwidth=1)
        instructions_frame.grid(row=5, column=0, sticky="ew", padx=5, pady=15)

        instructions_text = (
            "How to use death detection:\n"
            "1. Enable Death Detection to automatically detect when your character dies\n"
            "2. Capture a template of the revival button from your game\n"
            "3. Set the confidence threshold (85% recommended)\n"
            "4. Enable auto-click to automatically click the revival button when detected"
        )

        ttk.Label(instructions_frame, text=instructions_text, wraplength=500, justify="left").grid(
            row=0, column=0, sticky="w", padx=5, pady=5
        )

        # Update death status indicator based on current state
        self._update_death_status_indicator()

    def _update_death_status_indicator(self):
        """Update the death detection status indicator."""
        if death_detection_settings['enabled']:
            if death_detection_active and death_detection_thread and death_detection_thread.is_alive():
                self.death_status_var.set("Active")
                self.death_status_color.delete("all")
                self.death_status_color.create_oval(1, 1, 11, 11, fill=self.colors['success'], outline=self.colors['accent'])
            else:
                self.death_status_var.set("Enabled but not running")
                self.death_status_color.delete("all")
                self.death_status_color.create_oval(1, 1, 11, 11, fill=self.colors['warning'], outline=self.colors['accent'])
        else:
            self.death_status_var.set("Inactive")
            self.death_status_color.delete("all")
            self.death_status_color.create_oval(1, 1, 11, 11, fill=self.colors['error'], outline=self.colors['accent'])

        # Schedule the next update
        self.root.after(1000, self._update_death_status_indicator)

    def _apply_focus_prevention(self):
        """Apply focus prevention settings."""
        try:
            # Set window style to prevent focus stealing
            # This uses Windows-specific attributes
            hwnd = int(self.root.winfo_id())

            # Get current window style
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)

            # Add the WS_EX_NOACTIVATE style to prevent focus stealing
            new_style = style | win32con.WS_EX_NOACTIVATE

            # Set the new style
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, new_style)

            logger.info("Applied focus prevention settings")
        except Exception as e:
            logger.error(f"Error applying focus prevention: {e}")

    def _create_custom_titlebar(self):
        """Create a custom title bar to allow moving the window."""
        try:
            # Check if we already have a title bar
            if hasattr(self, 'title_bar') and self.title_bar is not None:
                return

            # Create a frame for the title bar at the top of the window
            self.title_bar = tk.Frame(self.root, bg=self.colors['accent_dark'], height=20)
            self.title_bar.pack(side=tk.TOP, fill=tk.X)

            # Add title text
            title_label = tk.Label(
                self.title_bar,
                text="Rappelz Macro Recorder",
                bg=self.colors['accent_dark'],
                fg="white",
                font=('Arial', 9)
            )
            title_label.pack(side=tk.LEFT, padx=5)

            # Add a close button
            close_button = tk.Button(
                self.title_bar,
                text="×",
                bg=self.colors['accent_dark'],
                fg="white",
                font=('Arial', 12),
                bd=0,
                command=self.root.destroy
            )
            close_button.pack(side=tk.RIGHT)

            # Add a minimize button
            minimize_button = tk.Button(
                self.title_bar,
                text="_",
                bg=self.colors['accent_dark'],
                fg="white",
                font=('Arial', 12),
                bd=0,
                command=self.root.iconify
            )
            minimize_button.pack(side=tk.RIGHT)

            # Variables to track window movement
            self.dragging = False
            self.drag_start_x = 0
            self.drag_start_y = 0

            # Bind mouse events for dragging
            self.title_bar.bind("<ButtonPress-1>", self._start_drag)
            self.title_bar.bind("<ButtonRelease-1>", self._stop_drag)
            self.title_bar.bind("<B1-Motion>", self._on_drag)

            # Also bind the title label
            title_label.bind("<ButtonPress-1>", self._start_drag)
            title_label.bind("<ButtonRelease-1>", self._stop_drag)
            title_label.bind("<B1-Motion>", self._on_drag)

            logger.info("Created custom title bar")
        except Exception as e:
            logger.error(f"Error creating custom title bar: {e}")

    def _start_drag(self, event):
        """Start window dragging."""
        self.dragging = True
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root

    def _stop_drag(self, _):
        """Stop window dragging."""
        self.dragging = False

    def _on_drag(self, event):
        """Handle window dragging."""
        if self.dragging:
            # Calculate the distance moved
            dx = event.x_root - self.drag_start_x
            dy = event.y_root - self.drag_start_y

            # Get current window position
            x = self.root.winfo_x() + dx
            y = self.root.winfo_y() + dy

            # Move the window
            self.root.geometry(f"+{x}+{y}")

            # Update drag start position
            self.drag_start_x = event.x_root
            self.drag_start_y = event.y_root

    def _remove_focus_prevention(self):
        """Remove focus prevention settings."""
        try:
            # Reset window style to allow focus
            hwnd = int(self.root.winfo_id())

            # Get current window style
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)

            # Remove the WS_EX_NOACTIVATE style
            new_style = style & ~win32con.WS_EX_NOACTIVATE

            # Set the new style
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, new_style)

            logger.info("Removed focus prevention settings")
        except Exception as e:
            logger.error(f"Error removing focus prevention: {e}")

    def _apply_game_settings(self):
        """Apply all game window settings."""
        global game_window_settings

        try:
            # Update settings from UI
            game_window_settings['target_game_only'] = self.target_game_var.get()
            game_window_settings['window_title'] = self.window_title_var.get()
            game_window_settings['stay_on_top'] = self.stay_on_top_var.get()
            game_window_settings['prevent_focus_steal'] = self.prevent_focus_var.get()
            game_window_settings['use_overlay_mode'] = self.overlay_mode_var.get()

            # Apply stay on top setting
            if game_window_settings['stay_on_top']:
                self.root.attributes("-topmost", True)
                logger.info("Applied stay on top setting")
            else:
                self.root.attributes("-topmost", False)
                logger.info("Removed stay on top setting")

            # Apply focus prevention setting
            if game_window_settings['prevent_focus_steal']:
                self._apply_focus_prevention()
            else:
                self._remove_focus_prevention()

            # Try to find the game window if targeting is enabled
            if game_window_settings['target_game_only']:
                if self._find_game_window():
                    self.status_var.set("Game window settings applied and game window found")
                else:
                    self.status_var.set("Game window settings applied but game window not found")
            else:
                self.status_var.set("Game window settings applied")

            logger.info(f"Applied game window settings: {game_window_settings}")
        except Exception as e:
            logger.error(f"Error applying game window settings: {e}")
            messagebox.showerror("Error", f"Failed to apply game window settings: {e}")

    def _find_game_window(self):
        """Find the game window using the specified title."""
        global game_window_settings, game_window_handle

        # Update the window title from the UI
        game_window_settings['window_title'] = self.window_title_var.get()

        # Try to find the window
        if find_game_window():
            self.status_var.set(f"Found game window: {game_window_settings['window_title']}")

            # Get window position and size
            window_rect = get_game_window_rect()
            if window_rect:
                self.status_var.set(f"Found game window at ({window_rect['x']}, {window_rect['y']}) "
                                   f"size: {window_rect['width']}x{window_rect['height']}")
            return True
        else:
            self.status_var.set(f"Game window not found: {game_window_settings['window_title']}")
            return False

    def _toggle_playback(self):
        """Toggle playback state."""
        global running, playback_thread

        if running and playback_thread and playback_thread.is_alive():
            # Stop playback
            self._stop_playback()
        else:
            # Start playback
            infinite = self.infinite_var.get()
            repeat_count = self.repeat_count_var.get()
            repeat_delay = self.repeat_delay_var.get()

            if not recorded_actions:
                messagebox.showwarning("Warning", "No actions recorded")
                return

            running = True
            playback_thread = threading.Thread(
                target=playback_actions,
                args=(repeat_count, repeat_delay, infinite)
            )

            # Start the thread first
            playback_thread.daemon = True
            playback_thread.start()

            # Update minimalist mode indicators if active
            if self.minimalist_mode and self.minimalist_window:
                self._update_minimalist_indicators()
                logger.debug("Updated minimalist indicators - playback started")

            self.playback_button.config(text="Stop Playback (F9)")

            if infinite:
                self.status_var.set(f"Playing back {len(recorded_actions)} actions (infinite repetitions)")
            else:
                self.status_var.set(f"Playing back {len(recorded_actions)} actions ({repeat_count} times)")

            # Check if playback is still running periodically
            self.root.after(100, self._check_playback)

    def _check_playback(self):
        """Check if playback is still running."""
        global running, playback_thread

        if not running or not playback_thread or not playback_thread.is_alive():
            # Playback stopped
            self.playback_button.config(text="Start Playback (F9)")
            if not running:
                self.status_var.set("Playback stopped")
            else:
                self.status_var.set("Playback completed")
            running = False
            playback_thread = None

            # Update minimalist mode indicators if active
            if self.minimalist_mode and self.minimalist_window:
                self._update_minimalist_indicators()
                logger.debug("Updated minimalist indicators - playback stopped")
        else:
            # Playback still running, check again later
            # Update minimalist mode indicators if active
            if self.minimalist_mode and self.minimalist_window:
                self._update_minimalist_indicators()
                logger.debug("Updated minimalist indicators - playback running")

            self.root.after(100, self._check_playback)

    def _stop_playback(self):
        """Stop playback."""
        global running

        running = False
        self.playback_button.config(text="Start Playback (F9)")
        self.status_var.set("Playback stopped")

        # Update minimalist mode indicators if active
        if self.minimalist_mode and self.minimalist_window:
            self._update_minimalist_indicators()

    def _save_macro(self):
        """Save recorded macro to file."""
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")],
            title="Save Macro"
        )

        if filename:
            if save_actions(filename):
                self.status_var.set(f"Macro saved to {os.path.basename(filename)}")

    def _save_last_macro_path(self, path):
        """Save the path of the last loaded macro to a settings file."""
        settings_file = "app_settings.json"
        settings = {}
        
        # Load existing settings if they exist
        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r') as f:
                    settings = json.load(f)
            except:
                pass
                
        # Update with the last macro path
        settings['last_macro_path'] = path
        
        # Save the settings
        try:
            with open(settings_file, 'w') as f:
                json.dump(settings, f, indent=4)
            logger.info(f"Saved last macro path: {path}")
        except Exception as e:
            logger.error(f"Error saving last macro path: {e}")
    
    def _load_last_macro(self):
        """Load the last used macro if available."""
        settings_file = "app_settings.json"
        
        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r') as f:
                    settings = json.load(f)
                
                if 'last_macro_path' in settings and os.path.exists(settings['last_macro_path']):
                    last_path = settings['last_macro_path']
                    if load_actions(last_path):
                        self.status_var.set(f"Loaded last macro: {os.path.basename(last_path)}")
                        self._update_actions_list()
                        
                        # Update UI with loaded death detection settings
                        self.death_enabled_var.set(death_detection_settings['enabled'])
                        self.check_interval_var.set(death_detection_settings['check_interval'])
                        
                        # Update UI with loaded game window settings
                        self.target_game_var.set(game_window_settings['target_game_only'])
                        self.window_title_var.set(game_window_settings['window_title'])
                        
                        # Update click position if available
                        if 'click_position' in death_detection_settings and death_detection_settings['click_position']:
                            x, y = death_detection_settings['click_position']
                            self.click_pos_var.set(f"({x}, {y})")
                        else:
                            self.click_pos_var.set("Not set")
                        
                        logger.info(f"Automatically loaded last macro: {last_path}")
                        return True
            except Exception as e:
                logger.error(f"Error loading last macro: {e}")
        
        return False
    
    def _load_macro(self):
        """Load recorded macro from file."""
        filename = filedialog.askopenfilename(
            defaultextension=".json",
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")],
            title="Load Macro"
        )

        if filename:
            if load_actions(filename):
                # Save this as the last loaded macro
                self._save_last_macro_path(filename)
                
                self.status_var.set(f"Macro loaded from {os.path.basename(filename)}")
                self._update_actions_list()

                # Update UI with loaded death detection settings
                self.death_enabled_var.set(death_detection_settings['enabled'])
                self.check_interval_var.set(death_detection_settings['check_interval'])

                # Update UI with loaded game window settings
                self.target_game_var.set(game_window_settings['target_game_only'])
                self.window_title_var.set(game_window_settings['window_title'])

                # Update click position if available
                if 'click_position' in death_detection_settings and death_detection_settings['click_position']:
                    x, y = death_detection_settings['click_position']
                    self.click_pos_var.set(f"({x}, {y})")
                else:
                    self.click_pos_var.set("Not set")

    def _rgb_to_hex(self, rgb):
        """Convert RGB tuple to hex color string."""
        return f'#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}'

    def _hex_to_rgb(self, hex_color):
        """Convert hex color string to RGB tuple."""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def _toggle_death_detection(self):
        """Toggle death detection."""
        global death_detection_active, death_detection_thread, death_detection_settings

        enabled = self.death_enabled_var.get()
        death_detection_settings['enabled'] = enabled
        
        # Update auto-click setting and confidence from the UI
        death_detection_settings['auto_click_revival'] = self.auto_click_var.get()
        death_detection_settings['confidence'] = self.confidence_var.get() / 100.0  # Convert from percentage to decimal
        death_detection_settings['check_interval'] = self.check_interval_var.get()
        death_detection_settings['use_template_matching'] = True  # Always use template matching now
        
        # Get the template path
        template_path = death_detection_settings['template_path']
        template_exists = os.path.exists(template_path)
        
        if enabled:
            if not template_exists:
                messagebox.showwarning("Template Missing", "No revival button template found. Please capture or browse for a template first.")
                self.death_enabled_var.set(False)  # Revert the checkbox
                death_detection_settings['enabled'] = False
                return
                
            # Start death detection if not already running
            if not death_detection_active:
                death_detection_active = True
                death_detection_thread = threading.Thread(target=detect_death)
                death_detection_thread.daemon = True
                death_detection_thread.start()
                logger.info("Death detection thread started")
                self.status_var.set("Death detection started")
        else:
            # Death detection will stop on its own when the flag is set to False
            death_detection_active = False
            death_detection_thread = None
            logger.info("Death detection stopped")
            self.status_var.set("Death detection stopped")
        
        # Update the status indicator
        self._update_death_status_indicator()

    def _create_color_picker_overlay(self):
        """Create an overlay for picking colors from the screen."""
        # Create a transparent overlay for color selection
        color_overlay = tk.Toplevel(self.root)
        color_overlay.title("Color Picker")

        # Make it cover the entire screen
        screen_width, screen_height = pyautogui.size()
        color_overlay.geometry(f"{screen_width}x{screen_height}+0+0")

        # Make it semi-transparent and remove window decorations
        color_overlay.attributes("-alpha", 0.2)  # Semi-transparent
        color_overlay.attributes("-topmost", True)
        color_overlay.overrideredirect(True)  # Remove window decorations

        # Configure the overlay
        color_overlay.configure(bg="black")

        # Create a frame for instructions
        instruction_frame = tk.Frame(color_overlay, bg="black", bd=2, relief=tk.RAISED)
        instruction_frame.place(relx=0.5, rely=0.1, anchor=tk.CENTER)

        # Add instructions
        instruction_text = (
            "Click on the color you want to detect for death detection\n"
            "(usually the revival button color)\n\n"
            "Press ESC to cancel"
        )
        instruction_label = tk.Label(
            instruction_frame,
            text=instruction_text,
            font=("Arial", 16, "bold"),
            bg="black",
            fg="white",
            padx=20,
            pady=10
        )
        instruction_label.pack()

        # Create a frame for the color preview
        preview_frame = tk.Frame(color_overlay, bg="black", bd=2, relief=tk.RAISED)
        preview_frame.place(relx=0.5, rely=0.9, anchor=tk.CENTER)

        # Add color preview elements
        preview_label = tk.Label(
            preview_frame,
            text="Current Color: ",
            font=("Arial", 14),
            bg="black",
            fg="white"
        )
        preview_label.grid(row=0, column=0, padx=10, pady=10)

        # Color canvas for preview
        preview_canvas = tk.Canvas(
            preview_frame,
            width=50,
            height=30,
            bg="#000000",
            highlightthickness=1,
            highlightbackground="white"
        )
        preview_canvas.grid(row=0, column=1, padx=10, pady=10)

        # RGB value label
        rgb_var = tk.StringVar(value="RGB: (0, 0, 0)")
        rgb_label = tk.Label(
            preview_frame,
            textvariable=rgb_var,
            font=("Arial", 14),
            bg="black",
            fg="white"
        )
        rgb_label.grid(row=0, column=2, padx=10, pady=10)

        # Function to update preview on mouse movement
        def update_preview(event):
            try:
                # Get current mouse position
                x, y = event.x_root, event.y_root

                # Take a screenshot of a small region around the cursor
                sample_size = 5
                screenshot = pyautogui.screenshot(region=(x-2, y-2, sample_size, sample_size))

                # Convert to numpy array for processing
                img_array = np.array(screenshot)

                # Calculate the average color in the region
                r_avg = int(np.mean(img_array[:, :, 0]))
                g_avg = int(np.mean(img_array[:, :, 1]))
                b_avg = int(np.mean(img_array[:, :, 2]))

                # Create RGB color tuple
                rgb_color = (r_avg, g_avg, b_avg)

                # Update preview
                hex_color = self._rgb_to_hex(rgb_color)
                preview_canvas.config(bg=hex_color)
                rgb_var.set(f"RGB: {rgb_color}")
            except Exception as e:
                logger.error(f"Error updating color preview: {e}")

        # Function to handle the click
        def on_click(event):
            try:
                # Get current mouse position
                x, y = event.x_root, event.y_root

                # Take a screenshot of a small region around the cursor
                sample_size = 5
                screenshot = pyautogui.screenshot(region=(x-2, y-2, sample_size, sample_size))

                # Convert to numpy array for processing
                img_array = np.array(screenshot)

                # Calculate the average color in the region
                r_avg = int(np.mean(img_array[:, :, 0]))
                g_avg = int(np.mean(img_array[:, :, 1]))
                b_avg = int(np.mean(img_array[:, :, 2]))

                # Create RGB color tuple
                rgb_color = (r_avg, g_avg, b_avg)

                # Also get the exact pixel color
                exact_pixel = pyautogui.screenshot(region=(x, y, 1, 1)).getpixel((0, 0))

                # Update settings
                death_detection_settings['color'] = rgb_color

                # Update UI
                if hasattr(self, 'color_canvas'):
                    hex_color = self._rgb_to_hex(rgb_color)
                    self.color_canvas.config(bg=hex_color)

                # Update status with both average and exact colors
                self.status_var.set(f"Death detection color set to RGB: {rgb_color} (at {x}, {y})")
                logger.info(f"Death detection color updated to RGB: {rgb_color}, exact pixel: {exact_pixel} at ({x}, {y})")

                # Close the overlay
                color_overlay.destroy()

                # Show confirmation
                messagebox.showinfo(
                    "Color Captured",
                    f"Color captured successfully:\nRGB: {rgb_color}\nPosition: ({x}, {y})"
                )
            except Exception as e:
                logger.error(f"Error capturing color: {e}")
                messagebox.showerror("Error", f"Failed to capture color: {e}")
                color_overlay.destroy()

        # Function to handle escape key
        def on_escape(_):
            color_overlay.destroy()

        # Bind events
        color_overlay.bind("<Motion>", update_preview)
        color_overlay.bind("<Button-1>", on_click)
        color_overlay.bind("<Escape>", on_escape)

        # Wait for the overlay to be destroyed
        self.root.wait_window(color_overlay)

    def _pick_death_color_dialog(self):
        """Pick a color for death detection using a color picker dialog."""
        try:
            # Get current color from settings
            current_color = death_detection_settings['color']

            # Convert to hex for color chooser
            hex_color = self._rgb_to_hex(current_color)

            # Open color chooser dialog
            color_result = colorchooser.askcolor(
                initialcolor=hex_color,
                title="Select Death Screen Color"
            )

            # Check if a color was selected (not canceled)
            if color_result and color_result[0] is not None:
                # Extract RGB values and hex color
                rgb_values, hex_color = color_result

                # Convert to integer RGB tuple
                rgb_color = tuple(int(c) for c in rgb_values)

                # Update settings
                death_detection_settings['color'] = rgb_color

                # Update UI
                if hasattr(self, 'color_canvas'):
                    self.color_canvas.config(bg=hex_color)

                # Update status
                self.status_var.set(f"Death detection color set to RGB: {rgb_color}")
                logger.info(f"Death detection color updated to RGB: {rgb_color}")

                # Show confirmation
                messagebox.showinfo(
                    "Color Selected",
                    f"Death detection color set to:\nRGB: {rgb_color}\nHex: {hex_color}"
                )
            else:
                # User canceled the color selection
                logger.info("Color selection canceled by user")

        except Exception as e:
            logger.error(f"Error picking death color: {e}")
            messagebox.showerror("Error", f"Failed to set death detection color: {e}")

    def _select_region(self):
        """Select a region for death detection using a visual overlay."""
        try:
            # Create a region selection overlay
            self._create_region_selection_overlay()
        except Exception as e:
            logger.error(f"Error selecting region: {e}")
            messagebox.showerror("Error", f"Failed to select region: {e}")
            # Make sure the main window is visible
            self.root.deiconify()

    def _create_region_selection_overlay(self):
        """Create an overlay for selecting a region on the screen."""
        # Create a transparent overlay for region selection
        region_overlay = tk.Toplevel(self.root)
        region_overlay.title("Region Selection")

        # Make it cover the entire screen
        screen_width, screen_height = pyautogui.size()
        region_overlay.geometry(f"{screen_width}x{screen_height}+0+0")

        # Make it semi-transparent and remove window decorations
        region_overlay.attributes("-alpha", 0.3)  # Semi-transparent
        region_overlay.attributes("-topmost", True)
        region_overlay.overrideredirect(True)  # Remove window decorations

        # Configure the overlay
        region_overlay.configure(bg="black")

        # Create a frame for instructions
        instruction_frame = tk.Frame(region_overlay, bg="black", bd=2, relief=tk.RAISED)
        instruction_frame.place(relx=0.5, rely=0.1, anchor=tk.CENTER)

        # Add instructions
        instruction_text = (
            "Click and drag to select the region for death detection\n\n"
            "Press ESC to cancel"
        )
        instruction_label = tk.Label(
            instruction_frame,
            text=instruction_text,
            font=("Arial", 16, "bold"),
            bg="black",
            fg="white",
            padx=20,
            pady=10
        )
        instruction_label.pack()

        # Create a canvas for drawing the selection rectangle
        selection_canvas = tk.Canvas(
            region_overlay,
            bg="black",
            highlightthickness=0
        )
        selection_canvas.place(x=0, y=0, width=screen_width, height=screen_height)

        # Variables to track selection
        start_x = None
        start_y = None
        current_rectangle = None

        # Create a frame for the region info
        info_frame = tk.Frame(region_overlay, bg="black", bd=2, relief=tk.RAISED)
        info_frame.place(relx=0.5, rely=0.9, anchor=tk.CENTER)

        # Region info label
        region_info_var = tk.StringVar(value="Region: Not selected")
        region_info_label = tk.Label(
            info_frame,
            textvariable=region_info_var,
            font=("Arial", 14),
            bg="black",
            fg="white",
            padx=20,
            pady=10
        )
        region_info_label.pack()

        # Function to handle mouse button press (start selection)
        def on_press(event):
            nonlocal start_x, start_y, current_rectangle

            # Store starting position
            start_x = event.x
            start_y = event.y

            # Create a new rectangle if one doesn't exist
            if current_rectangle:
                selection_canvas.delete(current_rectangle)

            # Create initial rectangle
            current_rectangle = selection_canvas.create_rectangle(
                start_x, start_y, start_x, start_y,
                outline="white", width=2
            )

            # Update info
            region_info_var.set(f"Region: Starting at ({start_x}, {start_y})")

        # Function to handle mouse movement (update selection)
        def on_motion(event):
            nonlocal start_x, start_y, current_rectangle

            # Only update if we have a starting point
            if start_x is not None and start_y is not None and current_rectangle:
                # Update the rectangle
                selection_canvas.coords(current_rectangle, start_x, start_y, event.x, event.y)

                # Calculate width and height
                width = abs(event.x - start_x)
                height = abs(event.y - start_y)

                # Update info
                region_info_var.set(
                    f"Region: ({min(start_x, event.x)}, {min(start_y, event.y)}) to "
                    f"({max(start_x, event.x)}, {max(start_y, event.y)}) - Size: {width}x{height}"
                )

        # Function to handle mouse button release (complete selection)
        def on_release(event):
            nonlocal start_x, start_y, current_rectangle

            # Only process if we have a starting point
            if start_x is not None and start_y is not None:
                # Get the final coordinates
                end_x = event.x
                end_y = event.y

                # Make sure we have a valid selection (not just a click)
                if abs(end_x - start_x) > 10 and abs(end_y - start_y) > 10:
                    # Calculate the region
                    x1 = min(start_x, end_x)
                    y1 = min(start_y, end_y)
                    x2 = max(start_x, end_x)
                    y2 = max(start_y, end_y)

                    # Update settings
                    region = (x1, y1, x2, y2)
                    death_detection_settings['region'] = region
                    self.region_var.set(f"{x1}, {y1}, {x2}, {y2}")

                    # Update status
                    self.status_var.set(f"Detection region set to {region}")
                    logger.info(f"Detection region set to {region}")

                    # Close the overlay
                    region_overlay.destroy()

                    # Show confirmation
                    messagebox.showinfo(
                        "Region Selected",
                        f"Detection region set to:\n"
                        f"Top-left: ({x1}, {y1})\n"
                        f"Bottom-right: ({x2}, {y2})\n"
                        f"Size: {x2-x1}x{y2-y1} pixels"
                    )
                else:
                    # Selection too small, reset
                    if current_rectangle:
                        selection_canvas.delete(current_rectangle)
                        current_rectangle = None

                    start_x = None
                    start_y = None

                    # Update info
                    region_info_var.set("Region: Selection too small, try again")

        # Function to handle escape key
        def on_escape(_):
            region_overlay.destroy()
            self.status_var.set("Region selection canceled")
            logger.info("Region selection canceled")

        # Bind events
        selection_canvas.bind("<ButtonPress-1>", on_press)
        selection_canvas.bind("<B1-Motion>", on_motion)
        selection_canvas.bind("<ButtonRelease-1>", on_release)
        region_overlay.bind("<Escape>", on_escape)

        # Wait for the overlay to be destroyed
        self.root.wait_window(region_overlay)

    def _select_click_position(self):
        """Select a position to click when character dies."""
        global death_detection_settings

        # Create a transparent overlay for position selection
        position_overlay = tk.Toplevel(self.root)

        # Make it cover the entire screen
        screen_width, screen_height = pyautogui.size()
        position_overlay.geometry(f"{screen_width}x{screen_height}+0+0")

        # Make it semi-transparent and remove window decorations
        position_overlay.attributes("-alpha", 0.3)  # Semi-transparent
        position_overlay.attributes("-topmost", True)
        position_overlay.overrideredirect(True)  # Remove window decorations

        # Add instructions
        instruction_label = tk.Label(
            position_overlay,
            text="Click on the position you want to use for revival",
            font=("Arial", 24),
            bg="black",
            fg="white"
        )
        instruction_label.pack(pady=50)

        # Function to handle the click
        def on_click(event):
            x, y = event.x_root, event.y_root

            # Update click position
            death_detection_settings['click_position'] = (x, y)
            self.click_pos_var.set(f"({x}, {y})")

            # Close the overlay
            position_overlay.destroy()

            self.status_var.set(f"Revival click position set to ({x}, {y})")

        # Bind click event
        position_overlay.bind("<Button-1>", on_click)

        # Bind escape key to cancel
        position_overlay.bind("<Escape>", lambda _: position_overlay.destroy())

        # Wait for the overlay to be destroyed
        self.root.wait_window(position_overlay)

    def _clear_click_position(self):
        """Clear the revival click position."""
        global death_detection_settings

        if messagebox.askyesno("Clear Click Position", "Are you sure you want to clear the revival click position?"):
            death_detection_settings['click_position'] = None
            self.click_pos_var.set("Not set")
            self.status_var.set("Revival click position cleared")

    def _clear_region(self):
        """Clear the custom detection region and use default."""
        global death_detection_settings

        if messagebox.askyesno("Use Default Region", "Are you sure you want to use the default detection region (screen center)?"):
            death_detection_settings['region'] = None
            self.region_var.set("Default (screen center)")
            self.status_var.set("Using default detection region (screen center)")
            logger.info("Detection region reset to default")

    def _apply_death_settings(self):
        """Apply death detection settings."""
        global death_detection_settings, death_detection_active, death_detection_thread

        try:
            # Get values from UI
            death_detection_settings['enabled'] = self.death_enabled_var.get()
            death_detection_settings['check_interval'] = self.check_interval_var.get()

            # Get confidence value for template matching if it exists in UI
            if hasattr(self, 'confidence_var'):
                death_detection_settings['confidence'] = self.confidence_var.get()

            # Update status with detailed information
            status_message = []

            if death_detection_settings['enabled']:
                status_message.append("Death detection enabled")

                # Add click position info if available
                if death_detection_settings['click_position']:
                    x, y = death_detection_settings['click_position']
                    status_message.append(f"with click at ({x}, {y})")
                else:
                    status_message.append("but no click position set")

                # Add template info
                if os.path.exists(death_detection_settings['template_path']):
                    template_name = os.path.basename(death_detection_settings['template_path'])
                    status_message.append(f"using template '{template_name}'")
                else:
                    status_message.append("but no template set")
                
                # Add confidence info
                confidence = death_detection_settings['confidence']
                status_message.append(f"with confidence {confidence:.2f}")
            else:
                status_message.append("Death detection disabled")

            # Set the status message
            self.status_var.set(" ".join(status_message))

            # Log the settings
            logger.info(f"Death detection settings applied: {death_detection_settings}")

            # Update minimalist mode indicators if active
            if self.minimalist_mode and self.minimalist_window:
                self._update_minimalist_indicators()

            # Restart death detection if needed
            if death_detection_settings['enabled']:
                # Stop existing thread if running
                if death_detection_active and death_detection_thread and death_detection_thread.is_alive():
                    death_detection_active = False
                    # Wait for thread to stop
                    time.sleep(0.5)

                # Start a new thread
                death_detection_active = True
                death_detection_thread = threading.Thread(target=detect_death)
                death_detection_thread.daemon = True
                death_detection_thread.start()
                logger.info("Death detection thread started")
            else:
                # Stop the thread if running
                if death_detection_active:
                    death_detection_active = False
                    death_detection_thread = None
        except Exception as e:
            logger.error(f"Error applying death detection settings: {e}")
            messagebox.showerror("Error", f"Failed to apply settings: {e}")
    def _update_death_detection_setting(self, setting, value):
        """Update a death detection setting and save it."""
        global death_detection_settings
        death_detection_settings[setting] = value
        logger.info(f"Updated death detection setting: {setting} = {value}")
        
        # If death detection was just enabled, start the thread if not already running
        if setting == 'enabled' and value == True:
            global death_detection_active, death_detection_thread
            if not death_detection_active or (death_detection_thread and not death_detection_thread.is_alive()):
                death_detection_active = True
                death_detection_thread = threading.Thread(target=detect_death)
                death_detection_thread.daemon = True
                death_detection_thread.start()
                logger.info("Started death detection thread")
        
        # Save settings to a file
        self._save_death_detection_settings()
        
    def _capture_revival_template(self):
        """Capture a template of the revival button from the screen."""
        try:
            # Minimize the window to not interfere with the capture
            self.root.iconify()
            # Wait a moment for the window to minimize
            time.sleep(0.5)
            
            # Show a message to position the cursor
            messagebox.showinfo("Capture Template", "Position your cursor over the revival button and press OK.")
            
            # Get cursor position
            x, y = pyautogui.position()
            
            # Create a region around the cursor position
            template_width = 100  # Reasonable size for a button
            template_height = 50
            
            # Capture a screenshot of this region
            logger.info(f"Capturing revival button template at ({x}, {y})")
            capture_revival_button_template(x, y, template_width, template_height)
            
            # Update the UI with the new template
            if os.path.exists(death_detection_settings['template_path']):
                # Refresh the template display to show the new image
                self._refresh_template_display()
                
                # Show success message
                template_name = os.path.basename(death_detection_settings['template_path'])
                messagebox.showinfo("Success", f"Revival button template captured successfully!\n\nTemplate saved as: {template_name}")
            else:
                messagebox.showerror("Error", "Failed to capture template.")
            
            # Restore the window
            self.root.deiconify()
        except Exception as e:
            logger.error(f"Error capturing revival button template: {e}")
            messagebox.showerror("Error", f"Failed to capture template: {e}")
            # Restore the window
            self.root.deiconify()
    
    def _refresh_template_display(self):
        """Refresh the template image display with the current template."""
        try:
            # Get the template path
            template_path = death_detection_settings['template_path']
            
            # Update the template path display
            if os.path.exists(template_path):
                template_name = os.path.basename(template_path)
                self.template_path_var.set(template_name)
                
                # Use PIL to load and display the image
                try:
                    from PIL import Image, ImageTk
                    img = Image.open(template_path)
                    
                    # Resize while maintaining aspect ratio
                    max_size = (200, 200)
                    img.thumbnail(max_size, Image.LANCZOS)
                    
                    # Convert to PhotoImage for Tkinter
                    photo = ImageTk.PhotoImage(img)
                    
                    # Update the display
                    self.template_display.configure(image=photo, text="")
                    self.template_display.image = photo  # Keep a reference to prevent garbage collection
                    logger.info(f"Displayed template image: {template_path}")
                except Exception as e:
                    logger.error(f"Error loading template image for display: {e}")
                    self.template_display.configure(text=f"Error loading image: {e}", image="")
            else:
                self.template_path_var.set("Not set")
                self.template_display.configure(image="", text="No template image loaded")
        except Exception as e:
            logger.error(f"Error refreshing template display: {e}")
    
    def _browse_revival_template(self):
        """Browse for an existing revival button template image."""
        try:
            # Show file dialog to select an image file
            template_file = filedialog.askopenfilename(
                title="Select Revival Button Template",
                filetypes=[("Image files", "*.png;*.jpg;*.jpeg")],
                initialdir=os.path.dirname(death_detection_settings['template_path'])
            )
            
            if template_file:
                # Update the template path
                death_detection_settings['template_path'] = template_file
                
                # Refresh the template display
                self._refresh_template_display()
                
                logger.info(f"Revival button template updated to: {template_file}")
                messagebox.showinfo("Success", f"Revival button template updated to:\n{os.path.basename(template_file)}")
        except Exception as e:
            logger.error(f"Error browsing for revival button template: {e}")
            messagebox.showerror("Error", f"Failed to update template: {e}")
    
    def _save_death_detection_settings(self):
        """Save death detection settings to a file."""
        settings_file = "death_detection_settings.json"
        try:
            # Create a copy of settings to save (excluding objects that can't be serialized)
            settings_to_save = {}
            for key, value in death_detection_settings.items():
                if isinstance(value, (bool, int, float, str, list, dict)) or value is None:
                    settings_to_save[key] = value
            
            with open(settings_file, "w") as f:
                json.dump(settings_to_save, f, indent=4)
                logger.info("Saved death detection settings")
        except Exception as e:
            logger.error(f"Error saving death detection settings: {e}")
    
    def _update_confidence_display(self):
        """Update the confidence threshold display label."""
        if hasattr(self, 'confidence_display') and hasattr(self, 'confidence_var'):
            value = self.confidence_var.get()
            self.confidence_display.configure(text=f"{int(value*100)}%")
    
    def _capture_revival_template(self):
        """Capture the revival button template using the current mouse position."""
        # Show instructions
        messagebox.showinfo(
            "Capture Template",
            "Position your mouse over the revival button in your game and press OK. \n\n"
            "A screenshot will be taken around your mouse position to create a template."
        )
        
        # Capture the template
        if capture_revival_button_template():
            messagebox.showinfo("Success", "Revival button template captured successfully!")
            
            # Refresh the template display
            self._refresh_template_display()
        else:
            messagebox.showerror("Error", "Failed to capture template. Please try again.")
    
    def _browse_revival_template(self):
        """Browse for an existing image file to use as the revival button template."""
        file_path = filedialog.askopenfilename(
            title="Select Revival Button Template Image",
            filetypes=[("Image files", "*.png;*.jpg;*.jpeg;*.bmp")]
        )
        
        if file_path:
            try:
                # Create templates directory if it doesn't exist
                templates_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "templates")
                os.makedirs(templates_dir, exist_ok=True)
                
                # Copy the selected file to the templates directory
                import shutil
                template_path = death_detection_settings['template_path']
                shutil.copy(file_path, template_path)
                
                logger.info(f"Copied template from {file_path} to {template_path}")
                messagebox.showinfo("Success", "Revival button template updated successfully!")
                
                # Refresh the template display
                self._refresh_template_display()
            except Exception as e:
                logger.error(f"Error updating template: {e}")
                messagebox.showerror("Error", f"Failed to update template: {e}")
    
    def _refresh_template_display(self):
        """Refresh the template image display."""
        if hasattr(self, 'template_display_frame'):
            # Clear existing widgets
            for widget in self.template_display_frame.winfo_children():
                widget.destroy()
            
            # Check if template exists and display it
            template_path = death_detection_settings['template_path']
            if os.path.exists(template_path):
                try:
                    # Load and display the template image
                    img = tk.PhotoImage(file=template_path)
                    template_label = ttk.Label(self.template_display_frame, image=img)
                    template_label.image = img  # Keep a reference to prevent garbage collection
                    template_label.grid(row=0, column=0, sticky="nsew")
                except Exception as e:
                    template_label = ttk.Label(self.template_display_frame, text="Error loading template image")
                    template_label.grid(row=0, column=0, sticky="nsew")
                    logger.error(f"Error loading template image: {e}")
            else:
                template_label = ttk.Label(self.template_display_frame, text="No template image found")
                template_label.grid(row=0, column=0, sticky="nsew")

    def _disable_death_detection(self):
        """Disable death detection."""
        global death_detection_active, death_detection_thread
        try:
            if death_detection_active:
                death_detection_active = False
                death_detection_thread = None
                logger.info("Death detection stopped")
        except Exception as e:
            logger.error(f"Error disabling death detection: {e}")
            
    # Fix for previous method that had a try without except
    def _apply_death_settings(self):
        """Apply death detection settings."""
        global death_detection_settings, death_detection_active, death_detection_thread

        try:
            # Get values from UI
            death_detection_settings['enabled'] = self.death_enabled_var.get()
            death_detection_settings['check_interval'] = self.check_interval_var.get()

            # Get confidence value for template matching if it exists in UI
            if hasattr(self, 'confidence_var'):
                death_detection_settings['confidence'] = self.confidence_var.get()

            # Update status with detailed information
            status_message = []

            if death_detection_settings['enabled']:
                status_message.append("Death detection enabled")

                # Add click position info if available
                if death_detection_settings['click_position']:
                    x, y = death_detection_settings['click_position']
                    status_message.append(f"with click at ({x}, {y})")
                else:
                    status_message.append("but no click position set")

                # Add template info
                if os.path.exists(death_detection_settings['template_path']):
                    template_name = os.path.basename(death_detection_settings['template_path'])
                    status_message.append(f"using template '{template_name}'")
                else:
                    status_message.append("but no template set")
                
                # Add confidence info
                confidence = death_detection_settings['confidence']
                status_message.append(f"with confidence {confidence:.2f}")
            else:
                status_message.append("Death detection disabled")

            # Set the status message
            self.status_var.set(" ".join(status_message))

            # Log the settings
            logger.info(f"Death detection settings applied: {death_detection_settings}")

            # Update minimalist mode indicators if active
            if self.minimalist_mode and self.minimalist_window:
                self._update_minimalist_indicators()

            # Restart death detection if needed
            if death_detection_settings['enabled']:
                # Stop existing thread if running
                if death_detection_active and death_detection_thread and death_detection_thread.is_alive():
                    death_detection_active = False
                    # Wait for thread to stop
                    time.sleep(0.5)

                # Start a new thread
                death_detection_active = True
                death_detection_thread = threading.Thread(target=detect_death)
                death_detection_thread.daemon = True
                death_detection_thread.start()
                logger.info("Death detection thread started")
            else:
                # Stop the thread if running
                if death_detection_active:
                    death_detection_active = False
                    death_detection_thread = None
        except Exception as e:
            logger.error(f"Error applying death detection settings: {e}")
            messagebox.showerror("Error", f"Failed to apply settings: {e}")

    def _update_actions_list(self):
        """Update the actions listbox with enhanced formatting and visual indicators."""
        self.actions_listbox.delete(0, tk.END)

        # Update the header with count
        action_count = len(recorded_actions)
        self.actions_header_var.set(f"Recorded Actions: {action_count}")

        # Format and insert each action
        for i, action in enumerate(recorded_actions):
            # Format the index with padding for better alignment
            index = f"{i+1:3d}"

            # Format the delay with consistent width and better presentation
            delay_value = action['delay']
            if delay_value < 0.01:
                delay_str = "No delay"
            elif delay_value < 0.1:
                delay_str = f"Quick ({delay_value:.2f}s)"
            elif delay_value < 0.5:
                delay_str = f"Short ({delay_value:.2f}s)"
            elif delay_value < 1.0:
                delay_str = f"Medium ({delay_value:.2f}s)"
            elif delay_value < 2.0:
                delay_str = f"Long ({delay_value:.2f}s)"
            else:
                delay_str = f"Very long ({delay_value:.2f}s)"

            if action['type'] == 'key':
                # Format key actions with better visual indicators
                key_name = action['key'].upper() if len(action['key']) == 1 else action['key']

                # Special formatting for common keys
                if key_name.lower() in ['enter', 'return']:
                    key_display = "⏎ Enter"
                elif key_name.lower() == 'space':
                    key_display = "␣ Space"
                elif key_name.lower() == 'tab':
                    key_display = "⇥ Tab"
                elif key_name.lower() == 'esc':
                    key_display = "⎋ Esc"
                elif key_name.lower() == 'backspace':
                    key_display = "⌫ Backspace"
                elif key_name.lower() in ['shift', 'shift_l', 'shift_r']:
                    key_display = "⇧ Shift"
                elif key_name.lower() in ['ctrl', 'ctrl_l', 'ctrl_r']:
                    key_display = "⌃ Ctrl"
                elif key_name.lower() in ['alt', 'alt_l', 'alt_r']:
                    key_display = "⌥ Alt"
                elif key_name.lower() == 'up':
                    key_display = "↑ Up"
                elif key_name.lower() == 'down':
                    key_display = "↓ Down"
                elif key_name.lower() == 'left':
                    key_display = "← Left"
                elif key_name.lower() == 'right':
                    key_display = "→ Right"
                elif key_name.lower().startswith('f') and key_name[1:].isdigit():
                    # Function keys (F1-F12)
                    key_display = f"⊞ {key_name.upper()}"
                else:
                    # Regular keys
                    key_display = f"⌨ {key_name}"

                # Create a more visually appealing format with better spacing and alignment
                action_str = f"{index}. Key: {key_display:<15} | {delay_str}"

                # Add to listbox with color coding
                self.actions_listbox.insert(tk.END, action_str)
                self.actions_listbox.itemconfig(i, foreground=self.colors['accent'])  # Blue for key presses

            elif action['type'] == 'mouse':
                # Format mouse actions with better visual indicators
                button = action['button'].capitalize()

                # Add appropriate mouse icon and description based on button
                if button == 'Left':
                    button_display = "🖱️ Left Click"
                elif button == 'Right':
                    button_display = "🖱️ Right Click"
                elif button == 'Middle':
                    button_display = "🖱️ Middle Click"
                else:
                    button_display = f"🖱️ {button} Click"

                # Format coordinates with better presentation
                coords = f"({action['x']}, {action['y']})"

                # Create a more visually appealing format with better spacing and alignment
                action_str = f"{index}. {button_display:<15} at {coords:<10} | {delay_str}"

                # Add to listbox with color coding
                self.actions_listbox.insert(tk.END, action_str)
                self.actions_listbox.itemconfig(i, foreground=self.colors['secondary'])  # Teal for mouse clicks

            # Add alternating row background for better readability
            if i % 2 == 1:
                self.actions_listbox.itemconfig(i, background='#1c2833')  # Slightly darker background for odd rows

    def _show_action_context_menu(self, event):
        """Show context menu for actions."""
        # Get the selected index
        try:
            index = self.actions_listbox.nearest(event.y)
            if index < 0 or index >= len(recorded_actions):
                return

            # Select the item under cursor
            self.actions_listbox.selection_clear(0, tk.END)
            self.actions_listbox.selection_set(index)
            self.actions_listbox.activate(index)

            # Create context menu
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="Edit", command=self._edit_selected_action)
            context_menu.add_command(label="Delete", command=self._delete_selected_action)
            context_menu.add_separator()
            context_menu.add_command(label="Move Up", command=self._move_action_up)
            context_menu.add_command(label="Move Down", command=self._move_action_down)

            # Display the menu
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
        except Exception as e:
            logger.error(f"Error showing context menu: {e}")

    def _edit_selected_action(self, _=None):
        """Edit the selected action."""
        global recorded_actions

        # Get selected index
        try:
            selected = self.actions_listbox.curselection()
            if not selected:
                messagebox.showinfo("Edit Action", "Please select an action to edit")
                return

            index = selected[0]
            if index < 0 or index >= len(recorded_actions):
                return

            action = recorded_actions[index]

            # Create edit dialog
            edit_dialog = tk.Toplevel(self.root)
            edit_dialog.title("Edit Action")
            edit_dialog.geometry("400x300")
            edit_dialog.resizable(False, False)
            edit_dialog.transient(self.root)
            edit_dialog.grab_set()

            # Configure dialog
            edit_dialog.columnconfigure(0, weight=1)

            # Action type (read-only)
            type_frame = ttk.Frame(edit_dialog, padding=10)
            type_frame.grid(row=0, column=0, sticky="ew")

            ttk.Label(type_frame, text="Action Type:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky="w")
            action_type = "Key Press" if action['type'] == 'key' else "Mouse Click"
            ttk.Label(type_frame, text=action_type, font=('Arial', 10)).grid(row=0, column=1, sticky="w", padx=10)

            # Delay
            delay_frame = ttk.Frame(edit_dialog, padding=10)
            delay_frame.grid(row=1, column=0, sticky="ew")

            ttk.Label(delay_frame, text="Delay (seconds):", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky="w")
            delay_var = tk.DoubleVar(value=action['delay'])
            delay_entry = ttk.Spinbox(delay_frame, from_=0, to=10, increment=0.1, textvariable=delay_var, width=10)
            delay_entry.grid(row=0, column=1, sticky="w", padx=10)

            # Action-specific fields
            if action['type'] == 'key':
                # Key action
                key_frame = ttk.Frame(edit_dialog, padding=10)
                key_frame.grid(row=2, column=0, sticky="ew")

                ttk.Label(key_frame, text="Key:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky="w")
                key_var = tk.StringVar(value=action['key'])
                key_entry = ttk.Entry(key_frame, textvariable=key_var, width=15)
                key_entry.grid(row=0, column=1, sticky="w", padx=10)

                # Common keys dropdown
                common_keys = ["enter", "space", "tab", "esc", "backspace", "shift", "ctrl", "alt",
                              "up", "down", "left", "right", "f1", "f2", "f3", "f4", "f5", "f6", "f7", "f8"]
                ttk.Label(key_frame, text="Common Keys:").grid(row=1, column=0, sticky="w", pady=5)
                key_dropdown = ttk.Combobox(key_frame, values=common_keys, width=15)
                key_dropdown.grid(row=1, column=1, sticky="w", padx=10, pady=5)

                # Bind dropdown to update entry
                def update_key(_):
                    key_var.set(key_dropdown.get())
                key_dropdown.bind("<<ComboboxSelected>>", update_key)

            elif action['type'] == 'mouse':
                # Mouse action
                mouse_frame = ttk.Frame(edit_dialog, padding=10)
                mouse_frame.grid(row=2, column=0, sticky="ew")

                # Button
                ttk.Label(mouse_frame, text="Button:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky="w")
                button_var = tk.StringVar(value=action['button'])
                button_dropdown = ttk.Combobox(mouse_frame, textvariable=button_var, values=["left", "right", "middle"], width=10)
                button_dropdown.grid(row=0, column=1, sticky="w", padx=10)

                # Coordinates
                coords_frame = ttk.Frame(edit_dialog, padding=10)
                coords_frame.grid(row=3, column=0, sticky="ew")

                ttk.Label(coords_frame, text="X Coordinate:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky="w")
                x_var = tk.IntVar(value=action['x'])
                x_entry = ttk.Spinbox(coords_frame, from_=0, to=3000, textvariable=x_var, width=10)
                x_entry.grid(row=0, column=1, sticky="w", padx=10)

                ttk.Label(coords_frame, text="Y Coordinate:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky="w", pady=5)
                y_var = tk.IntVar(value=action['y'])
                y_entry = ttk.Spinbox(coords_frame, from_=0, to=3000, textvariable=y_var, width=10)
                y_entry.grid(row=1, column=1, sticky="w", padx=10, pady=5)

                # Current position button
                def use_current_position():
                    x, y = pyautogui.position()
                    x_var.set(x)
                    y_var.set(y)

                current_pos_button = tk.Button(
                    coords_frame,
                    text="Use Current Position",
                    command=use_current_position,
                    bg=self.colors['accent'],
                    fg="white",
                    font=('Arial', 9),
                    relief=tk.RAISED,
                    borderwidth=2
                )
                current_pos_button.grid(row=2, column=0, columnspan=2, pady=5)

            # Buttons frame
            buttons_frame = ttk.Frame(edit_dialog, padding=10)
            buttons_frame.grid(row=10, column=0, sticky="ew")
            buttons_frame.columnconfigure(0, weight=1)
            buttons_frame.columnconfigure(1, weight=1)

            # Save function
            def save_changes():
                try:
                    # Update delay for all action types
                    action['delay'] = delay_var.get()

                    # Update action-specific fields
                    if action['type'] == 'key':
                        action['key'] = key_var.get()
                    elif action['type'] == 'mouse':
                        action['button'] = button_var.get()
                        action['x'] = x_var.get()
                        action['y'] = y_var.get()

                    # Update the display
                    self._update_actions_list()

                    # Close dialog
                    edit_dialog.destroy()

                    # Show success message
                    self.status_var.set(f"Action {index+1} updated successfully")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to save changes: {e}")

            # Save button
            save_button = tk.Button(
                buttons_frame,
                text="Save Changes",
                command=save_changes,
                bg=self.colors['success'],
                fg="white",
                font=('Arial', 10, 'bold'),
                width=15,
                relief=tk.RAISED,
                borderwidth=2
            )
            save_button.grid(row=0, column=0, padx=5, pady=10)

            # Cancel button
            cancel_button = tk.Button(
                buttons_frame,
                text="Cancel",
                command=edit_dialog.destroy,
                bg=self.colors['warning'],
                fg="white",
                font=('Arial', 10, 'bold'),
                width=15,
                relief=tk.RAISED,
                borderwidth=2
            )
            cancel_button.grid(row=0, column=1, padx=5, pady=10)

            # Center the dialog on the parent window
            edit_dialog.update_idletasks()
            width = edit_dialog.winfo_width()
            height = edit_dialog.winfo_height()
            x = self.root.winfo_rootx() + (self.root.winfo_width() - width) // 2
            y = self.root.winfo_rooty() + (self.root.winfo_height() - height) // 2
            edit_dialog.geometry(f"{width}x{height}+{x}+{y}")

            # Make dialog modal
            edit_dialog.focus_set()
            edit_dialog.wait_window()

        except Exception as e:
            logger.error(f"Error editing action: {e}")
            messagebox.showerror("Error", f"Failed to edit action: {e}")

    def _delete_selected_action(self):
        """Delete the selected action."""
        global recorded_actions

        # Get selected index
        try:
            selected = self.actions_listbox.curselection()
            if not selected:
                messagebox.showinfo("Delete Action", "Please select an action to delete")
                return

            index = selected[0]
            if index < 0 or index >= len(recorded_actions):
                return

            # Confirm deletion
            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete action {index+1}?"):
                # Remove the action
                del recorded_actions[index]

                # Update the display
                self._update_actions_list()

                # Show success message
                self.status_var.set(f"Action {index+1} deleted successfully")
        except Exception as e:
            logger.error(f"Error deleting action: {e}")
            messagebox.showerror("Error", f"Failed to delete action: {e}")

    def _move_action_up(self):
        """Move the selected action up in the list."""
        global recorded_actions

        # Get selected index
        try:
            selected = self.actions_listbox.curselection()
            if not selected:
                messagebox.showinfo("Move Action", "Please select an action to move")
                return

            index = selected[0]
            if index <= 0 or index >= len(recorded_actions):
                return

            # Swap with previous action
            recorded_actions[index], recorded_actions[index-1] = recorded_actions[index-1], recorded_actions[index]

            # Update the display
            self._update_actions_list()

            # Select the moved item
            self.actions_listbox.selection_clear(0, tk.END)
            self.actions_listbox.selection_set(index-1)
            self.actions_listbox.see(index-1)

            # Show success message
            self.status_var.set(f"Action {index+1} moved up successfully")
        except Exception as e:
            logger.error(f"Error moving action up: {e}")
            messagebox.showerror("Error", f"Failed to move action: {e}")

    def _move_action_down(self):
        """Move the selected action down in the list."""
        global recorded_actions

        # Get selected index
        try:
            selected = self.actions_listbox.curselection()
            if not selected:
                messagebox.showinfo("Move Action", "Please select an action to move")
                return

            index = selected[0]
            if index < 0 or index >= len(recorded_actions) - 1:
                return

            # Swap with next action
            recorded_actions[index], recorded_actions[index+1] = recorded_actions[index+1], recorded_actions[index]

            # Update the display
            self._update_actions_list()

            # Select the moved item
            self.actions_listbox.selection_clear(0, tk.END)
            self.actions_listbox.selection_set(index+1)
            self.actions_listbox.see(index+1)

            # Show success message
            self.status_var.set(f"Action {index+1} moved down successfully")
        except Exception as e:
            logger.error(f"Error moving action down: {e}")
            messagebox.showerror("Error", f"Failed to move action: {e}")

    def _on_action_select(self, _):
        """Handle action selection in the listbox."""
        try:
            # Get selected index
            selected = self.actions_listbox.curselection()
            if not selected:
                return

            index = selected[0]
            if index < 0 or index >= len(recorded_actions):
                return

            # Get the action
            action = recorded_actions[index]

            # Update status bar with action details
            if action['type'] == 'key':
                self.status_var.set(f"Selected: Key '{action['key']}' with {action['delay']:.2f}s delay")
            elif action['type'] == 'mouse':
                self.status_var.set(f"Selected: {action['button']} click at ({action['x']}, {action['y']}) with {action['delay']:.2f}s delay")

        except Exception as e:
            logger.error(f"Error handling action selection: {e}")

    def _add_new_action(self):
        """Add a new action manually."""
        global recorded_actions

        # Create add dialog
        add_dialog = tk.Toplevel(self.root)
        add_dialog.title("Add New Action")
        add_dialog.geometry("400x300")
        add_dialog.resizable(False, False)
        add_dialog.transient(self.root)
        add_dialog.grab_set()

        # Configure dialog
        add_dialog.columnconfigure(0, weight=1)

        # Action type selection
        type_frame = ttk.Frame(add_dialog, padding=10)
        type_frame.grid(row=0, column=0, sticky="ew")

        ttk.Label(type_frame, text="Action Type:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky="w")
        action_type_var = tk.StringVar(value="key")

        # Radio buttons for action type
        key_radio = ttk.Radiobutton(type_frame, text="Key Press", variable=action_type_var, value="key")
        key_radio.grid(row=0, column=1, sticky="w", padx=10)

        mouse_radio = ttk.Radiobutton(type_frame, text="Mouse Click", variable=action_type_var, value="mouse")
        mouse_radio.grid(row=0, column=2, sticky="w", padx=10)

        # Delay
        delay_frame = ttk.Frame(add_dialog, padding=10)
        delay_frame.grid(row=1, column=0, sticky="ew")

        ttk.Label(delay_frame, text="Delay (seconds):", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky="w")
        delay_var = tk.DoubleVar(value=0.5)
        delay_entry = ttk.Spinbox(delay_frame, from_=0, to=10, increment=0.1, textvariable=delay_var, width=10)
        delay_entry.grid(row=0, column=1, sticky="w", padx=10)

        # Key action frame
        key_frame = ttk.LabelFrame(add_dialog, text="Key Press Settings", padding=10)
        key_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=5)

        ttk.Label(key_frame, text="Key:", font=('Arial', 10)).grid(row=0, column=0, sticky="w")
        key_var = tk.StringVar(value="")
        key_entry = ttk.Entry(key_frame, textvariable=key_var, width=15)
        key_entry.grid(row=0, column=1, sticky="w", padx=10)

        # Common keys dropdown
        common_keys = ["enter", "space", "tab", "esc", "backspace", "shift", "ctrl", "alt",
                      "up", "down", "left", "right", "f1", "f2", "f3", "f4", "f5", "f6", "f7", "f8"]
        ttk.Label(key_frame, text="Common Keys:").grid(row=1, column=0, sticky="w", pady=5)
        key_dropdown = ttk.Combobox(key_frame, values=common_keys, width=15)
        key_dropdown.grid(row=1, column=1, sticky="w", padx=10, pady=5)

        # Bind dropdown to update entry
        def update_key(_):
            key_var.set(key_dropdown.get())
        key_dropdown.bind("<<ComboboxSelected>>", update_key)

        # Mouse action frame
        mouse_frame = ttk.LabelFrame(add_dialog, text="Mouse Click Settings", padding=10)
        mouse_frame.grid(row=3, column=0, sticky="ew", padx=10, pady=5)

        # Button
        ttk.Label(mouse_frame, text="Button:", font=('Arial', 10)).grid(row=0, column=0, sticky="w")
        button_var = tk.StringVar(value="left")
        button_dropdown = ttk.Combobox(mouse_frame, textvariable=button_var, values=["left", "right", "middle"], width=10)
        button_dropdown.grid(row=0, column=1, sticky="w", padx=10)

        # Coordinates
        ttk.Label(mouse_frame, text="X Coordinate:", font=('Arial', 10)).grid(row=1, column=0, sticky="w", pady=5)
        x_var = tk.IntVar(value=0)
        x_entry = ttk.Spinbox(mouse_frame, from_=0, to=3000, textvariable=x_var, width=10)
        x_entry.grid(row=1, column=1, sticky="w", padx=10, pady=5)

        ttk.Label(mouse_frame, text="Y Coordinate:", font=('Arial', 10)).grid(row=2, column=0, sticky="w")
        y_var = tk.IntVar(value=0)
        y_entry = ttk.Spinbox(mouse_frame, from_=0, to=3000, textvariable=y_var, width=10)
        y_entry.grid(row=2, column=1, sticky="w", padx=10)

        # Current position button
        def use_current_position():
            x, y = pyautogui.position()
            x_var.set(x)
            y_var.set(y)

        current_pos_button = tk.Button(
            mouse_frame,
            text="Use Current Position",
            command=use_current_position,
            bg=self.colors['accent'],
            fg="white",
            font=('Arial', 9),
            relief=tk.RAISED,
            borderwidth=2
        )
        current_pos_button.grid(row=3, column=0, columnspan=2, pady=5)

        # Function to show/hide frames based on action type
        def update_frames(_=None):
            action_type = action_type_var.get()
            if action_type == "key":
                key_frame.grid()
                mouse_frame.grid_remove()
            else:
                key_frame.grid_remove()
                mouse_frame.grid()

        # Set initial state
        action_type_var.trace_add("write", update_frames)
        update_frames()

        # Buttons frame
        buttons_frame = ttk.Frame(add_dialog, padding=10)
        buttons_frame.grid(row=10, column=0, sticky="ew")
        buttons_frame.columnconfigure(0, weight=1)
        buttons_frame.columnconfigure(1, weight=1)

        # Add function
        def add_action():
            try:
                action_type = action_type_var.get()

                # Create new action
                if action_type == "key":
                    key = key_var.get()
                    if not key:
                        messagebox.showwarning("Warning", "Please enter a key")
                        return

                    new_action = {
                        'type': 'key',
                        'key': key,
                        'delay': delay_var.get()
                    }
                else:
                    new_action = {
                        'type': 'mouse',
                        'button': button_var.get(),
                        'x': x_var.get(),
                        'y': y_var.get(),
                        'delay': delay_var.get()
                    }

                # Add to recorded actions
                recorded_actions.append(new_action)

                # Update the display
                self._update_actions_list()

                # Select the new item
                new_index = len(recorded_actions) - 1
                self.actions_listbox.selection_clear(0, tk.END)
                self.actions_listbox.selection_set(new_index)
                self.actions_listbox.see(new_index)

                # Close dialog
                add_dialog.destroy()

                # Show success message
                self.status_var.set(f"New {action_type} action added successfully")
            except Exception as e:
                logger.error(f"Error adding action: {e}")
                messagebox.showerror("Error", f"Failed to add action: {e}")

        # Add button
        add_button = tk.Button(
            buttons_frame,
            text="Add Action",
            command=add_action,
            bg=self.colors['success'],
            fg="white",
            font=('Arial', 10, 'bold'),
            width=15,
            relief=tk.RAISED,
            borderwidth=2
        )
        add_button.grid(row=0, column=0, padx=5, pady=10)

        # Cancel button
        cancel_button = tk.Button(
            buttons_frame,
            text="Cancel",
            command=add_dialog.destroy,
            bg=self.colors['warning'],
            fg="white",
            font=('Arial', 10, 'bold'),
            width=15,
            relief=tk.RAISED,
            borderwidth=2
        )
        cancel_button.grid(row=0, column=1, padx=5, pady=10)

        # Center the dialog on the parent window
        add_dialog.update_idletasks()
        width = add_dialog.winfo_width()
        height = add_dialog.winfo_height()
        x = self.root.winfo_rootx() + (self.root.winfo_width() - width) // 2
        y = self.root.winfo_rooty() + (self.root.winfo_height() - height) // 2
        add_dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Make dialog modal
        add_dialog.focus_set()
        add_dialog.wait_window()

# Overlay window class
class OverlayWindow:
    def __init__(self):
        # Create a transparent overlay window
        self.overlay = tk.Tk()
        self.overlay.title("Macro Recorder Overlay")
        self.overlay.attributes("-alpha", 0.01)  # Almost completely transparent
        self.overlay.attributes("-topmost", True)  # Always on top
        self.overlay.overrideredirect(True)  # No window decorations

        # Make it cover the entire screen
        screen_width, screen_height = self.overlay.winfo_screenwidth(), self.overlay.winfo_screenheight()
        self.overlay.geometry(f"{screen_width}x{screen_height}+0+0")

        # Set window style to prevent focus stealing
        try:
            hwnd = int(self.overlay.winfo_id())
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
            new_style = style | win32con.WS_EX_NOACTIVATE | win32con.WS_EX_TRANSPARENT | win32con.WS_EX_LAYERED
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, new_style)
            logger.info("Applied overlay window styles")
        except Exception as e:
            logger.error(f"Error setting overlay window style: {e}")

        # Create a control panel window
        self.control_panel = tk.Toplevel(self.overlay)
        self.control_panel.title("Macro Recorder Controls")
        self.control_panel.geometry("300x200+50+50")
        self.control_panel.attributes("-topmost", True)
        self.control_panel.overrideredirect(True)  # No window decorations

        # Set control panel style to prevent focus stealing
        try:
            hwnd = int(self.control_panel.winfo_id())
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
            new_style = style | win32con.WS_EX_NOACTIVATE | win32con.WS_EX_TOOLWINDOW
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, new_style)
            logger.info("Applied control panel styles")
        except Exception as e:
            logger.error(f"Error setting control panel style: {e}")

        # Add a title bar to the control panel
        self.title_bar = tk.Frame(self.control_panel, bg="#3498db", height=25)
        self.title_bar.pack(fill=tk.X)

        # Add title text
        title_label = tk.Label(
            self.title_bar,
            text="Rappelz Macro Recorder",
            bg="#3498db",
            fg="white",
            font=('Arial', 10, 'bold')
        )
        title_label.pack(side=tk.LEFT, padx=5)

        # Add a close button
        close_button = tk.Button(
            self.title_bar,
            text="×",
            bg="#3498db",
            fg="white",
            font=('Arial', 12, 'bold'),
            bd=0,
            command=self.overlay.destroy
        )
        close_button.pack(side=tk.RIGHT)

        # Add a minimize button
        minimize_button = tk.Button(
            self.title_bar,
            text="_",
            bg="#3498db",
            fg="white",
            font=('Arial', 12),
            bd=0,
            command=self._minimize
        )
        minimize_button.pack(side=tk.RIGHT)

        # Add a settings button to open the main app
        settings_button = tk.Button(
            self.title_bar,
            text="⚙",
            bg="#3498db",
            fg="white",
            font=('Arial', 12),
            bd=0,
            command=self._open_settings
        )
        settings_button.pack(side=tk.RIGHT)

        # Variables to track window movement
        self.dragging = False
        self.drag_start_x = 0
        self.drag_start_y = 0

        # Bind mouse events for dragging
        self.title_bar.bind("<ButtonPress-1>", self._start_drag)
        self.title_bar.bind("<ButtonRelease-1>", self._stop_drag)
        self.title_bar.bind("<B1-Motion>", self._on_drag)

        # Also bind the title label
        title_label.bind("<ButtonPress-1>", self._start_drag)
        title_label.bind("<ButtonRelease-1>", self._stop_drag)
        title_label.bind("<B1-Motion>", self._on_drag)

        # Create the main content frame
        content_frame = tk.Frame(self.control_panel, bg="#f0f0f0")
        content_frame.pack(fill=tk.BOTH, expand=True)

        # Add buttons for recording and playback
        record_button = tk.Button(
            content_frame,
            text="Start Recording (F8)",
            command=self._toggle_recording,
            bg="#e74c3c",
            fg="white",
            font=('Arial', 10, 'bold'),
            relief=tk.RAISED,
            borderwidth=2
        )
        record_button.pack(pady=10)

        playback_button = tk.Button(
            content_frame,
            text="Start Playback (F9)",
            command=self._toggle_playback,
            bg="#2ecc71",
            fg="white",
            font=('Arial', 10, 'bold'),
            relief=tk.RAISED,
            borderwidth=2
        )
        playback_button.pack(pady=10)

        stop_button = tk.Button(
            content_frame,
            text="Stop (F11)",
            command=self._stop_playback,
            bg="#f39c12",
            fg="white",
            font=('Arial', 10, 'bold'),
            relief=tk.RAISED,
            borderwidth=2
        )
        stop_button.pack(pady=10)

        # Set up keyboard hooks
        if HAVE_KEYBOARD:
            try:
                # Register hotkeys
                keyboard.add_hotkey('f8', self._toggle_recording)
                keyboard.add_hotkey('f9', self._toggle_playback)
                keyboard.add_hotkey('f11', self._stop_playback)
                logger.info("Keyboard hotkeys registered for overlay mode")
            except Exception as e:
                logger.error(f"Error setting up keyboard hooks in overlay mode: {e}")

        # Create the main app window but don't show it
        self.main_app_root = None
        self.main_app = None

    def _start_drag(self, event):
        """Start window dragging."""
        self.dragging = True
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root

    def _stop_drag(self, _):
        """Stop window dragging."""
        self.dragging = False

    def _on_drag(self, event):
        """Handle window dragging."""
        if self.dragging:
            # Calculate the distance moved
            dx = event.x_root - self.drag_start_x
            dy = event.y_root - self.drag_start_y

            # Get current window position
            x = self.control_panel.winfo_x() + dx
            y = self.control_panel.winfo_y() + dy

            # Move the window
            self.control_panel.geometry(f"+{x}+{y}")

            # Update drag start position
            self.drag_start_x = event.x_root
            self.drag_start_y = event.y_root

    def _minimize(self):
        """Minimize the control panel."""
        # Hide the control panel
        self.control_panel.withdraw()

        # Create a small icon to restore it
        self.restore_icon = tk.Toplevel(self.overlay)
        self.restore_icon.geometry("30x30+0+0")
        self.restore_icon.overrideredirect(True)
        self.restore_icon.attributes("-topmost", True)

        # Set icon style to prevent focus stealing
        try:
            hwnd = int(self.restore_icon.winfo_id())
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
            new_style = style | win32con.WS_EX_NOACTIVATE | win32con.WS_EX_TOOLWINDOW
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, new_style)
        except Exception as e:
            logger.error(f"Error setting restore icon style: {e}")

        # Add a button to restore
        restore_button = tk.Button(
            self.restore_icon,
            text="▲",
            bg="#3498db",
            fg="white",
            font=('Arial', 10, 'bold'),
            command=self._restore
        )
        restore_button.pack(fill=tk.BOTH, expand=True)

    def _restore(self):
        """Restore the control panel."""
        # Destroy the restore icon
        if hasattr(self, 'restore_icon') and self.restore_icon is not None:
            self.restore_icon.destroy()
            self.restore_icon = None

        # Show the control panel
        self.control_panel.deiconify()

    def _open_settings(self):
        """Open the main application settings."""
        if self.main_app_root is None:
            # Create the main app window
            self.main_app_root = tk.Toplevel(self.overlay)
            self.main_app_root.title("Rappelz Macro Recorder Settings")
            self.main_app_root.geometry("800x600+100+100")
            self.main_app_root.attributes("-topmost", True)

            # Create the main app
            self.main_app = MacroRecorderGUI(self.main_app_root)
        else:
            # Show the main app window
            self.main_app_root.deiconify()
            self.main_app_root.lift()

    def _toggle_recording(self):
        """Toggle recording state."""
        global recording
        recording = not recording

        if recording:
            logger.info("Recording started from overlay")
            # Reset recording state
            global last_action_time
            last_action_time = None

            # Set up keyboard hook for recording
            if HAVE_KEYBOARD:
                try:
                    # Set up a new hook
                    keyboard.on_press(self._on_key_press)
                    logger.info("Keyboard recording hook enabled from overlay")
                except Exception as e:
                    logger.error(f"Error setting up keyboard recording hook from overlay: {e}")

            # Set up mouse hook for recording
            if HAVE_MOUSE:
                try:
                    # Set up mouse hook for clicks only
                    mouse.on_click(lambda x, y, button, pressed:
                        self._on_mouse_click(x, y, button) if pressed else None)
                    logger.info("Mouse click hook enabled from overlay")
                except Exception as e:
                    logger.error(f"Error setting up mouse click hook from overlay: {e}")
        else:
            logger.info("Recording stopped from overlay")

    def _on_key_press(self, event):
        """Handle key press events for recording."""
        if recording:
            key = event.name
            record_key_press(key)

    def _on_mouse_click(self, x, y, button):
        """Handle mouse click events for recording."""
        if recording:
            button_name = button.name if hasattr(button, 'name') else 'left'
            record_mouse_click(button_name, x, y)

    def _toggle_playback(self):
        """Toggle playback state."""
        global running, playback_thread

        if running and playback_thread and playback_thread.is_alive():
            # Stop playback
            self._stop_playback()
        else:
            # Start playback
            running = True
            playback_thread = threading.Thread(
                target=playback_actions,
                args=(1, 0.5, False)  # Default values
            )
            playback_thread.daemon = True
            playback_thread.start()
            logger.info("Playback started from overlay")

    def _stop_playback(self):
        """Stop playback."""
        global running
        running = False
        logger.info("Playback stopped from overlay")

# Main function
def main():
    # Create the normal window
    root = tk.Tk()
    MacroRecorderGUI(root)  # Create the GUI
    root.mainloop()

if __name__ == "__main__":
    main()
