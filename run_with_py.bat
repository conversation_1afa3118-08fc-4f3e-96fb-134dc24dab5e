@echo off
echo Running Game Macro Recorder as Administrator...
echo.

:: Set Python path
set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe

:: Check if Python exists at the specified path
if not exist "%PYTHON_PATH%" (
    echo Python not found at %PYTHON_PATH%
    echo Please check your Python installation.
    pause
    exit /b
)

:: Check if the script exists
if not exist "game_macro_recorder.py" (
    echo game_macro_recorder.py not found in the current directory.
    pause
    exit /b
)

:: Install required libraries
echo Installing required libraries...
"%PYTHON_PATH%" -m pip install pyautogui keyboard mouse pywin32 numpy pillow opencv-python --quiet

:: Make sure critical modules are installed (sometimes fails silently)
"%PYTHON_PATH%" -m pip install mouse --quiet
"%PYTHON_PATH%" -m pip install numpy --quiet
"%PYTHON_PATH%" -m pip install pywin32 --quiet
"%PYTHON_PATH%" -m pip install opencv-python --quiet

:: Run the script as administrator
powershell -Command "Start-Process '%PYTHON_PATH%' -ArgumentList 'game_macro_recorder.py' -Verb RunAs"

echo.
echo If a UAC prompt appears, please click "Yes" to allow the script to run with administrator privileges.
echo.
pause
