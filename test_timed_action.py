#!/usr/bin/env python3
"""
Test script for the Timed Action functionality.
"""

import sys
import time
import threading

def test_timed_action_module():
    """Test the timed action module functionality."""
    print("=" * 60)
    print("Timed Action Module Test")
    print("=" * 60)
    
    try:
        # Test imports
        print("1. Testing imports...")
        from timed_action import (
            TimedAction, add_timed_action, get_all_timed_actions,
            remove_timed_action, update_timed_action, save_timed_actions,
            load_timed_actions, get_timed_actions_status, start_timed_action,
            stop_timed_action, stop_all_timed_actions, parse_interval_string,
            validate_keys, get_supported_keys, export_timed_actions_config,
            import_timed_actions_config
        )
        print("   ✅ All imports successful")
        
        # Test TimedAction creation
        print("\n2. Testing TimedAction creation...")
        test_action = TimedAction(
            name="test_action",
            keys=["space", "enter"],
            interval_ms=2000,
            enabled=True,
            description="Test action for validation"
        )
        print(f"   ✅ TimedAction created: {test_action.name}")
        print(f"      Keys: {test_action.keys}")
        print(f"      Interval: {test_action.get_interval_display()}")
        print(f"      Enabled: {test_action.enabled}")
        
        # Test serialization
        print("\n3. Testing serialization...")
        action_dict = test_action.to_dict()
        restored_action = TimedAction.from_dict(action_dict)
        print(f"   ✅ Serialization successful: {len(action_dict)} fields")
        print(f"   ✅ Restoration successful: {restored_action.name}")
        
        # Test action management
        print("\n4. Testing action management...")
        add_timed_action(test_action)
        all_actions = get_all_timed_actions()
        print(f"   ✅ Action added. Total actions: {len(all_actions)}")
        
        # Test update
        success = update_timed_action("test_action", interval_ms=3000, enabled=False)
        updated_action = get_all_timed_actions()["test_action"]
        print(f"   ✅ Action updated: {success}")
        print(f"      New interval: {updated_action.get_interval_display()}")
        print(f"      New enabled state: {updated_action.enabled}")
        
        # Test status
        print("\n5. Testing status...")
        status = get_timed_actions_status()
        print(f"   ✅ Status retrieved:")
        print(f"      Total actions: {status['total_actions']}")
        print(f"      Running actions: {status['running_actions']}")
        print(f"      System enabled: {status['enabled']}")
        
        # Test interval parsing
        print("\n6. Testing interval parsing...")
        test_intervals = ["1000ms", "5s", "2m", "500"]
        for interval_str in test_intervals:
            parsed = parse_interval_string(interval_str)
            print(f"   ✅ '{interval_str}' -> {parsed}ms")
        
        # Test key validation
        print("\n7. Testing key validation...")
        test_keys = ["space", "enter", "invalid_key", "a", "f1"]
        valid_keys, invalid_keys = validate_keys(test_keys)
        print(f"   ✅ Valid keys: {valid_keys}")
        print(f"   ✅ Invalid keys: {invalid_keys}")
        
        # Test supported keys
        print("\n8. Testing supported keys...")
        supported = get_supported_keys()
        print(f"   ✅ Supported keys count: {len(supported)}")
        print(f"   ✅ Sample keys: {supported[:10]}...")
        
        # Test save/load
        print("\n9. Testing save/load...")
        save_success = save_timed_actions()
        print(f"   ✅ Save successful: {save_success}")
        
        # Clear actions and reload
        global _timed_actions
        from timed_action import _timed_actions
        original_count = len(_timed_actions)
        _timed_actions.clear()
        print(f"   ✅ Actions cleared (was {original_count})")
        
        load_success = load_timed_actions()
        reloaded_count = len(_timed_actions)
        print(f"   ✅ Load successful: {load_success} (count: {reloaded_count})")
        
        # Test export/import
        print("\n10. Testing export/import...")
        export_path = "test_timed_actions_export.json"
        export_success = export_timed_actions_config(export_path)
        print(f"   ✅ Export successful: {export_success}")
        
        # Clear and import
        _timed_actions.clear()
        import_success = import_timed_actions_config(export_path)
        imported_count = len(_timed_actions)
        print(f"   ✅ Import successful: {import_success} (count: {imported_count})")
        
        # Clean up export file
        import os
        try:
            os.remove(export_path)
            print(f"   ✅ Cleaned up export file")
        except:
            pass
        
        # Test action execution (without actually sending keys)
        print("\n11. Testing action execution simulation...")
        
        # Create a test action with very short interval for testing
        quick_action = TimedAction(
            name="quick_test",
            keys=["space"],
            interval_ms=500,  # 0.5 seconds
            enabled=True
        )
        add_timed_action(quick_action)
        
        # Note: We won't actually start the action to avoid sending keys during testing
        print("   ✅ Quick action created for execution testing")
        print("   ⚠️  Skipping actual execution to avoid sending keys during test")
        
        # Clean up
        print("\n12. Cleaning up...")
        remove_success = remove_timed_action("test_action")
        remove_success2 = remove_timed_action("quick_test")
        print(f"   ✅ Test actions removed: {remove_success and remove_success2}")
        
        print("\n" + "=" * 60)
        print("🎉 All Timed Action tests passed!")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_gui_integration():
    """Test GUI integration for timed actions."""
    print("\n" + "=" * 60)
    print("GUI Integration Test")
    print("=" * 60)
    
    try:
        # Test GUI imports
        print("1. Testing GUI imports...")
        from main import MacroRecorderGUI
        print("   ✅ Main GUI module imported successfully")
        
        # Check if timed action tab creation method exists
        if hasattr(MacroRecorderGUI, '_create_timed_action_tab'):
            print("   ✅ Timed action tab creation method found")
        else:
            print("   ❌ Timed action tab creation method not found")
            return False
        
        # Check if timed action management methods exist
        required_methods = [
            '_toggle_timed_actions_system',
            '_refresh_timed_actions_display',
            '_add_timed_action',
            '_edit_timed_action',
            '_delete_timed_action',
            '_start_timed_action',
            '_stop_timed_action',
            '_stop_all_timed_actions',
            '_show_timed_action_dialog',
            '_export_timed_actions_config',
            '_import_timed_actions_config'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(MacroRecorderGUI, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"   ❌ Missing methods: {missing_methods}")
            return False
        else:
            print("   ✅ All required GUI methods found")
        
        print("\n🎉 GUI Integration test passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ GUI test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("Starting Timed Action Tests...")
    
    # Test core module
    module_success = test_timed_action_module()
    
    # Test GUI integration
    gui_success = test_gui_integration()
    
    # Overall result
    if module_success and gui_success:
        print("\n🎉 ALL TESTS PASSED! Timed Action system is ready!")
    else:
        print("\n❌ Some tests failed. Please check the output above.")
    
    sys.exit(0 if (module_success and gui_success) else 1)
