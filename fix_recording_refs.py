"""
Script to fix all recorded_actions references in recording_manager.py
"""

import re

def fix_recording_manager_refs():
    """Fix all recorded_actions references to use self.recorded_actions."""
    
    # Read the file
    with open('recording_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace all standalone recorded_actions with self.recorded_actions
    # Use word boundaries to avoid replacing parts of other words
    # Don't replace if already preceded by 'self.'
    pattern = r'(?<!self\.)(?<!\.)\brecorded_actions\b'
    content = re.sub(pattern, 'self.recorded_actions', content)
    
    # Also fix is_recording and is_paused references that aren't using self.
    pattern = r'(?<!self\.)(?<!\.)\bis_recording\b'
    content = re.sub(pattern, 'self.is_recording', content)
    
    pattern = r'(?<!self\.)(?<!\.)\bis_paused\b'
    content = re.sub(pattern, 'self.is_paused', content)
    
    # Write back
    with open('recording_manager.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Fixed all recording manager references")

if __name__ == "__main__":
    fix_recording_manager_refs()
