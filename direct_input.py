"""
Direct Input Module - For reliable input simulation in games
Uses the Windows SendInput API for more reliable input in DirectX/OpenGL games
"""
import ctypes
import time
import logging
from ctypes import wintypes
from typing import Union, Optional

# Configure logging
logger = logging.getLogger("direct_input")

# Constants for key events
KEYEVENTF_EXTENDEDKEY = 0x0001
KEYEVENTF_KEYUP = 0x0002
KEYEVENTF_SCANCODE = 0x0008
KEYEVENTF_UNICODE = 0x0004

# Constants for mouse events
MOUSEEVENTF_MOVE = 0x0001
MOUSEEVENTF_LEFTDOWN = 0x0002
MOUSEEVENTF_LEFTUP = 0x0004
MOUSEEVENTF_RIGHTDOWN = 0x0008
MOUSEEVENTF_RIGHTUP = 0x0010
MOUSEEVENTF_MIDDLEDOWN = 0x0020
MOUSEEVENTF_MIDDLEUP = 0x0040
MOUSEEVENTF_ABSOLUTE = 0x8000

# Virtual key codes
VK_CODES = {
    'backspace': 0x08,
    'tab': 0x09,
    'clear': 0x0C,
    'enter': 0x0D,
    'shift': 0x10,
    'ctrl': 0x11,
    'alt': 0x12,
    'pause': 0x13,
    'caps_lock': 0x14,
    'esc': 0x1B,
    'spacebar': 0x20,
    'page_up': 0x21,
    'page_down': 0x22,
    'end': 0x23,
    'home': 0x24,
    'left_arrow': 0x25,
    'up_arrow': 0x26,
    'right_arrow': 0x27,
    'down_arrow': 0x28,
    'select': 0x29,
    'print': 0x2A,
    'execute': 0x2B,
    'print_screen': 0x2C,
    'ins': 0x2D,
    'del': 0x2E,
    'help': 0x2F,
    '0': 0x30,
    '1': 0x31,
    '2': 0x32,
    '3': 0x33,
    '4': 0x34,
    '5': 0x35,
    '6': 0x36,
    '7': 0x37,
    '8': 0x38,
    '9': 0x39,
    'a': 0x41,
    'b': 0x42,
    'c': 0x43,
    'd': 0x44,
    'e': 0x45,
    'f': 0x46,
    'g': 0x47,
    'h': 0x48,
    'i': 0x49,
    'j': 0x4A,
    'k': 0x4B,
    'l': 0x4C,
    'm': 0x4D,
    'n': 0x4E,
    'o': 0x4F,
    'p': 0x50,
    'q': 0x51,
    'r': 0x52,
    's': 0x53,
    't': 0x54,
    'u': 0x55,
    'v': 0x56,
    'w': 0x57,
    'x': 0x58,
    'y': 0x59,
    'z': 0x5A,
    'numpad_0': 0x60,
    'numpad_1': 0x61,
    'numpad_2': 0x62,
    'numpad_3': 0x63,
    'numpad_4': 0x64,
    'numpad_5': 0x65,
    'numpad_6': 0x66,
    'numpad_7': 0x67,
    'numpad_8': 0x68,
    'numpad_9': 0x69,
    'multiply_key': 0x6A,
    'add_key': 0x6B,
    'separator_key': 0x6C,
    'subtract_key': 0x6D,
    'decimal_key': 0x6E,
    'divide_key': 0x6F,
    'f1': 0x70,
    'f2': 0x71,
    'f3': 0x72,
    'f4': 0x73,
    'f5': 0x74,
    'f6': 0x75,
    'f7': 0x76,
    'f8': 0x77,
    'f9': 0x78,
    'f10': 0x79,
    'f11': 0x7A,
    'f12': 0x7B,
    'f13': 0x7C,
    'f14': 0x7D,
    'f15': 0x7E,
    'f16': 0x7F,
    'f17': 0x80,
    'f18': 0x81,
    'f19': 0x82,
    'f20': 0x83,
    'f21': 0x84,
    'f22': 0x85,
    'f23': 0x86,
    'f24': 0x87,
    'num_lock': 0x90,
    'scroll_lock': 0x91,
    'left_shift': 0xA0,
    'right_shift': 0xA1,
    'left_control': 0xA2,
    'right_control': 0xA3,
    'left_menu': 0xA4,
    'right_menu': 0xA5,
}

# C structures for SendInput
class MOUSEINPUT(ctypes.Structure):
    _fields_ = (("dx", wintypes.LONG),
                ("dy", wintypes.LONG),
                ("mouseData", wintypes.DWORD),
                ("dwFlags", wintypes.DWORD),
                ("time", wintypes.DWORD),
                ("dwExtraInfo", ctypes.POINTER(wintypes.ULONG)))

class KEYBDINPUT(ctypes.Structure):
    _fields_ = (("wVk", wintypes.WORD),
                ("wScan", wintypes.WORD),
                ("dwFlags", wintypes.DWORD),
                ("time", wintypes.DWORD),
                ("dwExtraInfo", ctypes.POINTER(wintypes.ULONG)))

class HARDWAREINPUT(ctypes.Structure):
    _fields_ = (("uMsg", wintypes.DWORD),
                ("wParamL", wintypes.WORD),
                ("wParamH", wintypes.WORD))

class INPUT_UNION(ctypes.Union):
    _fields_ = (("mi", MOUSEINPUT),
                ("ki", KEYBDINPUT),
                ("hi", HARDWAREINPUT))

class INPUT(ctypes.Structure):
    _fields_ = (("type", wintypes.DWORD),
                ("union", INPUT_UNION))

# Load user32.dll
user32 = ctypes.WinDLL('user32', use_last_error=True)
user32.SendInput.argtypes = (wintypes.UINT, ctypes.POINTER(INPUT), wintypes.INT)
user32.SendInput.restype = wintypes.UINT

# Helper functions
def get_vk_code(key: Union[str, int]) -> int:
    """Get the virtual key code for a key."""
    if isinstance(key, int):
        return key

    key_lower = key.lower()
    if key_lower in VK_CODES:
        return VK_CODES[key_lower]

    # For single character keys, use ASCII code
    if len(key) == 1:
        return ord(key.upper())

    # Log warning for unknown keys
    logger.warning(f"Unknown key: {key}, using default value")
    return 0

def key_down(key: Union[str, int]) -> bool:
    """Press a key down."""
    try:
        vk_code = get_vk_code(key)

        extra = ctypes.pointer(wintypes.ULONG(0))
        input_struct = INPUT(
            type=1,  # INPUT_KEYBOARD
            union=INPUT_UNION(
                ki=KEYBDINPUT(
                    wVk=vk_code,
                    wScan=0,
                    dwFlags=0,
                    time=0,
                    dwExtraInfo=extra
                )
            )
        )

        result = user32.SendInput(1, ctypes.byref(input_struct), ctypes.sizeof(input_struct))
        if result != 1:
            error = ctypes.get_last_error()
            logger.error(f"SendInput failed for key_down({key}): error code {error}")
            return False
        return True
    except Exception as e:
        logger.error(f"Error in key_down({key}): {e}")
        return False

def key_up(key: Union[str, int]) -> bool:
    """Release a key."""
    try:
        vk_code = get_vk_code(key)

        extra = ctypes.pointer(wintypes.ULONG(0))
        input_struct = INPUT(
            type=1,  # INPUT_KEYBOARD
            union=INPUT_UNION(
                ki=KEYBDINPUT(
                    wVk=vk_code,
                    wScan=0,
                    dwFlags=KEYEVENTF_KEYUP,
                    time=0,
                    dwExtraInfo=extra
                )
            )
        )

        result = user32.SendInput(1, ctypes.byref(input_struct), ctypes.sizeof(input_struct))
        if result != 1:
            error = ctypes.get_last_error()
            logger.error(f"SendInput failed for key_up({key}): error code {error}")
            return False
        return True
    except Exception as e:
        logger.error(f"Error in key_up({key}): {e}")
        return False

def press_key(key: Union[str, int], duration: float = 0.1) -> bool:
    """Press a key for a specified duration."""
    try:
        if key_down(key):
            time.sleep(duration)
            return key_up(key)
        return False
    except Exception as e:
        logger.error(f"Error in press_key({key}): {e}")
        return False

def press_keys(keys: list, duration: float = 0.1) -> bool:
    """Press multiple keys simultaneously."""
    try:
        # Press all keys down
        all_down_success = True
        pressed_keys = []

        for key in keys:
            if key_down(key):
                pressed_keys.append(key)
            else:
                all_down_success = False
                logger.warning(f"Failed to press key: {key}")

        # Wait for the specified duration
        time.sleep(duration)

        # Release all keys that were successfully pressed (in reverse order)
        all_up_success = True
        for key in reversed(pressed_keys):
            if not key_up(key):
                all_up_success = False
                logger.warning(f"Failed to release key: {key}")

        return all_down_success and all_up_success
    except Exception as e:
        logger.error(f"Error in press_keys({keys}): {e}")
        # Try to release any keys that might be down
        for key in keys:
            try:
                key_up(key)
            except:
                pass
        return False

def mouse_move(x: int, y: int, absolute: bool = False) -> bool:
    """Move the mouse to the specified coordinates."""
    try:
        flags = MOUSEEVENTF_MOVE
        if absolute:
            flags |= MOUSEEVENTF_ABSOLUTE

        extra = ctypes.pointer(wintypes.ULONG(0))
        input_struct = INPUT(
            type=0,  # INPUT_MOUSE
            union=INPUT_UNION(
                mi=MOUSEINPUT(
                    dx=x,
                    dy=y,
                    mouseData=0,
                    dwFlags=flags,
                    time=0,
                    dwExtraInfo=extra
                )
            )
        )

        result = user32.SendInput(1, ctypes.byref(input_struct), ctypes.sizeof(input_struct))
        if result != 1:
            error = ctypes.get_last_error()
            logger.error(f"SendInput failed for mouse_move: error code {error}")
            return False
        return True
    except Exception as e:
        logger.error(f"Error in mouse_move: {e}")
        return False

def mouse_click(button: str = 'left', x: Optional[int] = None, y: Optional[int] = None) -> bool:
    """Perform a mouse click at the specified coordinates."""
    try:
        if x is not None and y is not None:
            # Move to the specified coordinates first
            if not mouse_move_absolute(x, y):
                logger.warning(f"Failed to move mouse to ({x}, {y}) before clicking")

        # Determine button flags
        if button.lower() == 'left':
            down_flag = MOUSEEVENTF_LEFTDOWN
            up_flag = MOUSEEVENTF_LEFTUP
        elif button.lower() == 'right':
            down_flag = MOUSEEVENTF_RIGHTDOWN
            up_flag = MOUSEEVENTF_RIGHTUP
        elif button.lower() == 'middle':
            down_flag = MOUSEEVENTF_MIDDLEDOWN
            up_flag = MOUSEEVENTF_MIDDLEUP
        else:
            logger.error(f"Unsupported button: {button}")
            return False

        # Mouse down
        extra = ctypes.pointer(wintypes.ULONG(0))
        input_down = INPUT(
            type=0,  # INPUT_MOUSE
            union=INPUT_UNION(
                mi=MOUSEINPUT(
                    dx=0,
                    dy=0,
                    mouseData=0,
                    dwFlags=down_flag,
                    time=0,
                    dwExtraInfo=extra
                )
            )
        )

        # Mouse up
        input_up = INPUT(
            type=0,  # INPUT_MOUSE
            union=INPUT_UNION(
                mi=MOUSEINPUT(
                    dx=0,
                    dy=0,
                    mouseData=0,
                    dwFlags=up_flag,
                    time=0,
                    dwExtraInfo=extra
                )
            )
        )

        # Send inputs
        down_result = user32.SendInput(1, ctypes.byref(input_down), ctypes.sizeof(input_down))
        if down_result != 1:
            error = ctypes.get_last_error()
            logger.error(f"SendInput failed for mouse down: error code {error}")
            return False

        time.sleep(0.05)  # Small delay between down and up

        up_result = user32.SendInput(1, ctypes.byref(input_up), ctypes.sizeof(input_up))
        if up_result != 1:
            error = ctypes.get_last_error()
            logger.error(f"SendInput failed for mouse up: error code {error}")
            return False

        return True
    except Exception as e:
        logger.error(f"Error in mouse_click: {e}")
        return False

def mouse_move_absolute(x: int, y: int) -> bool:
    """Move the mouse to absolute screen coordinates."""
    try:
        # Get screen dimensions
        screen_width = user32.GetSystemMetrics(0)
        screen_height = user32.GetSystemMetrics(1)

        if screen_width <= 0 or screen_height <= 0:
            logger.error(f"Invalid screen dimensions: {screen_width}x{screen_height}")
            return False

        # Validate coordinates
        if x < 0 or x >= screen_width or y < 0 or y >= screen_height:
            logger.warning(f"Coordinates ({x}, {y}) are outside screen bounds ({screen_width}x{screen_height})")

        # Convert to normalized coordinates (0-65535)
        normalized_x = int(x * 65535 / screen_width)
        normalized_y = int(y * 65535 / screen_height)

        return mouse_move(normalized_x, normalized_y, absolute=True)
    except Exception as e:
        logger.error(f"Error in mouse_move_absolute: {e}")
        return False
