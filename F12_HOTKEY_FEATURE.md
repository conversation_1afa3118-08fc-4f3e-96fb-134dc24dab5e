# F12 Hotkey for Death Detection - Feature Documentation

## Overview

The F12 hotkey has been added to provide quick and convenient toggle control for death detection functionality. This allows users to instantly enable or disable automatic revival detection during gameplay without needing to access the GUI.

## Feature Details

### 🎯 **Quick Toggle**
- **F12 Key**: Instantly toggles death detection on/off
- **Global hotkey**: Works from anywhere in the application
- **Visual feedback**: Status updates in both full and minimalist modes
- **Audio-free**: Silent operation for gaming environments

### 🎮 **Gaming Integration**
- **In-game access**: Toggle death detection without alt-tabbing
- **Emergency disable**: Quickly turn off if needed during critical moments
- **Emergency enable**: Instantly activate when entering dangerous areas
- **No interference**: Doesn't conflict with most game controls

### 📱 **Multi-Mode Support**
- **Full mode**: Works in the complete interface
- **Minimalist mode**: Functional in compact overlay
- **Overlay mode**: Available in transparent overlay
- **Consistent behavior**: Same functionality across all modes

## Usage

### Basic Operation

**Enable Death Detection:**
```
Press F12 when death detection is OFF
→ Death detection starts automatically
→ Status shows "Death detection enabled (F12)"
→ Minimalist mode shows "Death detection ON"
```

**Disable Death Detection:**
```
Press F12 when death detection is ON
→ Death detection stops immediately
→ Status shows "Death detection disabled (F12)"
→ Minimalist mode shows "Death detection OFF"
```

### Visual Feedback

#### Full Mode Interface
- **Status bar**: Shows "Death detection enabled/disabled (F12)"
- **Death detection tab**: Checkbox updates automatically
- **Button colors**: Death detection button changes color

#### Minimalist Mode
- **Status text**: Updates to show current state
- **Button color**: 
  - Blue = OFF
  - Green = ON
- **Button text**: Shows "💀 Death ON/OFF"

#### Console Logging
```
INFO - Death detection started via hotkey
INFO - Death detection stopped via hotkey
```

## Technical Implementation

### Hotkey Registration
```python
# In main.py _setup_hooks method
keyboard.add_hotkey('f12', self._toggle_death_detection)
```

### State Management
```python
def _toggle_death_detection(self):
    """Toggle death detection via F12 hotkey."""
    current_state = is_death_detection_active()
    
    if current_state:
        # Disable death detection
        stop_death_detection()
        self.death_enabled_var.set(False)
        self.status_var.set("Death detection disabled (F12)")
    else:
        # Enable death detection
        start_death_detection()
        self.death_enabled_var.set(True)
        self.status_var.set("Death detection enabled (F12)")
```

### Cross-Mode Synchronization
- **UI updates**: All interface elements update automatically
- **Settings sync**: Changes reflect in all modes
- **State consistency**: Same behavior regardless of current mode

## Integration with Existing Features

### Hotkey Compatibility
- **F8**: Start/stop recording
- **F9**: Start/stop playback
- **F10**: Toggle minimalist mode
- **F12**: Toggle death detection

### Recording Exclusion
```python
# F12 is excluded from recording like other hotkeys
if event.name in ['f8', 'f9', 'f10', 'f11', 'f12']:
    return  # Don't record hotkey presses
```

### Settings Preservation
- **Current settings**: Uses existing death detection configuration
- **Auto-click**: Maintains auto-click revival setting
- **Confidence**: Preserves confidence threshold
- **Check interval**: Keeps current check interval

## Use Cases

### 🎮 **Gaming Scenarios**

#### Dangerous Area Entry
```
Situation: Entering a high-risk dungeon
Action: Press F12 to enable death detection
Result: Automatic revival protection activated
```

#### Safe Area/AFK
```
Situation: Moving to safe area or going AFK
Action: Press F12 to disable death detection
Result: Saves system resources, prevents false positives
```

#### Boss Fights
```
Situation: Critical boss encounter
Action: Press F12 to disable (avoid interruption)
Result: Full manual control during important fights
```

#### Grinding/Farming
```
Situation: Long farming sessions
Action: Press F12 to enable death detection
Result: Automated revival during repetitive tasks
```

### 🔧 **Technical Scenarios**

#### Template Issues
```
Situation: Revival button template not working
Action: Press F12 to disable, fix template, F12 to re-enable
Result: Quick troubleshooting without GUI access
```

#### Performance Optimization
```
Situation: System running slowly
Action: Press F12 to disable death detection temporarily
Result: Reduced CPU usage for better game performance
```

#### Testing Macros
```
Situation: Testing new macro sequences
Action: Press F12 to disable death detection during tests
Result: Prevents interference with macro testing
```

## Error Handling

### Robust Operation
- **Graceful failures**: Continues working even if some components fail
- **Fallback settings**: Uses defaults if UI settings unavailable
- **Error logging**: Records issues for troubleshooting
- **State recovery**: Maintains consistent state even after errors

### Common Scenarios
```python
# UI not available (minimalist mode)
try:
    auto_click = self.auto_click_var.get()
    confidence = self.confidence_var.get() / 100.0
    check_interval = self.check_interval_var.get()
except:
    # Use sensible defaults
    auto_click = True
    confidence = 0.85
    check_interval = 2.0
```

## Performance Impact

### Minimal Overhead
- **Hotkey registration**: One-time setup cost
- **Toggle operation**: Instant response
- **No polling**: Event-driven activation
- **Memory usage**: Negligible additional memory

### Gaming Performance
- **No FPS impact**: Hotkey doesn't affect game performance
- **Low latency**: Immediate response to key press
- **Background operation**: Doesn't interfere with game input
- **Resource efficient**: Minimal CPU usage

## Troubleshooting

### Common Issues

#### F12 Not Responding
**Symptoms**: Pressing F12 has no effect
**Solutions**:
1. Check if application has focus
2. Verify keyboard module is working
3. Look for conflicting software using F12
4. Restart application

#### Death Detection Not Toggling
**Symptoms**: F12 pressed but death detection state unchanged
**Solutions**:
1. Check application logs for errors
2. Verify death detection module is loaded
3. Ensure administrator privileges
4. Check template file exists

#### UI Not Updating
**Symptoms**: F12 works but interface doesn't show changes
**Solutions**:
1. Switch between full/minimalist mode to refresh
2. Check status bar for confirmation
3. Verify UI synchronization is working

### Debug Information
```python
# Check hotkey registration
logger.info("F12 hotkey registered successfully")

# Verify toggle operation
logger.info("Death detection started via hotkey")
logger.info("Death detection stopped via hotkey")

# Monitor state changes
current_state = is_death_detection_active()
logger.debug(f"Death detection state: {current_state}")
```

## Best Practices

### For Gaming
1. **Learn the hotkey**: Practice using F12 until it's muscle memory
2. **Strategic use**: Enable in dangerous areas, disable in safe zones
3. **Emergency access**: Use F12 for quick disable during critical moments
4. **Resource management**: Disable when not needed to save CPU

### For Development
1. **Testing**: Use F12 for quick enable/disable during testing
2. **Debugging**: Toggle to isolate death detection issues
3. **Performance**: Monitor impact and disable when optimizing
4. **Integration**: Ensure F12 works with custom modifications

### For Efficiency
1. **Hotkey combinations**: Combine with other hotkeys for workflows
2. **Mode switching**: Use with F10 for optimal interface management
3. **Status monitoring**: Watch for visual feedback to confirm changes
4. **Settings optimization**: Configure death detection settings before using F12

## Future Enhancements

### Planned Improvements
- **Visual notifications**: Toast-style popups for F12 activation
- **Sound feedback**: Optional audio confirmation (toggleable)
- **Custom hotkeys**: Allow users to change F12 to different key
- **Macro integration**: Include F12 state in macro recordings

### Advanced Features
- **Conditional toggling**: Smart enable/disable based on game state
- **Profile switching**: F12 cycles through different death detection profiles
- **Statistics tracking**: Monitor F12 usage patterns
- **Remote control**: API access for external tools

The F12 hotkey provides essential quick access to death detection control, making the macro recorder more convenient and efficient for active gaming scenarios.
