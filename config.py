"""
Configuration Module - Logic for reading/writing configuration files
"""
import json
import os
from imports import logger, messagebox

# Game window settings
game_window_settings = {
    'window_title': 'Rappelz',  # The window title to look for
    'target_game_only': True,   # Whether to target only the game window
    'stay_on_top': False,       # Whether the app should stay on top of other windows
    'prevent_focus_steal': False, # Prevent the app from stealing focus from the game
    'use_overlay_mode': False    # Use a transparent overlay window that doesn't steal focus
}

# Death detection settings have been moved to death_detection.py module

def save_actions(filename, recorded_actions):
    """Save recorded actions and settings to a file."""
    try:
        # Import death detection settings
        from death_detection import get_death_detection_settings
        death_settings = get_death_detection_settings()

        # Create a data structure with both actions and settings
        data = {
            'actions': recorded_actions,
            'death_detection': {
                'enabled': death_settings['enabled'],
                'check_interval': death_settings['check_interval'],
                'click_position': death_settings['click_position'],
                'color': death_settings['color'],
                'threshold': death_settings['threshold'],
                'region': death_settings['region']
            },
            'game_window': {
                'target_game_only': game_window_settings['target_game_only'],
                'window_title': game_window_settings['window_title'],
                'stay_on_top': game_window_settings['stay_on_top'],
                'prevent_focus_steal': game_window_settings['prevent_focus_steal'],
                'use_overlay_mode': game_window_settings['use_overlay_mode']
            }
        }

        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        messagebox.showerror("Error", f"Failed to save actions: {e}")
        return False

def load_actions(filename):
    """Load recorded actions and settings from a file."""
    global game_window_settings

    try:
        with open(filename, 'r') as f:
            data = json.load(f)

        recorded_actions = []

        # Check if this is a new format file with both actions and settings
        if isinstance(data, dict) and 'actions' in data:
            recorded_actions = data['actions']

            # Load death detection settings if available
            if 'death_detection' in data:
                from death_detection import update_death_detection_settings
                dd = data['death_detection']
                update_death_detection_settings(
                    enabled=dd.get('enabled', False),
                    check_interval=dd.get('check_interval', 2.0),
                    click_position=dd.get('click_position', None),
                    color=dd.get('color', (41, 76, 122)),
                    threshold=dd.get('threshold', 500),
                    region=dd.get('region', None)
                )

            # Load game window settings if available
            if 'game_window' in data:
                gw = data['game_window']
                game_window_settings['target_game_only'] = gw.get('target_game_only', True)
                game_window_settings['window_title'] = gw.get('window_title', 'Rappelz')
                game_window_settings['stay_on_top'] = gw.get('stay_on_top', False)
                game_window_settings['prevent_focus_steal'] = gw.get('prevent_focus_steal', True)
                game_window_settings['use_overlay_mode'] = gw.get('use_overlay_mode', True)
        else:
            # Old format file with just actions
            recorded_actions = data

        return recorded_actions
    except Exception as e:
        messagebox.showerror("Error", f"Failed to load actions: {e}")
        return None

def save_last_macro_path(path):
    """Save the path of the last loaded macro to a settings file."""
    settings_file = "app_settings.json"
    settings = {}
    
    # Load existing settings if they exist
    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r') as f:
                settings = json.load(f)
        except:
            pass
            
    # Update with the last macro path
    settings['last_macro_path'] = path
    
    # Save the settings
    try:
        with open(settings_file, 'w') as f:
            json.dump(settings, f, indent=4)
        logger.info(f"Saved last macro path: {path}")
    except Exception as e:
        logger.error(f"Error saving last macro path: {e}")

def load_last_macro():
    """Load the last used macro if available."""
    settings_file = "app_settings.json"
    
    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r') as f:
                settings = json.load(f)
            
            if 'last_macro_path' in settings and os.path.exists(settings['last_macro_path']):
                last_path = settings['last_macro_path']
                recorded_actions = load_actions(last_path)
                if recorded_actions is not None:
                    logger.info(f"Automatically loaded last macro: {last_path}")
                    return recorded_actions, last_path
        except Exception as e:
            logger.error(f"Error loading last macro: {e}")
    
    return None, None

def save_minimalist_settings(minimalist_settings):
    """Save minimalist window settings to a file."""
    try:
        # Save settings to a file
        settings_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "minimalist_settings.json")
        with open(settings_file, 'w') as f:
            json.dump(minimalist_settings, f)

        logger.info(f"Saved minimalist settings: {minimalist_settings}")
    except Exception as e:
        logger.error(f"Error saving minimalist settings: {e}")

def load_minimalist_settings():
    """Load minimalist window settings from a file."""
    default_settings = {
        'x': 10,
        'y': 10,
        'transparency': 0.9
    }
    
    try:
        settings_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "minimalist_settings.json")
        if os.path.exists(settings_file):
            with open(settings_file, 'r') as f:
                loaded_settings = json.load(f)

                # Update settings with loaded values
                for key, value in loaded_settings.items():
                    if key in default_settings:
                        default_settings[key] = value

            logger.info(f"Loaded minimalist settings: {default_settings}")
    except Exception as e:
        logger.error(f"Error loading minimalist settings: {e}")
        # Keep default settings on error
    
    return default_settings

def save_death_detection_settings():
    """Save death detection settings to a file."""
    settings_file = "death_detection_settings.json"
    try:
        from death_detection import get_death_detection_settings
        death_settings = get_death_detection_settings()

        # Create a copy of settings to save (excluding objects that can't be serialized)
        settings_to_save = {}
        for key, value in death_settings.items():
            if isinstance(value, (bool, int, float, str, list, dict)) or value is None:
                settings_to_save[key] = value

        with open(settings_file, "w") as f:
            json.dump(settings_to_save, f, indent=4)
            logger.info("Saved death detection settings")
    except Exception as e:
        logger.error(f"Error saving death detection settings: {e}")
