"""
Recording Management Module

Handles all recording-related functionality including:
- Recording start/stop/pause
- Action list management
- Action editing and manipulation
- Import/export operations
- Recording state management
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
import time
from datetime import datetime

# Import shared components
from recorder import get_recorded_actions
from ui_helpers import (
    create_styled_button, show_info_message, show_warning_message,
    format_action_display, update_listbox_colors
)
from config import save_actions, load_actions

logger = logging.getLogger(__name__)

class RecordingManager:
    """Manages all recording-related operations and UI."""

    def __init__(self, main_gui):
        """Initialize the recording manager."""
        self.main_gui = main_gui
        self.root = main_gui.root
        self.colors = main_gui.colors
        # status_var will be set later when UI is ready
        self.status_var = None

        # Recording state
        self.recording_start_time = None
        self.last_action_time = None

        # UI components (will be set by main GUI)
        self.actions_listbox = None
        self.action_details_text = None
        self.record_button = None
        self.pause_button = None
        self.stop_button = None

        # State management - will be set by main GUI
        self._recording = False
        self._paused = False

        logger.info("Recording manager initialized")

    def _update_status(self, message):
        """Safely update the status message."""
        if self.status_var:
            self.status_var.set(message)

    @property
    def recorded_actions(self):
        """Get the current recorded actions list."""
        try:
            return get_recorded_actions()
        except:
            return []

    @property
    def is_recording(self):
        """Check if recording is active."""
        try:
            # Access main GUI's recording state
            return getattr(self.main_gui, 'recording', False)
        except:
            return self._recording

    @property
    def is_paused(self):
        """Check if recording is paused."""
        try:
            # Access main GUI's paused state
            return getattr(self.main_gui, 'paused', False)
        except:
            return self._paused

    def set_recording_state(self, recording, paused=False):
        """Set the recording state from main GUI."""
        self._recording = recording
        self._paused = paused

    def set_ui_components(self, actions_listbox, action_details_text,
                         record_button, pause_button, stop_button):
        """Set references to UI components."""
        self.actions_listbox = actions_listbox
        self.action_details_text = action_details_text
        self.record_button = record_button
        self.pause_button = pause_button
        self.stop_button = stop_button

        # Set status_var now that UI is ready
        if hasattr(self.main_gui, 'status_var'):
            self.status_var = self.main_gui.status_var

        # Set up event bindings
        if self.actions_listbox:
            self.actions_listbox.bind('<<ListboxSelect>>', self._on_action_select)
            self.actions_listbox.bind('<Double-Button-1>', self._edit_selected_action)
    
    def start_recording(self):
        """Start recording actions."""
        try:
            if not self.is_recording:
                # Set up recording state
                self.recording_start_time = time.time()
                self.last_action_time = self.recording_start_time

                # Update state
                self.set_recording_state(True, False)

                # Update UI
                self._update_recording_buttons()
                self._update_status("Recording started - perform actions to record")

                # Start monitoring for new actions
                self._monitor_recording()

                logger.info("Recording started successfully")
                return True
            else:
                logger.warning("Recording already active")
                return False

        except Exception as e:
            logger.error(f"Error starting recording: {e}")
            show_warning_message("Recording Error", f"Failed to start recording: {e}")
            return False
    
    def pause_recording(self):
        """Pause/resume recording."""
        try:
            from recorder import pause_recording, resume_recording
            
            if self.is_paused:
                # Resume recording
                if resume_recording():
                    self._update_recording_buttons()
                    self._update_status("Recording resumed")
                    logger.info("Recording resumed")
                    return True
            else:
                # Pause recording
                if pause_recording():
                    self._update_recording_buttons()
                    self._update_status("Recording paused")
                    logger.info("Recording paused")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error pausing/resuming recording: {e}")
            show_warning_message("Recording Error", f"Failed to pause/resume recording: {e}")
            return False
    
    def stop_recording(self):
        """Stop recording actions."""
        try:
            # Calculate recording duration
            if self.recording_start_time:
                duration = time.time() - self.recording_start_time
                duration_str = f"{duration:.1f} seconds"
            else:
                duration_str = "unknown duration"

            # Update state
            self.set_recording_state(False, False)

            # Update UI
            self._update_recording_buttons()
            self._update_actions_list()

            action_count = len(self.recorded_actions)
            self._update_status(f"Recording stopped - {action_count} actions recorded in {duration_str}")

            logger.info(f"Recording stopped - {action_count} actions in {duration_str}")
            return True

        except Exception as e:
            logger.error(f"Error stopping recording: {e}")
            show_warning_message("Recording Error", f"Failed to stop recording: {e}")
            return False
    
    def _monitor_recording(self):
        """Monitor recording state and update UI."""
        try:
            if self.is_recording:
                # Update actions list if new actions were recorded
                current_count = len(self.recorded_actions)
                if hasattr(self, '_last_action_count'):
                    if current_count != self._last_action_count:
                        self._update_actions_list()
                else:
                    self._update_actions_list()

                self._last_action_count = current_count

                # Update recording time
                if self.recording_start_time and not self.is_paused:
                    elapsed = time.time() - self.recording_start_time
                    self._update_status(f"Recording... {elapsed:.1f}s - {current_count} actions")

                # Schedule next update
                self.root.after(100, self._monitor_recording)
            
        except Exception as e:
            logger.error(f"Error monitoring recording: {e}")
    
    def _update_recording_buttons(self):
        """Update recording button states."""
        try:
            if self.record_button and self.pause_button and self.stop_button:
                if self.is_recording:
                    # Recording active
                    self.record_button.config(state='disabled')
                    self.stop_button.config(state='normal')

                    if self.is_paused:
                        self.pause_button.config(text="▶ Resume", state='normal')
                    else:
                        self.pause_button.config(text="⏸ Pause", state='normal')
                else:
                    # Recording inactive
                    self.record_button.config(state='normal')
                    self.pause_button.config(text="⏸ Pause", state='disabled')
                    self.stop_button.config(state='disabled')
                    
        except Exception as e:
            logger.error(f"Error updating recording buttons: {e}")
    
    def _update_actions_list(self):
        """Update the actions list display."""
        try:
            if not self.actions_listbox:
                return
            
            # Clear current list
            self.actions_listbox.delete(0, tk.END)
            
            # Add all actions
            for i, action in enumerate(self.recorded_actions):
                action_str, action_type = format_action_display(action, i + 1)
                self.actions_listbox.insert(tk.END, action_str)
            
            # Update colors
            update_listbox_colors(self.actions_listbox, self.recorded_actions, self.colors)
            
            # Auto-scroll to bottom
            if self.recorded_actions:
                self.actions_listbox.see(tk.END)
                
        except Exception as e:
            logger.error(f"Error updating actions list: {e}")
    
    def _on_action_select(self, event):
        """Handle action selection to show details."""
        try:
            if not self.actions_listbox or not self.action_details_text:
                return
            
            selected = self.actions_listbox.curselection()
            if selected and self.recorded_actions:
                index = selected[0]
                if 0 <= index < len(self.recorded_actions):
                    action = self.recorded_actions[index]
                    details = self._format_action_details(action, index + 1)
                    
                    # Update details display
                    if hasattr(self.action_details_text, 'delete'):
                        # It's a Text widget
                        self.action_details_text.delete(1.0, tk.END)
                        self.action_details_text.insert(1.0, details)
                    elif hasattr(self.action_details_text, 'set'):
                        # It's a StringVar
                        self.action_details_text.set(details)
                    
        except Exception as e:
            logger.error(f"Error handling action selection: {e}")
    
    def _format_action_details(self, action, index):
        """Format detailed action information."""
        try:
            details = f"Action {index} Details:\n"
            details += "=" * 30 + "\n\n"
            
            # Basic info
            action_type = action.get('type', 'unknown')
            timestamp = action.get('timestamp', 0)
            
            details += f"Type: {action_type.title()}\n"
            details += f"Timestamp: {timestamp:.3f}s\n"
            
            # Type-specific details
            if action_type == 'key':
                key = action.get('key', 'unknown')
                details += f"Key: {key}\n"
                
            elif action_type in ['click', 'mouse']:
                button = action.get('button', 'unknown')
                x = action.get('x', 0)
                y = action.get('y', 0)
                details += f"Button: {button}\n"
                details += f"Position: ({x}, {y})\n"
                
            elif action_type == 'delay':
                duration = action.get('duration', 0)
                details += f"Duration: {duration:.3f}s\n"
            
            # Raw data
            details += f"\nRaw Data:\n{action}"
            
            return details
            
        except Exception as e:
            logger.error(f"Error formatting action details: {e}")
            return f"Error displaying action details: {e}"
    
    def clear_all_actions(self):
        """Clear all recorded actions."""
        try:
            if not self.recorded_actions:
                show_info_message("Info", "No actions to clear")
                return
            
            # Confirm with user
            result = messagebox.askyesno(
                "Clear All Actions",
                f"Are you sure you want to clear all {len(self.recorded_actions)} recorded actions?\n\n"
                "This action cannot be undone."
            )
            
            if result:
                self.recorded_actions.clear()
                self._update_actions_list()
                
                # Clear details display
                if self.action_details_text:
                    self.action_details_text.delete(1.0, tk.END)
                
                self._update_status("All actions cleared")
                logger.info("All recorded actions cleared")
                
        except Exception as e:
            logger.error(f"Error clearing actions: {e}")
            show_warning_message("Clear Error", f"Failed to clear actions: {e}")
    
    def get_recording_stats(self):
        """Get current recording statistics."""
        try:
            stats = {
                'total_actions': len(self.recorded_actions),
                'is_recording': self.is_recording,
                'is_paused': self.is_paused,
                'recording_time': 0
            }
            
            if self.recording_start_time and self.is_recording:
                stats['recording_time'] = time.time() - self.recording_start_time
            
            # Count action types
            action_types = {}
            for action in self.recorded_actions:
                action_type = action.get('type', 'unknown')
                action_types[action_type] = action_types.get(action_type, 0) + 1
            
            stats['action_types'] = action_types
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting recording stats: {e}")
            return {'total_actions': 0, 'is_recording': False, 'is_paused': False}
    
    def refresh_display(self):
        """Refresh all recording displays."""
        try:
            self._update_actions_list()
            self._update_recording_buttons()
            
            # Update status
            if self.is_recording:
                if self.is_paused:
                    self._update_status("Recording paused")
                else:
                    action_count = len(self.recorded_actions)
                    self._update_status(f"Recording... {action_count} actions")
            else:
                action_count = len(self.recorded_actions)
                if action_count > 0:
                    self._update_status(f"Ready - {action_count} actions recorded")
                else:
                    self._update_status("Ready to record")
                    
        except Exception as e:
            logger.error(f"Error refreshing display: {e}")

    def edit_selected_action(self):
        """Edit the selected action."""
        try:
            if not self.actions_listbox:
                return

            selected = self.actions_listbox.curselection()
            if not selected:
                show_info_message("No Selection", "Please select an action to edit")
                return

            index = selected[0]
            if 0 <= index < len(self.recorded_actions):
                action = self.recorded_actions[index]
                self._show_edit_dialog(action, index)

        except Exception as e:
            logger.error(f"Error editing action: {e}")
            show_warning_message("Edit Error", f"Failed to edit action: {e}")

    def _edit_selected_action(self, event=None):
        """Handle double-click to edit action."""
        self.edit_selected_action()

    def delete_selected_action(self):
        """Delete the selected action."""
        try:
            if not self.actions_listbox:
                return

            selected = self.actions_listbox.curselection()
            if not selected:
                show_info_message("No Selection", "Please select an action to delete")
                return

            index = selected[0]
            if 0 <= index < len(self.recorded_actions):
                action = self.recorded_actions[index]
                action_desc = self._get_action_description(action)

                # Confirm deletion
                result = messagebox.askyesno(
                    "Delete Action",
                    f"Are you sure you want to delete this action?\n\n{action_desc}"
                )

                if result:
                    del self.recorded_actions[index]
                    self._update_actions_list()

                    # Clear details if this was the selected action
                    if self.action_details_text:
                        self.action_details_text.delete(1.0, tk.END)

                    self._update_status(f"Action {index + 1} deleted")
                    logger.info(f"Deleted action {index + 1}: {action_desc}")

        except Exception as e:
            logger.error(f"Error deleting action: {e}")
            show_warning_message("Delete Error", f"Failed to delete action: {e}")

    def duplicate_selected_action(self):
        """Duplicate the selected action."""
        try:
            if not self.actions_listbox:
                return

            selected = self.actions_listbox.curselection()
            if not selected:
                show_info_message("No Selection", "Please select an action to duplicate")
                return

            index = selected[0]
            if 0 <= index < len(self.recorded_actions):
                action = self.recorded_actions[index].copy()

                # Insert duplicate after the original
                self.recorded_actions.insert(index + 1, action)
                self._update_actions_list()

                # Select the new duplicate
                self.actions_listbox.selection_clear(0, tk.END)
                self.actions_listbox.selection_set(index + 1)
                self.actions_listbox.see(index + 1)

                action_desc = self._get_action_description(action)
                self._update_status(f"Action duplicated: {action_desc}")
                logger.info(f"Duplicated action {index + 1}")

        except Exception as e:
            logger.error(f"Error duplicating action: {e}")
            show_warning_message("Duplicate Error", f"Failed to duplicate action: {e}")

    def move_action_up(self):
        """Move selected action up in the list."""
        try:
            if not self.actions_listbox:
                return

            selected = self.actions_listbox.curselection()
            if not selected:
                show_info_message("No Selection", "Please select an action to move")
                return

            index = selected[0]
            if index > 0:
                # Swap with previous action
                self.recorded_actions[index], self.recorded_actions[index - 1] = \
                    self.recorded_actions[index - 1], self.recorded_actions[index]

                self._update_actions_list()

                # Keep selection on moved action
                self.actions_listbox.selection_clear(0, tk.END)
                self.actions_listbox.selection_set(index - 1)
                self.actions_listbox.see(index - 1)

                self._update_status(f"Action {index + 1} moved up")
                logger.info(f"Moved action {index + 1} up")
            else:
                show_info_message("Cannot Move", "Action is already at the top")

        except Exception as e:
            logger.error(f"Error moving action up: {e}")
            show_warning_message("Move Error", f"Failed to move action: {e}")

    def move_action_down(self):
        """Move selected action down in the list."""
        try:
            if not self.actions_listbox:
                return

            selected = self.actions_listbox.curselection()
            if not selected:
                show_info_message("No Selection", "Please select an action to move")
                return

            index = selected[0]
            if index < len(self.recorded_actions) - 1:
                # Swap with next action
                self.recorded_actions[index], self.recorded_actions[index + 1] = \
                    self.recorded_actions[index + 1], self.recorded_actions[index]

                self._update_actions_list()

                # Keep selection on moved action
                self.actions_listbox.selection_clear(0, tk.END)
                self.actions_listbox.selection_set(index + 1)
                self.actions_listbox.see(index + 1)

                self._update_status(f"Action {index + 1} moved down")
                logger.info(f"Moved action {index + 1} down")
            else:
                show_info_message("Cannot Move", "Action is already at the bottom")

        except Exception as e:
            logger.error(f"Error moving action down: {e}")
            show_warning_message("Move Error", f"Failed to move action: {e}")

    def insert_delay_action(self):
        """Insert a delay action before the selected action."""
        try:
            if not self.actions_listbox:
                return

            selected = self.actions_listbox.curselection()
            insert_index = selected[0] if selected else len(self.recorded_actions)

            self._show_delay_dialog(insert_index)

        except Exception as e:
            logger.error(f"Error inserting delay: {e}")
            show_warning_message("Insert Error", f"Failed to insert delay: {e}")

    def insert_delay_before(self):
        """Insert a delay before the selected action."""
        try:
            if not self.actions_listbox:
                return

            selected = self.actions_listbox.curselection()
            if not selected:
                show_info_message("No Selection", "Please select an action to insert delay before")
                return

            index = selected[0]
            self._show_delay_dialog(index)

        except Exception as e:
            logger.error(f"Error inserting delay: {e}")
            show_warning_message("Insert Error", f"Failed to insert delay: {e}")

    def _get_action_description(self, action):
        """Get a brief description of an action."""
        try:
            action_type = action.get('type', 'unknown')

            if action_type == 'key':
                key = action.get('key', 'unknown')
                return f"Key press: {key}"
            elif action_type in ['click', 'mouse']:
                button = action.get('button', 'unknown')
                x = action.get('x', 0)
                y = action.get('y', 0)
                return f"{button.title()} click at ({x}, {y})"
            elif action_type == 'delay':
                duration = action.get('duration', 0)
                return f"Delay: {duration:.2f}s"
            else:
                return f"Unknown action: {action_type}"

        except Exception as e:
            return f"Action (error: {e})"

    def _show_edit_dialog(self, action, index):
        """Show dialog to edit an action."""
        try:
            dialog = tk.Toplevel(self.root)
            dialog.title(f"Edit Action {index + 1}")
            dialog.geometry("400x300")
            dialog.transient(self.root)
            dialog.grab_set()

            # Center dialog
            dialog.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 100,
                self.root.winfo_rooty() + 100
            ))

            # Action type
            ttk.Label(dialog, text="Action Type:", font=('Arial', 10, 'bold')).pack(pady=5)
            type_var = tk.StringVar(value=action.get('type', 'unknown'))
            ttk.Label(dialog, text=type_var.get()).pack(pady=5)

            # Editable fields based on action type
            fields = {}

            if action['type'] == 'key':
                # Key field
                ttk.Label(dialog, text="Key:").pack(pady=5)
                fields['key'] = tk.StringVar(value=str(action.get('key', '')))
                ttk.Entry(dialog, textvariable=fields['key'], width=20).pack(pady=5)

            elif action['type'] in ['click', 'mouse']:
                # Button field
                ttk.Label(dialog, text="Button:").pack(pady=5)
                fields['button'] = tk.StringVar(value=action.get('button', 'left'))
                button_combo = ttk.Combobox(dialog, textvariable=fields['button'],
                                          values=['left', 'right', 'middle'], width=15)
                button_combo.pack(pady=5)

                # Position fields
                pos_frame = ttk.Frame(dialog)
                pos_frame.pack(pady=5)

                ttk.Label(pos_frame, text="X:").pack(side=tk.LEFT, padx=5)
                fields['x'] = tk.IntVar(value=action.get('x', 0))
                ttk.Spinbox(pos_frame, from_=0, to=9999, textvariable=fields['x'], width=8).pack(side=tk.LEFT, padx=5)

                ttk.Label(pos_frame, text="Y:").pack(side=tk.LEFT, padx=5)
                fields['y'] = tk.IntVar(value=action.get('y', 0))
                ttk.Spinbox(pos_frame, from_=0, to=9999, textvariable=fields['y'], width=8).pack(side=tk.LEFT, padx=5)

            elif action['type'] == 'delay':
                # Duration field
                ttk.Label(dialog, text="Duration (seconds):").pack(pady=5)
                fields['duration'] = tk.DoubleVar(value=action.get('duration', 1.0))
                ttk.Spinbox(dialog, from_=0.1, to=60.0, increment=0.1,
                           textvariable=fields['duration'], width=10).pack(pady=5)

            # Timestamp field (always editable)
            ttk.Label(dialog, text="Timestamp (seconds):").pack(pady=5)
            fields['timestamp'] = tk.DoubleVar(value=action.get('timestamp', 0))
            ttk.Spinbox(dialog, from_=0.0, to=9999.0, increment=0.1,
                       textvariable=fields['timestamp'], width=10).pack(pady=5)

            # Buttons
            button_frame = ttk.Frame(dialog)
            button_frame.pack(pady=20)

            def save_changes():
                try:
                    # Update action with new values
                    for field, var in fields.items():
                        action[field] = var.get()

                    # Update the list
                    self._update_actions_list()

                    # Update selection to show changes
                    if self.actions_listbox:
                        self.actions_listbox.selection_clear(0, tk.END)
                        self.actions_listbox.selection_set(index)
                        self.actions_listbox.see(index)

                    dialog.destroy()
                    self._update_status(f"Action {index + 1} updated")
                    logger.info(f"Updated action {index + 1}")

                except Exception as e:
                    logger.error(f"Error saving action changes: {e}")
                    show_warning_message("Save Error", f"Failed to save changes: {e}")

            save_btn = create_styled_button(button_frame, "Save", save_changes, self.colors['success'])
            save_btn.pack(side=tk.LEFT, padx=5)

            cancel_btn = create_styled_button(button_frame, "Cancel", dialog.destroy, self.colors['danger'])
            cancel_btn.pack(side=tk.LEFT, padx=5)

        except Exception as e:
            logger.error(f"Error showing edit dialog: {e}")
            show_warning_message("Dialog Error", f"Failed to show edit dialog: {e}")

    def _show_delay_dialog(self, insert_index):
        """Show dialog to insert a delay."""
        try:
            dialog = tk.Toplevel(self.root)
            dialog.title("Insert Delay")
            dialog.geometry("300x200")
            dialog.transient(self.root)
            dialog.grab_set()

            # Center dialog
            dialog.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 150,
                self.root.winfo_rooty() + 150
            ))

            ttk.Label(dialog, text="Insert Delay", font=('Arial', 12, 'bold')).pack(pady=10)
            ttk.Label(dialog, text="Duration (seconds):").pack(pady=5)

            duration_var = tk.DoubleVar(value=1.0)
            duration_spinbox = ttk.Spinbox(dialog, from_=0.1, to=60.0, increment=0.1,
                                         textvariable=duration_var, width=10)
            duration_spinbox.pack(pady=5)
            duration_spinbox.focus()

            button_frame = ttk.Frame(dialog)
            button_frame.pack(pady=20)

            def insert_delay():
                try:
                    duration = duration_var.get()

                    # Calculate timestamp
                    if insert_index > 0:
                        prev_timestamp = self.recorded_actions[insert_index - 1].get('timestamp', 0)
                    else:
                        prev_timestamp = 0

                    # Create delay action
                    delay_action = {
                        'type': 'delay',
                        'duration': duration,
                        'timestamp': prev_timestamp + 0.1
                    }

                    # Insert the delay
                    self.recorded_actions.insert(insert_index, delay_action)
                    self._update_actions_list()

                    # Select the new delay
                    if self.actions_listbox:
                        self.actions_listbox.selection_clear(0, tk.END)
                        self.actions_listbox.selection_set(insert_index)
                        self.actions_listbox.see(insert_index)

                    dialog.destroy()
                    self._update_status(f"Delay of {duration:.1f}s inserted")
                    logger.info(f"Inserted delay of {duration:.1f}s at position {insert_index}")

                except Exception as e:
                    logger.error(f"Error inserting delay: {e}")
                    show_warning_message("Insert Error", f"Failed to insert delay: {e}")

            insert_btn = create_styled_button(button_frame, "Insert", insert_delay, self.colors['success'])
            insert_btn.pack(side=tk.LEFT, padx=5)

            cancel_btn = create_styled_button(button_frame, "Cancel", dialog.destroy, self.colors['danger'])
            cancel_btn.pack(side=tk.LEFT, padx=5)

            # Bind Enter key to insert
            dialog.bind('<Return>', lambda e: insert_delay())

        except Exception as e:
            logger.error(f"Error showing delay dialog: {e}")
            show_warning_message("Dialog Error", f"Failed to show delay dialog: {e}")

    def export_actions(self, format_type='json'):
        """Export recorded actions to file."""
        try:
            if not self.recorded_actions:
                show_warning_message("Info", "No actions to export")
                return False

            from tkinter import filedialog
            import json

            if format_type == 'json':
                filename = filedialog.asksaveasfilename(
                    title="Export Actions (JSON)",
                    defaultextension=".json",
                    filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
                )

                if filename:
                    export_data = {
                        "metadata": {
                            "export_date": datetime.now().isoformat(),
                            "version": "1.0",
                            "total_actions": len(self.recorded_actions),
                            "application": "Rappelz Macro Recorder"
                        },
                        "actions": self.recorded_actions
                    }

                    with open(filename, 'w') as f:
                        json.dump(export_data, f, indent=2)

                    self._update_status(f"Actions exported to {filename}")
                    show_info_message("Export Complete",
                        f"Actions exported successfully to:\n{filename}\n\n"
                        f"Format: JSON (can be imported)\n"
                        f"Actions: {len(self.recorded_actions)}")

                    logger.info(f"Exported {len(self.recorded_actions)} actions to {filename}")
                    return True

            elif format_type == 'text':
                filename = filedialog.asksaveasfilename(
                    title="Export Actions (Text)",
                    defaultextension=".txt",
                    filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
                )

                if filename:
                    with open(filename, 'w') as f:
                        f.write(f"Macro Actions Export - {len(self.recorded_actions)} actions\n")
                        f.write(f"Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write("=" * 60 + "\n\n")

                        for i, action in enumerate(self.recorded_actions):
                            action_desc = self._format_action_details(action, i + 1)
                            f.write(f"{action_desc}\n\n")

                    self._update_status(f"Actions exported to {filename}")
                    show_info_message("Export Complete",
                        f"Actions exported successfully to:\n{filename}\n\n"
                        f"Format: Text (human readable only)\n"
                        f"Actions: {len(self.recorded_actions)}")

                    logger.info(f"Exported {len(self.recorded_actions)} actions to {filename}")
                    return True

            return False

        except Exception as e:
            logger.error(f"Error exporting actions: {e}")
            show_warning_message("Export Error", f"Failed to export actions: {e}")
            return False

    def import_actions(self, merge_mode='ask'):
        """Import actions from JSON file."""
        try:
            from tkinter import filedialog
            import json

            filename = filedialog.askopenfilename(
                title="Import Actions",
                filetypes=[
                    ("JSON files", "*.json"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                with open(filename, 'r') as f:
                    data = json.load(f)

                # Handle different import formats
                if isinstance(data, dict) and "actions" in data:
                    # New format with metadata
                    imported_actions = data["actions"]
                    metadata = data.get("metadata", {})
                    total_actions = metadata.get("total_actions", len(imported_actions))
                elif isinstance(data, list):
                    # Direct actions list (legacy format)
                    imported_actions = data
                    total_actions = len(imported_actions)
                else:
                    show_warning_message("Import Error", "Invalid file format. Expected JSON with actions list.")
                    return False

                if not imported_actions:
                    show_warning_message("Import Error", "No actions found in the file.")
                    return False

                # Handle merge mode
                if merge_mode == 'ask' and self.recorded_actions:
                    choice = messagebox.askyesnocancel(
                        "Import Actions",
                        f"You have {len(self.recorded_actions)} existing actions.\n\n"
                        f"Found {total_actions} actions to import.\n\n"
                        f"Yes: Replace existing actions\n"
                        f"No: Append to existing actions\n"
                        f"Cancel: Cancel import"
                    )

                    if choice is None:  # Cancel
                        return False
                    elif choice:  # Yes - Replace
                        self.recorded_actions.clear()
                        self.recorded_actions.extend(imported_actions)
                        action_text = "replaced"
                    else:  # No - Append
                        self.recorded_actions.extend(imported_actions)
                        action_text = "appended"
                elif merge_mode == 'replace':
                    self.recorded_actions.clear()
                    self.recorded_actions.extend(imported_actions)
                    action_text = "replaced"
                elif merge_mode == 'append':
                    self.recorded_actions.extend(imported_actions)
                    action_text = "appended"
                else:
                    # No existing actions, just import
                    self.recorded_actions.extend(imported_actions)
                    action_text = "imported"

                # Update UI
                self._update_actions_list()
                self._update_status(f"{len(imported_actions)} actions {action_text} from {filename}")

                show_info_message("Import Complete",
                    f"Actions {action_text} successfully!\n\n"
                    f"File: {filename}\n"
                    f"Imported: {len(imported_actions)} actions\n"
                    f"Total actions: {len(self.recorded_actions)}")

                logger.info(f"Imported {len(imported_actions)} actions from {filename}")
                return True

        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            show_warning_message("Import Error", f"Invalid JSON file: {e}")
            return False
        except Exception as e:
            logger.error(f"Error importing actions: {e}")
            show_warning_message("Import Error", f"Failed to import actions: {e}")
            return False

    def save_as_macro(self):
        """Save current actions as a named macro file."""
        try:
            if not self.recorded_actions:
                show_warning_message("Info", "No actions to save")
                return False

            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                title="Save Macro",
                defaultextension=".json",
                filetypes=[("Macro files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                success = save_actions(filename, self.recorded_actions)
                if success:
                    self._update_status(f"Macro saved to {filename}")
                    show_info_message("Save Complete", f"Macro saved successfully to:\n{filename}")
                    logger.info(f"Saved macro to {filename}")
                    return True
                else:
                    show_warning_message("Save Error", "Failed to save macro")
                    return False

            return False

        except Exception as e:
            logger.error(f"Error saving macro: {e}")
            show_warning_message("Save Error", f"Failed to save macro: {e}")
            return False

    def load_macro(self):
        """Load a macro file."""
        try:
            from tkinter import filedialog

            filename = filedialog.askopenfilename(
                title="Load Macro",
                filetypes=[
                    ("Macro files", "*.json"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                loaded_actions = load_actions(filename)

                if not loaded_actions:
                    show_warning_message("Load Error", "No actions found in the macro file.")
                    return False

                # Ask user how to handle existing actions
                if self.recorded_actions:
                    choice = messagebox.askyesnocancel(
                        "Load Macro",
                        f"You have {len(self.recorded_actions)} existing actions.\n\n"
                        f"Found {len(loaded_actions)} actions in macro.\n\n"
                        f"Yes: Replace existing actions\n"
                        f"No: Append to existing actions\n"
                        f"Cancel: Cancel load"
                    )

                    if choice is None:  # Cancel
                        return False
                    elif choice:  # Yes - Replace
                        self.recorded_actions.clear()
                        self.recorded_actions.extend(loaded_actions)
                        action_text = "replaced"
                    else:  # No - Append
                        self.recorded_actions.extend(loaded_actions)
                        action_text = "appended"
                else:
                    # No existing actions, just load
                    self.recorded_actions.extend(loaded_actions)
                    action_text = "loaded"

                # Update UI
                self._update_actions_list()
                self._update_status(f"Macro {action_text} from {filename}")

                show_info_message("Load Complete",
                    f"Macro {action_text} successfully!\n\n"
                    f"File: {filename}\n"
                    f"Actions: {len(loaded_actions)}\n"
                    f"Total actions: {len(self.recorded_actions)}")

                logger.info(f"Loaded macro from {filename}")
                return True

        except Exception as e:
            logger.error(f"Error loading macro: {e}")
            show_warning_message("Load Error", f"Failed to load macro: {e}")
            return False

    def quick_save_actions(self):
        """Quick save actions with auto-generated filename."""
        try:
            if not self.recorded_actions:
                show_warning_message("Info", "No actions to save")
                return False

            import os

            # Create saves directory if it doesn't exist
            saves_dir = "saved_macros"
            if not os.path.exists(saves_dir):
                os.makedirs(saves_dir)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(saves_dir, f"macro_{timestamp}.json")

            # Save the actions
            success = save_actions(filename, self.recorded_actions)
            if success:
                self._update_status(f"Quick saved to {filename}")
                show_info_message("Quick Save Complete",
                    f"Macro saved successfully!\n\n"
                    f"File: {filename}\n"
                    f"Actions: {len(self.recorded_actions)}\n"
                    f"Location: {os.path.abspath(filename)}")

                logger.info(f"Quick saved macro to {filename}")
                return True
            else:
                show_warning_message("Quick Save Error", "Failed to quick save")
                return False

        except Exception as e:
            logger.error(f"Error quick saving: {e}")
            show_warning_message("Quick Save Error", f"Failed to quick save: {e}")
            return False

    def import_actions_from_list(self, actions_list, merge_mode='ask'):
        """Import actions from a list directly."""
        try:
            if not actions_list:
                show_warning_message("Import Error", "No actions to import")
                return False

            # Handle merge mode
            if merge_mode == 'ask' and self.recorded_actions:
                choice = messagebox.askyesnocancel(
                    "Import Actions",
                    f"You have {len(self.recorded_actions)} existing actions.\n\n"
                    f"Found {len(actions_list)} actions to import.\n\n"
                    f"Yes: Replace existing actions\n"
                    f"No: Append to existing actions\n"
                    f"Cancel: Cancel import"
                )

                if choice is None:  # Cancel
                    return False
                elif choice:  # Yes - Replace
                    self.recorded_actions.clear()
                    self.recorded_actions.extend(actions_list)
                    action_text = "replaced"
                else:  # No - Append
                    self.recorded_actions.extend(actions_list)
                    action_text = "appended"
            elif merge_mode == 'replace':
                self.recorded_actions.clear()
                self.recorded_actions.extend(actions_list)
                action_text = "replaced"
            elif merge_mode == 'append':
                self.recorded_actions.extend(actions_list)
                action_text = "appended"
            else:
                # No existing actions, just import
                self.recorded_actions.extend(actions_list)
                action_text = "imported"

            # Update UI
            self._update_actions_list()
            self._update_status(f"{len(actions_list)} actions {action_text}")

            logger.info(f"Imported {len(actions_list)} actions from list")
            return True

        except Exception as e:
            logger.error(f"Error importing actions from list: {e}")
            show_warning_message("Import Error", f"Failed to import actions: {e}")
            return False

    def export_actions(self, format_type='json'):
        """Export actions to file."""
        try:
            if not self.recorded_actions:
                show_warning_message("Export Error", "No actions to export")
                return False

            from tkinter import filedialog
            import json
            from datetime import datetime

            if format_type == 'json':
                filename = filedialog.asksaveasfilename(
                    title="Export Actions (JSON)",
                    defaultextension=".json",
                    filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
                )

                if filename:
                    export_data = {
                        "metadata": {
                            "export_date": datetime.now().isoformat(),
                            "version": "1.0",
                            "total_actions": len(self.recorded_actions),
                            "application": "Rappelz Macro Recorder"
                        },
                        "actions": self.recorded_actions
                    }

                    with open(filename, 'w') as f:
                        json.dump(export_data, f, indent=2)

                    self._update_status(f"Actions exported to {filename}")
                    show_info_message("Export Complete",
                        f"Actions exported successfully to:\n{filename}\n\n"
                        f"Format: JSON (can be imported)\n"
                        f"Actions: {len(self.recorded_actions)}")
                    return True

            elif format_type == 'text':
                filename = filedialog.asksaveasfilename(
                    title="Export Actions (Text)",
                    defaultextension=".txt",
                    filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
                )

                if filename:
                    with open(filename, 'w') as f:
                        f.write(f"Macro Actions Export - {len(self.recorded_actions)} actions\n")
                        f.write(f"Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write("=" * 60 + "\n\n")

                        for i, action in enumerate(self.recorded_actions):
                            action_desc = self._format_action_details(action, i + 1)
                            f.write(f"{action_desc}\n")
                            f.write(f"  Raw data: {action}\n\n")

                    self._update_status(f"Actions exported to {filename}")
                    show_info_message("Export Complete",
                        f"Actions exported successfully to:\n{filename}\n\n"
                        f"Format: Text (human readable only)\n"
                        f"Actions: {len(self.recorded_actions)}")
                    return True

            return False

        except Exception as e:
            logger.error(f"Error exporting actions: {e}")
            show_warning_message("Export Error", f"Failed to export actions: {e}")
            return False

    def import_actions(self):
        """Import actions from JSON format."""
        try:
            from tkinter import filedialog
            import json

            filename = filedialog.askopenfilename(
                title="Import Actions",
                filetypes=[
                    ("JSON files", "*.json"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                with open(filename, 'r') as f:
                    data = json.load(f)

                # Handle different import formats
                if isinstance(data, dict) and "actions" in data:
                    # New format with metadata
                    imported_actions = data["actions"]
                    metadata = data.get("metadata", {})
                    total_actions = metadata.get("total_actions", len(imported_actions))
                elif isinstance(data, list):
                    # Direct actions list (legacy format)
                    imported_actions = data
                    total_actions = len(imported_actions)
                else:
                    show_warning_message("Import Error", "Invalid file format. Expected JSON with actions list.")
                    return False

                if not imported_actions:
                    show_warning_message("Import Error", "No actions found in the file.")
                    return False

                # Use the existing import_actions_from_list method
                return self.import_actions_from_list(imported_actions, 'ask')

        except json.JSONDecodeError as e:
            show_warning_message("Import Error", f"Invalid JSON file: {e}")
            return False
        except Exception as e:
            logger.error(f"Error importing actions: {e}")
            show_warning_message("Import Error", f"Failed to import actions: {e}")
            return False

    def save_as_macro(self):
        """Save current actions as a named macro file."""
        try:
            if not self.recorded_actions:
                show_warning_message("Save Error", "No actions to save")
                return False

            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                title="Save Macro",
                defaultextension=".json",
                filetypes=[("Macro files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                # Use the existing save_actions function from config
                save_actions(filename, self.recorded_actions)
                self._update_status(f"Macro saved to {filename}")
                show_info_message("Save Complete", f"Macro saved successfully to:\n{filename}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error saving macro: {e}")
            show_warning_message("Save Error", f"Failed to save macro: {e}")
            return False

    def load_macro(self):
        """Load a macro file."""
        try:
            from tkinter import filedialog

            filename = filedialog.askopenfilename(
                title="Load Macro",
                filetypes=[
                    ("Macro files", "*.json"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                # Use the existing load_actions function from config
                loaded_actions = load_actions(filename)

                if not loaded_actions:
                    show_warning_message("Load Error", "No actions found in the macro file.")
                    return False

                # Use the existing import_actions_from_list method
                success = self.import_actions_from_list(loaded_actions, 'ask')
                if success:
                    show_info_message("Load Complete",
                        f"Macro loaded successfully!\n\n"
                        f"File: {filename}\n"
                        f"Actions: {len(loaded_actions)}\n"
                        f"Total actions: {len(self.recorded_actions)}")
                return success

            return False

        except Exception as e:
            logger.error(f"Error loading macro: {e}")
            show_warning_message("Load Error", f"Failed to load macro: {e}")
            return False

    def quick_save_actions(self):
        """Quick save actions with auto-generated filename."""
        try:
            if not self.recorded_actions:
                show_warning_message("Quick Save Error", "No actions to save")
                return False

            from datetime import datetime
            import os

            # Create saves directory if it doesn't exist
            saves_dir = "saved_macros"
            if not os.path.exists(saves_dir):
                os.makedirs(saves_dir)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(saves_dir, f"macro_{timestamp}.json")

            # Save the actions
            save_actions(filename, self.recorded_actions)

            self._update_status(f"Quick saved to {filename}")
            show_info_message("Quick Save Complete",
                f"Macro saved successfully!\n\n"
                f"File: {filename}\n"
                f"Actions: {len(self.recorded_actions)}\n"
                f"Location: {os.path.abspath(filename)}")

            return True

        except Exception as e:
            logger.error(f"Error in quick save: {e}")
            show_warning_message("Quick Save Error", f"Failed to quick save: {e}")
            return False

    def load_last_macro(self):
        """Load the last used macro if available."""
        try:
            from config import load_last_macro
            import os

            recorded_actions_loaded, last_path = load_last_macro()
            if recorded_actions_loaded is not None:
                # Use recording manager to import the actions
                success = self.import_actions_from_list(recorded_actions_loaded, 'replace')
                if success:
                    self._update_status(f"Loaded last macro: {os.path.basename(last_path) if last_path else 'Unknown'}")
                    logger.info(f"Loaded last macro from {last_path}")
                return success

            return False

        except Exception as e:
            logger.error(f"Error loading last macro: {e}")
            return False
