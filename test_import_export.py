"""
Test script for import/export functionality
"""

import json
import os
import tempfile
from datetime import datetime

def test_json_export_format():
    """Test the JSON export format structure."""
    print("Testing JSON export format...")
    
    # Sample actions data
    sample_actions = [
        {
            "type": "key",
            "key": "space",
            "timestamp": 1.234
        },
        {
            "type": "click",
            "button": "left",
            "x": 100,
            "y": 200,
            "timestamp": 2.456
        },
        {
            "type": "delay",
            "duration": 1.5,
            "timestamp": 3.789
        }
    ]
    
    # Create export data structure
    export_data = {
        "metadata": {
            "export_date": datetime.now().isoformat(),
            "version": "1.0",
            "total_actions": len(sample_actions),
            "application": "Rappelz Macro Recorder"
        },
        "actions": sample_actions
    }
    
    # Test JSON serialization
    try:
        json_str = json.dumps(export_data, indent=2)
        print("  ✅ JSON serialization successful")
        
        # Test JSON deserialization
        parsed_data = json.loads(json_str)
        print("  ✅ JSON deserialization successful")
        
        # Validate structure
        assert "metadata" in parsed_data
        assert "actions" in parsed_data
        assert len(parsed_data["actions"]) == 3
        print("  ✅ JSON structure validation successful")
        
        return True
        
    except Exception as e:
        print(f"  ❌ JSON format test failed: {e}")
        return False

def test_file_operations():
    """Test file save and load operations."""
    print("\nTesting file operations...")
    
    # Sample data
    test_data = {
        "metadata": {
            "export_date": datetime.now().isoformat(),
            "version": "1.0",
            "total_actions": 2,
            "application": "Rappelz Macro Recorder"
        },
        "actions": [
            {"type": "key", "key": "a", "timestamp": 1.0},
            {"type": "click", "button": "left", "x": 50, "y": 75, "timestamp": 2.0}
        ]
    }
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_filename = f.name
        
        try:
            # Test file writing
            json.dump(test_data, f, indent=2)
            print("  ✅ File writing successful")
            
        except Exception as e:
            print(f"  ❌ File writing failed: {e}")
            return False
    
    try:
        # Test file reading
        with open(temp_filename, 'r') as f:
            loaded_data = json.load(f)
        
        print("  ✅ File reading successful")
        
        # Validate loaded data
        assert loaded_data["metadata"]["total_actions"] == 2
        assert len(loaded_data["actions"]) == 2
        assert loaded_data["actions"][0]["type"] == "key"
        assert loaded_data["actions"][1]["type"] == "click"
        print("  ✅ Data validation successful")
        
        return True
        
    except Exception as e:
        print(f"  ❌ File reading/validation failed: {e}")
        return False
    
    finally:
        # Clean up
        try:
            os.unlink(temp_filename)
            print("  ✅ Temporary file cleaned up")
        except:
            pass

def test_legacy_format_compatibility():
    """Test compatibility with legacy action format."""
    print("\nTesting legacy format compatibility...")
    
    # Legacy format (direct actions list)
    legacy_actions = [
        {"type": "key", "key": "space", "timestamp": 1.0},
        {"type": "click", "button": "right", "x": 200, "y": 300, "timestamp": 2.0}
    ]
    
    try:
        # Test legacy format handling
        if isinstance(legacy_actions, list):
            imported_actions = legacy_actions
            total_actions = len(imported_actions)
            print("  ✅ Legacy format detected and handled")
        
        # Validate legacy data
        assert len(imported_actions) == 2
        assert imported_actions[0]["type"] == "key"
        assert imported_actions[1]["type"] == "click"
        print("  ✅ Legacy data validation successful")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Legacy format test failed: {e}")
        return False

def test_error_handling():
    """Test error handling for invalid data."""
    print("\nTesting error handling...")
    
    # Test invalid JSON
    try:
        invalid_json = '{"invalid": json format}'
        json.loads(invalid_json)
        print("  ❌ Should have failed on invalid JSON")
        return False
    except json.JSONDecodeError:
        print("  ✅ Invalid JSON properly rejected")
    
    # Test missing required fields
    try:
        incomplete_data = {"metadata": {"version": "1.0"}}  # Missing actions
        
        if "actions" not in incomplete_data:
            print("  ✅ Missing actions field detected")
        else:
            print("  ❌ Should have detected missing actions")
            return False
    except Exception as e:
        print(f"  ❌ Error handling test failed: {e}")
        return False
    
    # Test empty actions
    try:
        empty_data = {"actions": []}
        
        if not empty_data["actions"]:
            print("  ✅ Empty actions list detected")
        else:
            print("  ❌ Should have detected empty actions")
            return False
    except Exception as e:
        print(f"  ❌ Empty actions test failed: {e}")
        return False
    
    return True

def test_action_types():
    """Test all supported action types."""
    print("\nTesting action types...")
    
    action_types = [
        {"type": "key", "key": "enter", "timestamp": 1.0},
        {"type": "click", "button": "left", "x": 100, "y": 200, "timestamp": 2.0},
        {"type": "delay", "duration": 2.5, "timestamp": 3.0}
    ]
    
    try:
        for i, action in enumerate(action_types):
            action_type = action.get("type", "unknown")
            
            if action_type == "key":
                assert "key" in action
                print(f"  ✅ Key action {i+1} validated")
            elif action_type == "click":
                assert "button" in action
                assert "x" in action
                assert "y" in action
                print(f"  ✅ Click action {i+1} validated")
            elif action_type == "delay":
                assert "duration" in action
                print(f"  ✅ Delay action {i+1} validated")
            else:
                print(f"  ❌ Unknown action type: {action_type}")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Action type test failed: {e}")
        return False

def main():
    """Run all import/export tests."""
    print("=" * 60)
    print("Import/Export Functionality Test Suite")
    print("=" * 60)
    
    tests = [
        test_json_export_format,
        test_file_operations,
        test_legacy_format_compatibility,
        test_error_handling,
        test_action_types
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print(f"  ❌ Test failed: {test.__name__}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All import/export tests passed!")
        print("\nThe import/export functionality is ready for use:")
        print("  • JSON export/import working correctly")
        print("  • File operations functioning properly")
        print("  • Legacy format compatibility maintained")
        print("  • Error handling robust")
        print("  • All action types supported")
    else:
        print("❌ Some tests failed. Please review the implementation.")
    
    print("=" * 60)
    return passed == total

if __name__ == "__main__":
    main()
