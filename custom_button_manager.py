"""
Custom Button Management Module

Generalizes the death detection logic to allow users to:
- Define custom templates (upload images)
- Set matching thresholds
- Configure one-click actions when matches are found
- Enable/disable individual templates
"""

import os
import time
import threading
import json
from datetime import datetime
from imports import logger, pyautogui, cv2, np, HAVE_PYAUTOGUI
from recorder import send_mouse_click_direct
from win_api import find_game_window, get_game_window_rect, game_window_handle
from config import game_window_settings

# Default settings for custom button management
DEFAULT_SETTINGS = {
    'enabled': False,
    'check_interval': 1.0,  # Seconds between checks
    'templates_dir': os.path.join(os.path.dirname(os.path.abspath(__file__)), "custom_templates"),
    'max_concurrent_detections': 5,  # Maximum number of templates to process simultaneously
    'global_cooldown': 0.5,  # Global cooldown between any detections
    'last_detection_time': 0,
}

# Global state variables
_custom_button_active = False
_custom_button_thread = None
_custom_templates = {}  # Dictionary to store template configurations

class CustomTemplate:
    """Represents a custom template for button detection."""
    
    def __init__(self, name, image_path, confidence=0.85, auto_click=True, enabled=True, 
                 click_offset_x=0, click_offset_y=0, cooldown=2.0):
        self.name = name
        self.image_path = image_path
        self.confidence = confidence  # Matching confidence threshold (0.0 to 1.0)
        self.auto_click = auto_click  # Whether to automatically click when found
        self.enabled = enabled  # Whether this template is active
        self.click_offset_x = click_offset_x  # X offset from template center for clicking
        self.click_offset_y = click_offset_y  # Y offset from template center for clicking
        self.cooldown = cooldown  # Seconds to wait before detecting this template again
        self.last_detection_time = 0  # Last time this template was detected
        self.detection_count = 0  # Total number of detections
        self.created_time = time.time()
        
    def to_dict(self):
        """Convert template to dictionary for serialization."""
        return {
            'name': self.name,
            'image_path': self.image_path,
            'confidence': self.confidence,
            'auto_click': self.auto_click,
            'enabled': self.enabled,
            'click_offset_x': self.click_offset_x,
            'click_offset_y': self.click_offset_y,
            'cooldown': self.cooldown,
            'last_detection_time': self.last_detection_time,
            'detection_count': self.detection_count,
            'created_time': self.created_time
        }
    
    @classmethod
    def from_dict(cls, data):
        """Create template from dictionary."""
        template = cls(
            name=data['name'],
            image_path=data['image_path'],
            confidence=data.get('confidence', 0.85),
            auto_click=data.get('auto_click', True),
            enabled=data.get('enabled', True),
            click_offset_x=data.get('click_offset_x', 0),
            click_offset_y=data.get('click_offset_y', 0),
            cooldown=data.get('cooldown', 2.0)
        )
        template.last_detection_time = data.get('last_detection_time', 0)
        template.detection_count = data.get('detection_count', 0)
        template.created_time = data.get('created_time', time.time())
        return template
    
    def can_detect(self):
        """Check if enough time has passed since last detection."""
        return (time.time() - self.last_detection_time) >= self.cooldown
    
    def mark_detected(self):
        """Mark this template as detected."""
        self.last_detection_time = time.time()
        self.detection_count += 1

def get_custom_button_settings():
    """Get the custom button management settings."""
    return DEFAULT_SETTINGS.copy()

def is_custom_button_active():
    """Check if custom button detection is currently active."""
    return _custom_button_active

def set_custom_button_active(active):
    """Set the custom button detection active state."""
    global _custom_button_active
    _custom_button_active = active

def get_custom_button_thread():
    """Get the current custom button detection thread."""
    return _custom_button_thread

def set_custom_button_thread(thread):
    """Set the custom button detection thread."""
    global _custom_button_thread
    _custom_button_thread = thread

def add_custom_template(template):
    """Add a custom template to the detection system."""
    global _custom_templates
    _custom_templates[template.name] = template
    logger.info(f"Added custom template: {template.name}")
    save_templates()

def remove_custom_template(name):
    """Remove a custom template from the detection system."""
    global _custom_templates
    if name in _custom_templates:
        del _custom_templates[name]
        logger.info(f"Removed custom template: {name}")
        save_templates()
        return True
    return False

def get_custom_template(name):
    """Get a custom template by name."""
    return _custom_templates.get(name)

def get_all_custom_templates():
    """Get all custom templates."""
    return _custom_templates.copy()

def update_custom_template(name, **kwargs):
    """Update a custom template's properties."""
    if name in _custom_templates:
        template = _custom_templates[name]
        for key, value in kwargs.items():
            if hasattr(template, key):
                setattr(template, key, value)
                logger.debug(f"Updated template {name}: {key} = {value}")
        save_templates()
        return True
    return False

def save_templates():
    """Save all templates to a JSON file."""
    try:
        # Create templates directory if it doesn't exist
        templates_dir = DEFAULT_SETTINGS['templates_dir']
        os.makedirs(templates_dir, exist_ok=True)
        
        # Save templates configuration
        config_path = os.path.join(templates_dir, 'templates_config.json')
        templates_data = {
            name: template.to_dict() 
            for name, template in _custom_templates.items()
        }
        
        with open(config_path, 'w') as f:
            json.dump(templates_data, f, indent=2)
        
        logger.debug(f"Saved {len(_custom_templates)} templates to {config_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving templates: {e}")
        return False

def load_templates():
    """Load templates from JSON file."""
    try:
        global _custom_templates
        templates_dir = DEFAULT_SETTINGS['templates_dir']
        config_path = os.path.join(templates_dir, 'templates_config.json')
        
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                templates_data = json.load(f)
            
            _custom_templates = {}
            for name, data in templates_data.items():
                try:
                    template = CustomTemplate.from_dict(data)
                    # Verify template image still exists
                    if os.path.exists(template.image_path):
                        _custom_templates[name] = template
                    else:
                        logger.warning(f"Template image not found: {template.image_path}")
                except Exception as e:
                    logger.error(f"Error loading template {name}: {e}")
            
            logger.info(f"Loaded {len(_custom_templates)} custom templates")
            return True
        else:
            logger.debug("No templates configuration file found")
            return True
    except Exception as e:
        logger.error(f"Error loading templates: {e}")
        return False

def validate_game_window():
    """Validate and refresh the game window handle if needed."""
    try:
        import win32gui
        from win_api import game_window_handle

        # Check if current handle is still valid
        if game_window_handle:
            try:
                # Try to get window text to validate handle
                win32gui.GetWindowText(game_window_handle)
                return True
            except:
                # Handle is invalid, need to re-find window
                logger.debug("Game window handle is invalid, re-finding window")
                return find_game_window()
        else:
            # No handle, try to find window
            return find_game_window()
    except Exception as e:
        logger.error(f"Error validating game window: {e}")
        return False

def detect_template(template):
    """Detect a single template on the screen and optionally click it."""
    try:
        if not HAVE_PYAUTOGUI:
            logger.error("PyAutoGUI not available for template matching")
            return False

        # Check if template can be detected (cooldown check)
        if not template.can_detect():
            return False

        # Take a screenshot
        screenshot = pyautogui.screenshot()
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

        # Load the template image
        if not os.path.exists(template.image_path):
            logger.error(f"Template file not found: {template.image_path}")
            return False

        template_img = cv2.imread(template.image_path)
        if template_img is None:
            logger.error(f"Failed to load template: {template.image_path}")
            return False

        # Template matching
        result = cv2.matchTemplate(screenshot, template_img, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, max_loc = cv2.minMaxLoc(result)

        logger.debug(f"Template '{template.name}' matching result: confidence = {max_val:.3f}")

        # Check if confidence is above threshold
        if max_val >= template.confidence:
            # Calculate click position
            h, w = template_img.shape[:2]
            click_x = max_loc[0] + w // 2 + template.click_offset_x
            click_y = max_loc[1] + h // 2 + template.click_offset_y

            logger.info(f"Template '{template.name}' detected at ({click_x}, {click_y}) with confidence {max_val:.3f}")

            # Mark as detected
            template.mark_detected()

            # If auto-click is enabled, click the button
            if template.auto_click:
                # If targeting game window only, activate it first
                if game_window_settings['target_game_only']:
                    if validate_game_window():
                        try:
                            import win32gui
                            win32gui.SetForegroundWindow(game_window_handle)
                            time.sleep(0.1)  # Small delay to ensure window is focused
                            logger.debug("Game window activated successfully")
                        except Exception as window_error:
                            logger.warning(f"Could not activate game window: {window_error}")
                    else:
                        logger.warning("Could not validate game window, clicking without activation")

                # Click the detected template
                try:
                    send_mouse_click_direct('left', click_x, click_y)
                    logger.info(f"Template '{template.name}' clicked successfully at ({click_x}, {click_y})")
                    return True
                except Exception as click_error:
                    logger.error(f"Failed to click template '{template.name}': {click_error}")
                    return False
            else:
                logger.info(f"Template '{template.name}' found but auto-click is disabled")
                return True
        else:
            # Only log at debug level for low confidence to reduce spam
            if max_val > 0.3:  # Only log if there's some detection
                logger.debug(f"Template '{template.name}' not found (confidence: {max_val:.3f} < {template.confidence:.3f})")
            return False

    except Exception as e:
        logger.error(f"Error detecting template '{template.name}': {e}")
        return False

def detect_custom_buttons():
    """Main detection loop for all custom button templates."""
    if not HAVE_PYAUTOGUI:
        logger.warning("PyAutoGUI not available, custom button detection disabled")
        return

    logger.info("Custom button detection started")
    consecutive_errors = 0
    max_consecutive_errors = 5

    while is_custom_button_active():
        try:
            current_time = time.time()

            # Check global cooldown
            if (current_time - DEFAULT_SETTINGS['last_detection_time']) >= DEFAULT_SETTINGS['global_cooldown']:

                # Get enabled templates
                enabled_templates = [
                    template for template in _custom_templates.values()
                    if template.enabled and template.can_detect()
                ]

                if enabled_templates:
                    # Process templates (limit concurrent processing)
                    max_concurrent = min(len(enabled_templates), DEFAULT_SETTINGS['max_concurrent_detections'])
                    templates_to_process = enabled_templates[:max_concurrent]

                    detection_occurred = False
                    for template in templates_to_process:
                        try:
                            if detect_template(template):
                                detection_occurred = True
                                DEFAULT_SETTINGS['last_detection_time'] = current_time
                                consecutive_errors = 0  # Reset error counter on success
                                break  # Only process one detection per cycle
                        except Exception as template_error:
                            logger.error(f"Error processing template '{template.name}': {template_error}")
                            consecutive_errors += 1

                # Wait between checks
                time.sleep(DEFAULT_SETTINGS['check_interval'])
            else:
                # Still in global cooldown period, wait shorter time
                time.sleep(0.1)

            # Reset error counter if we've been running successfully
            if consecutive_errors > 0:
                consecutive_errors = max(0, consecutive_errors - 1)

        except Exception as e:
            consecutive_errors += 1
            logger.error(f"Error in custom button detection loop: {e}")

            # If too many consecutive errors, increase wait time
            if consecutive_errors >= max_consecutive_errors:
                logger.warning(f"Too many consecutive errors ({consecutive_errors}), increasing wait time")
                time.sleep(5)
            else:
                time.sleep(1)

    logger.info("Custom button detection stopped")

def start_custom_button_detection():
    """Start the custom button detection in a separate thread."""
    global _custom_button_thread

    if not _custom_button_active:
        # Load templates before starting
        load_templates()

        if not _custom_templates:
            logger.warning("No custom templates defined, detection not started")
            return False

        set_custom_button_active(True)
        _custom_button_thread = threading.Thread(target=detect_custom_buttons)
        _custom_button_thread.daemon = True
        _custom_button_thread.start()
        logger.info("Custom button detection thread started")
        return True
    else:
        logger.warning("Custom button detection is already active")
        return False

def stop_custom_button_detection():
    """Stop the custom button detection."""
    if _custom_button_active:
        set_custom_button_active(False)
        logger.info("Custom button detection stop requested")
        return True
    else:
        logger.warning("Custom button detection is not active")
        return False

def update_custom_button_settings(**kwargs):
    """Update custom button detection settings."""
    for key, value in kwargs.items():
        if key in DEFAULT_SETTINGS:
            DEFAULT_SETTINGS[key] = value
            logger.debug(f"Updated custom button setting: {key} = {value}")
        else:
            logger.warning(f"Unknown custom button setting: {key}")

def get_custom_button_status():
    """Get the current status of custom button detection."""
    return {
        'active': _custom_button_active,
        'thread_alive': _custom_button_thread.is_alive() if _custom_button_thread else False,
        'settings': DEFAULT_SETTINGS.copy(),
        'templates_count': len(_custom_templates),
        'enabled_templates_count': len([t for t in _custom_templates.values() if t.enabled])
    }

def create_template_from_file(name, image_path, **kwargs):
    """Create a new template from an image file."""
    try:
        # Verify the image file exists and is valid
        if not os.path.exists(image_path):
            logger.error(f"Image file not found: {image_path}")
            return None

        # Try to load the image to verify it's valid
        test_img = cv2.imread(image_path)
        if test_img is None:
            logger.error(f"Invalid image file: {image_path}")
            return None

        # Create the template
        template = CustomTemplate(name, image_path, **kwargs)

        # Copy image to templates directory if it's not already there
        templates_dir = DEFAULT_SETTINGS['templates_dir']
        os.makedirs(templates_dir, exist_ok=True)

        # Generate a unique filename in templates directory
        filename = f"{name}_{int(time.time())}.png"
        dest_path = os.path.join(templates_dir, filename)

        # Copy the image file
        import shutil
        shutil.copy2(image_path, dest_path)

        # Update template path to point to copied file
        template.image_path = dest_path

        # Add to templates
        add_custom_template(template)

        logger.info(f"Created template '{name}' from {image_path}")
        return template

    except Exception as e:
        logger.error(f"Error creating template from file: {e}")
        return None

def get_template_statistics():
    """Get statistics about template detections."""
    stats = {
        'total_templates': len(_custom_templates),
        'enabled_templates': 0,
        'total_detections': 0,
        'templates': {}
    }

    for name, template in _custom_templates.items():
        if template.enabled:
            stats['enabled_templates'] += 1

        stats['total_detections'] += template.detection_count

        stats['templates'][name] = {
            'enabled': template.enabled,
            'detections': template.detection_count,
            'last_detection': template.last_detection_time,
            'confidence': template.confidence,
            'auto_click': template.auto_click
        }

    return stats

def reset_template_statistics():
    """Reset detection statistics for all templates."""
    for template in _custom_templates.values():
        template.detection_count = 0
        template.last_detection_time = 0

    DEFAULT_SETTINGS['last_detection_time'] = 0
    logger.info("Template statistics reset")

def export_templates_config(export_path):
    """Export templates configuration to a file."""
    try:
        templates_data = {
            'settings': DEFAULT_SETTINGS.copy(),
            'templates': {
                name: template.to_dict()
                for name, template in _custom_templates.items()
            },
            'export_date': datetime.now().isoformat(),
            'version': '1.0'
        }

        with open(export_path, 'w') as f:
            json.dump(templates_data, f, indent=2)

        logger.info(f"Exported templates configuration to {export_path}")
        return True
    except Exception as e:
        logger.error(f"Error exporting templates configuration: {e}")
        return False

def import_templates_config(import_path):
    """Import templates configuration from a file."""
    try:
        with open(import_path, 'r') as f:
            data = json.load(f)

        # Import settings
        if 'settings' in data:
            for key, value in data['settings'].items():
                if key in DEFAULT_SETTINGS:
                    DEFAULT_SETTINGS[key] = value

        # Import templates
        if 'templates' in data:
            global _custom_templates
            imported_count = 0

            for name, template_data in data['templates'].items():
                try:
                    template = CustomTemplate.from_dict(template_data)
                    # Verify template image exists
                    if os.path.exists(template.image_path):
                        _custom_templates[name] = template
                        imported_count += 1
                    else:
                        logger.warning(f"Template image not found during import: {template.image_path}")
                except Exception as e:
                    logger.error(f"Error importing template {name}: {e}")

            save_templates()
            logger.info(f"Imported {imported_count} templates from {import_path}")
            return True

        return False
    except Exception as e:
        logger.error(f"Error importing templates configuration: {e}")
        return False

def capture_template_from_screen(name, x1, y1, x2, y2, **kwargs):
    """Capture a template from screen coordinates."""
    try:
        if not HAVE_PYAUTOGUI:
            logger.error("PyAutoGUI not available for screen capture")
            return None

        # Ensure coordinates are in correct order
        left = min(x1, x2)
        top = min(y1, y2)
        right = max(x1, x2)
        bottom = max(y1, y2)

        width = right - left
        height = bottom - top

        if width < 10 or height < 10:
            logger.error("Capture area too small")
            return None

        # Take screenshot of the specified region
        screenshot = pyautogui.screenshot(region=(left, top, width, height))

        # Create templates directory if it doesn't exist
        templates_dir = DEFAULT_SETTINGS['templates_dir']
        os.makedirs(templates_dir, exist_ok=True)

        # Generate unique filename
        timestamp = int(time.time())
        filename = f"{name}_{timestamp}.png"
        image_path = os.path.join(templates_dir, filename)

        # Save the captured image
        screenshot.save(image_path)

        # Create template
        template = CustomTemplate(name, image_path, **kwargs)

        # Add to templates
        add_custom_template(template)

        logger.info(f"Captured template '{name}' from screen region ({left}, {top}, {right}, {bottom})")
        return template

    except Exception as e:
        logger.error(f"Error capturing template from screen: {e}")
        return None

def test_template_detection_once(template_name):
    """Test template detection once (for hotkey testing)."""
    try:
        template = get_custom_template(template_name)
        if not template:
            logger.warning(f"Template '{template_name}' not found for testing")
            return False

        # Temporarily disable auto-click for testing
        original_auto_click = template.auto_click
        template.auto_click = False

        try:
            result = detect_template(template)
            if result:
                logger.info(f"✅ Template '{template_name}' detected successfully!")
                return True
            else:
                logger.info(f"❌ Template '{template_name}' not detected")
                return False
        finally:
            # Restore original auto-click setting
            template.auto_click = original_auto_click

    except Exception as e:
        logger.error(f"Error testing template '{template_name}': {e}")
        return False

def test_all_templates_once():
    """Test all enabled templates once (for hotkey testing)."""
    try:
        templates = get_all_custom_templates()
        enabled_templates = [t for t in templates.values() if t.enabled]

        if not enabled_templates:
            logger.info("No enabled templates to test")
            return False

        results = {}
        for template in enabled_templates:
            result = test_template_detection_once(template.name)
            results[template.name] = result

        # Log summary
        detected_count = sum(results.values())
        total_count = len(results)
        logger.info(f"Template test complete: {detected_count}/{total_count} templates detected")

        return detected_count > 0

    except Exception as e:
        logger.error(f"Error testing all templates: {e}")
        return False

# Initialize templates on module load
load_templates()
