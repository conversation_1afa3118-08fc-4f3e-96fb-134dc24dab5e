# Administrator Setup Guide

## Why Administrator Privileges Are Required

The Rappelz Macro Recorder requires administrator privileges to function properly because:

1. **Game Input Protection**: Many games, including Rappelz, implement security measures that block input from non-administrator processes to prevent cheating and automation.

2. **Low-Level Input Access**: The application uses Windows API functions for direct input injection, which requires elevated privileges.

3. **System Hook Registration**: Global keyboard and mouse hooks require administrator access to monitor system-wide events.

4. **Process Interaction**: Targeting specific game windows and sending input to them requires elevated permissions.

## Automatic Administrator Elevation

The application now includes automatic administrator elevation features:

### 1. Built-in Elevation Check
When you run the application, it automatically:
- Checks if running with administrator privileges
- Shows a dialog asking if you want to restart as administrator
- Automatically restarts with elevated privileges if you choose "Yes"

### 2. Multiple Entry Points
All entry points now check for administrator privileges:

**Primary Entry Point:**
```bash
python main.py
```

**Legacy Entry Point:**
```bash
python game_macro_recorder.py
```

Both will show the administrator elevation dialog if needed.

## Running Methods

### Method 1: Automatic Elevation (Recommended)
Simply run the application normally:
```bash
python main.py
```

The application will automatically prompt for administrator privileges and restart if needed.

### Method 2: Batch File Launcher
Use the provided batch file that ensures administrator privileges:
```bash
run_macro_recorder.bat
```

This batch file:
- Checks if already running as administrator
- Requests elevation if needed
- Installs required dependencies
- Launches the application

### Method 3: PowerShell Script
Use the PowerShell script for advanced control:
```powershell
powershell -ExecutionPolicy Bypass -File run_as_admin.ps1
```

Features:
- Automatic administrator elevation
- Dependency installation
- Error handling and fallbacks
- Detailed status reporting

### Method 4: Manual Administrator Launch
Right-click on Command Prompt or PowerShell and select "Run as administrator", then:
```bash
cd "path\to\macro\recorder"
python main.py
```

## User Experience

### First Run Experience
1. User runs `python main.py`
2. Application detects non-administrator status
3. Shows dialog: "This application requires administrator privileges..."
4. User clicks "Yes" to restart as administrator
5. Windows UAC prompt appears
6. User clicks "Yes" in UAC prompt
7. Application restarts with administrator privileges
8. Application runs normally

### Subsequent Runs
If the user always runs from the same shortcut or method, the elevation process becomes familiar and quick.

## Security Considerations

### Why UAC Prompts Appear
- Windows User Account Control (UAC) protects against unauthorized privilege escalation
- The prompt ensures the user consciously grants administrator access
- This is normal and expected behavior for applications requiring elevated privileges

### What the Application Does With Admin Rights
The application uses administrator privileges only for:
- Registering global keyboard/mouse hooks
- Sending input to game windows
- Accessing Windows API functions for input injection
- No file system modifications outside the application directory
- No registry modifications
- No network access requiring elevated privileges

## Troubleshooting

### "Access Denied" Errors
**Problem**: Application shows access denied when trying to send input to games.
**Solution**: Ensure the application is running as administrator.

### UAC Prompt Doesn't Appear
**Problem**: No UAC prompt when trying to elevate.
**Solution**: 
- Check UAC settings in Windows
- Try running from an administrator command prompt
- Use the batch file launcher

### Game Still Doesn't Receive Input
**Problem**: Application runs as admin but game doesn't respond to input.
**Solution**:
- Ensure the game is also running as administrator
- Check if the game has additional anti-cheat protection
- Verify the game window title in settings

### Elevation Fails
**Problem**: Application fails to restart as administrator.
**Solution**:
- Manually run as administrator
- Check Windows UAC settings
- Ensure user account has administrator privileges

## Advanced Configuration

### Bypassing Elevation Prompt
For advanced users who want to skip the elevation dialog:

1. Edit `main.py`
2. Replace `check_admin_and_elevate()` with `ensure_admin()`
3. The application will require administrator privileges and exit if not available

### Custom Elevation Behavior
You can modify the admin check behavior by editing `admin_check.py`:

- `check_admin_warning()`: Shows warning but continues
- `check_admin_and_elevate()`: Offers to restart as admin
- `ensure_admin()`: Requires admin privileges or exits

## Best Practices

### For Regular Users
1. Use the batch file launcher (`run_macro_recorder.bat`) for easiest setup
2. Always click "Yes" when prompted for administrator privileges
3. Create a desktop shortcut to the batch file for convenience

### For Advanced Users
1. Use the PowerShell script for better control and error reporting
2. Consider creating a scheduled task that runs with highest privileges
3. Use Group Policy to manage UAC behavior if in a corporate environment

### For Developers
1. Test both elevated and non-elevated scenarios
2. Handle elevation failures gracefully
3. Provide clear error messages for privilege-related issues

## Creating Desktop Shortcuts

### For Batch File
1. Right-click on `run_macro_recorder.bat`
2. Select "Create shortcut"
3. Move shortcut to desktop
4. Right-click shortcut → Properties
5. In "Shortcut" tab, click "Advanced..."
6. Check "Run as administrator"
7. Click OK

### For Python Script
1. Create new shortcut on desktop
2. Target: `C:\Windows\System32\cmd.exe /c "cd /d "C:\path\to\your\script" && python main.py"`
3. Right-click shortcut → Properties
4. In "Shortcut" tab, click "Advanced..."
5. Check "Run as administrator"
6. Click OK

## Conclusion

The enhanced administrator privilege handling ensures that the Rappelz Macro Recorder works reliably with games while providing a smooth user experience. The automatic elevation features make it easy for users to run the application with the necessary privileges without manual intervention.
