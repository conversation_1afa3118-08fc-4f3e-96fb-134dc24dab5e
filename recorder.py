"""
Recorder Module - Handles the core functionality for recording and replaying mouse/keyboard actions
"""
from imports import *
from config import game_window_settings
from win_api import *

# Global variables for recording state
# game_window_handle is now in win_api.py

def send_key_direct(key):
    """Send a key press directly to the game window or globally."""
    from win_api import game_window_handle

    # If targeting only the game window, send directly to it
    if game_window_settings['target_game_only']:
        # Find the game window if needed
        if not game_window_handle:
            if not find_game_window():
                logger.warning(f"Game window not found, can't send key: {key}")
                return

        # Check if the game window is active
        try:
            foreground_window = win32gui.GetForegroundWindow()
            if foreground_window != game_window_handle:
                # Game window is not active, activate it first
                win32gui.SetForegroundWindow(game_window_handle)
                time.sleep(0.2)  # Give time for window to come to foreground

                # Double-check that the window is now active
                foreground_window = win32gui.GetForegroundWindow()
                if foreground_window != game_window_handle:
                    logger.warning(f"Failed to activate game window, can't send key: {key}")
                    return
        except Exception as e:
            logger.error(f"Error activating game window: {e}")
            return

        # Use PostMessage to send the key directly to the game window
        # Convert key to virtual key code
        if isinstance(key, str):
            # Convert string key to virtual key code
            if len(key) == 1:
                vk_code = VK_CODES.get(key.lower(), ord(key.upper()))
            else:
                vk_code = VK_CODES.get(key.lower(), 0)
        else:
            vk_code = key

        try:
            # Send key down message directly to the game window
            win32gui.PostMessage(game_window_handle, win32con.WM_KEYDOWN, vk_code, 0)
            time.sleep(0.05)  # Small delay between down and up
            # Send key up message directly to the game window
            win32gui.PostMessage(game_window_handle, win32con.WM_KEYUP, vk_code, 0)
            logger.info(f"Sent key directly to game window: {key} (VK: {vk_code})")
            return
        except Exception as e:
            logger.error(f"Error sending key to game window: {e}")
            return

    # If not targeting only the game window, use our improved direct input module
    try:
        direct_input.press_key(key, 0.05)
        logger.info(f"Sent key using direct_input: {key}")
    except Exception as e:
        logger.error(f"Error sending key with direct_input: {e}")
        # Fall back to other methods if direct input fails
        try:
            if HAVE_PYAUTOGUI:
                pyautogui.press(key)
                logger.info(f"Sent key using PyAutoGUI: {key}")
            elif HAVE_KEYBOARD:
                keyboard.press_and_release(key)
                logger.info(f"Sent key using Keyboard module: {key}")
        except Exception as fallback_error:
            logger.error(f"Fallback methods also failed: {fallback_error}")

def send_mouse_click_direct(button='left', x=None, y=None):
    """Send a mouse click directly to the game window or globally."""
    from win_api import game_window_handle

    # If targeting only the game window, send directly to it
    if game_window_settings['target_game_only']:
        # Find the game window if needed
        if not game_window_handle:
            if not find_game_window():
                logger.warning(f"Game window not found, can't send mouse click: {button}")
                return

        # Check if the game window is active
        try:
            foreground_window = win32gui.GetForegroundWindow()
            if foreground_window != game_window_handle:
                # Game window is not active, activate it first
                win32gui.SetForegroundWindow(game_window_handle)
                time.sleep(0.2)  # Give time for window to come to foreground

                # Double-check that the window is now active
                foreground_window = win32gui.GetForegroundWindow()
                if foreground_window != game_window_handle:
                    logger.warning(f"Failed to activate game window, can't send mouse click: {button}")
                    return
        except Exception as e:
            logger.error(f"Error activating game window: {e}")
            return

        # If we have coordinates, make sure they're within the game window
        if x is not None and y is not None:
            window_rect = get_game_window_rect()
            if window_rect:
                # Check if the coordinates are within the game window
                if (x < window_rect['x'] or x > window_rect['x'] + window_rect['width'] or
                    y < window_rect['y'] or y > window_rect['y'] + window_rect['height']):
                    logger.warning(f"Coordinates ({x}, {y}) are outside the game window, skipping click")
                    return

                # Convert screen coordinates to client coordinates
                client_x = x - window_rect['x']
                client_y = y - window_rect['y']

                # Send mouse messages directly to the game window
                try:
                    # Move cursor to position
                    win32gui.PostMessage(game_window_handle, win32con.WM_MOUSEMOVE, 0,
                                        win32api.MAKELONG(client_x, client_y))
                    time.sleep(0.05)

                    # Send mouse down message
                    if button.lower() == 'left':
                        win32gui.PostMessage(game_window_handle, win32con.WM_LBUTTONDOWN,
                                           win32con.MK_LBUTTON, win32api.MAKELONG(client_x, client_y))
                        time.sleep(0.05)
                        win32gui.PostMessage(game_window_handle, win32con.WM_LBUTTONUP,
                                           0, win32api.MAKELONG(client_x, client_y))
                    elif button.lower() == 'right':
                        win32gui.PostMessage(game_window_handle, win32con.WM_RBUTTONDOWN,
                                           win32con.MK_RBUTTON, win32api.MAKELONG(client_x, client_y))
                        time.sleep(0.05)
                        win32gui.PostMessage(game_window_handle, win32con.WM_RBUTTONUP,
                                           0, win32api.MAKELONG(client_x, client_y))
                    elif button.lower() == 'middle':
                        win32gui.PostMessage(game_window_handle, win32con.WM_MBUTTONDOWN,
                                           win32con.MK_MBUTTON, win32api.MAKELONG(client_x, client_y))
                        time.sleep(0.05)
                        win32gui.PostMessage(game_window_handle, win32con.WM_MBUTTONUP,
                                           0, win32api.MAKELONG(client_x, client_y))

                    logger.info(f"Sent {button} click directly to game window at ({x}, {y}) (client: {client_x}, {client_y})")
                    return
                except Exception as e:
                    logger.error(f"Error sending mouse click to game window: {e}")
                    return

    # If not targeting only the game window, use our improved direct input module
    try:
        if x is not None and y is not None:
            direct_input.mouse_click(button, x, y)
            logger.info(f"Sent {button} click using direct_input at ({x}, {y})")
        else:
            # Click at current position
            direct_input.mouse_click(button)
            logger.info(f"Sent {button} click using direct_input at current position")
    except Exception as e:
        logger.error(f"Error sending mouse click with direct_input: {e}")
        # Fall back to other methods if direct input fails
        try:
            if HAVE_PYAUTOGUI:
                if x is not None and y is not None:
                    pyautogui.click(x, y, button=button)
                    logger.info(f"Sent {button} click using PyAutoGUI at ({x}, {y})")
                else:
                    pyautogui.click(button=button)
                    logger.info(f"Sent {button} click using PyAutoGUI at current position")
        except Exception as fallback_error:
            logger.error(f"Fallback methods also failed: {fallback_error}")

# Global state variables that will be set by main module
_recorded_actions = []
_last_action_time = None
_running = False
_death_detection_active = False

def set_global_state(recorded_actions, last_action_time, running, death_detection_active):
    """Set global state variables from main module."""
    global _recorded_actions, _last_action_time, _running, _death_detection_active
    _recorded_actions = recorded_actions
    _last_action_time = last_action_time
    _running = running
    _death_detection_active = death_detection_active

def get_recorded_actions():
    """Get the recorded actions list."""
    return _recorded_actions

def get_last_action_time():
    """Get the last action time."""
    return _last_action_time

def set_last_action_time(time_val):
    """Set the last action time."""
    global _last_action_time
    _last_action_time = time_val

def is_running():
    """Check if playback is running."""
    return _running

def set_running(running):
    """Set the running state."""
    global _running
    _running = running

def is_death_detection_active():
    """Check if death detection is active."""
    return _death_detection_active

def set_death_detection_active(active):
    """Set the death detection active state."""
    global _death_detection_active
    _death_detection_active = active

def record_key_press(key):
    """Record a key press action."""
    try:
        # Validate input
        if not isinstance(key, str) or not key:
            logger.warning(f"Invalid key: {key}")
            return False

        # Calculate delay
        current_time = time.time()

        if _last_action_time is not None:
            delay = current_time - _last_action_time
        else:
            delay = 0

        # Create action record
        action = {
            'type': 'key',
            'key': key,
            'delay': delay
        }

        # Add to recorded actions
        _recorded_actions.append(action)

        # Update last action time
        set_last_action_time(current_time)

        # Log the action
        logger.info(f"Recorded key press: {key} (delay: {delay:.2f}s)")

        return True
    except Exception as e:
        logger.error(f"Error recording key press: {e}")
        return False

def record_mouse_click(button, x, y):
    """Record a mouse click action."""
    try:
        # Calculate delay
        current_time = time.time()

        if _last_action_time is not None:
            delay = current_time - _last_action_time
        else:
            delay = 0

        # Create action record
        action = {
            'type': 'mouse',
            'button': button,
            'x': x,
            'y': y,
            'delay': delay
        }

        # Add to recorded actions
        _recorded_actions.append(action)

        # Update last action time
        set_last_action_time(current_time)

        # Log the action
        logger.info(f"Recorded {button} click at ({x}, {y}) (delay: {delay:.2f}s)")

        return True
    except Exception as e:
        logger.error(f"Error recording mouse click: {e}")
        return False

# get_game_window_rect function has been moved to win_api.py

def playback_actions(repeat_count=1, repeat_delay=0, infinite=False):
    """Play back recorded actions."""
    if not _recorded_actions:
        print("No actions recorded")
        return

    # If targeting only the game window, find it first
    if game_window_settings['target_game_only']:
        from win_api import find_game_window, game_window_handle
        if not find_game_window():
            print("Game window not found, can't start playback")
            return

        # Activate the game window
        try:
            win32gui.SetForegroundWindow(game_window_handle)
            time.sleep(0.2)  # Give time for window to come to foreground
        except Exception as e:
            print(f"Error activating game window: {e}")

    set_running(True)
    iteration = 0

    # Main playback loop
    while is_running():
        # Check if we've reached the repeat count (unless infinite)
        if not infinite and iteration >= repeat_count:
            break

        # Increment iteration counter
        iteration += 1

        # Display iteration information
        if infinite:
            print(f"Playback iteration: {iteration} (infinite mode)")
        else:
            print(f"Playback iteration: {iteration} of {repeat_count}")

        # Play each action in the sequence
        for action in _recorded_actions:
            if not is_running():
                break

            # Wait for the specified delay
            time.sleep(action['delay'])

            # Perform the action
            if action['type'] == 'key':
                # Send key press using our improved function
                send_key_direct(action['key'])

            elif action['type'] == 'mouse':
                # Get the exact coordinates from the recorded action
                x = action.get('x')
                y = action.get('y')
                button = action.get('button', 'left')

                # Send mouse click using our improved function
                send_mouse_click_direct(button, x, y)

        # If not infinite and we have more iterations, wait for repeat delay
        if not infinite and iteration < repeat_count and repeat_delay > 0:
            time.sleep(repeat_delay)
        elif infinite and repeat_delay > 0:
            time.sleep(repeat_delay)

    print("Playback completed")

# Death detection functions have been moved to death_detection.py module
