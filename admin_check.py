"""
Admin Check Module - Check if running as administrator and handle elevation
"""
import ctypes
import sys
import os
import tkinter as tk
from tkinter import messagebox

def is_admin():
    """Check if the script is running as administrator."""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """Restart the current script as administrator."""
    try:
        if is_admin():
            return True
        else:
            # Get the current script path
            script_path = sys.argv[0]
            if not os.path.isabs(script_path):
                script_path = os.path.abspath(script_path)

            # Parameters to pass to the elevated process
            params = ' '.join(sys.argv[1:])

            # Use ShellExecute to run as admin
            ctypes.windll.shell32.ShellExecuteW(
                None,
                "runas",
                sys.executable,
                f'"{script_path}" {params}',
                None,
                1
            )
            return False  # Current process should exit
    except Exception as e:
        print(f"Failed to restart as administrator: {e}")
        return False

def check_admin_and_elevate():
    """Check admin status and offer to restart as admin if needed."""
    if not is_admin():
        # Create a simple dialog to ask user if they want to restart as admin
        root = tk.Tk()
        root.withdraw()  # Hide the main window

        result = messagebox.askyesno(
            "Administrator Required",
            "This application requires administrator privileges to function properly with games.\n\n"
            "Some games block inputs from non-administrator processes.\n\n"
            "Would you like to restart as administrator?",
            icon='warning'
        )

        root.destroy()

        if result:
            print("Restarting as administrator...")
            if run_as_admin():
                return True  # Already admin
            else:
                sys.exit(0)  # Exit current process, admin version will start
        else:
            print("Continuing without administrator privileges...")
            print("Note: Some games may block inputs from this application.")
            return False
    else:
        print("Running with administrator privileges.")
        return True

def check_admin_warning():
    """Show a warning if not running as administrator (legacy function)."""
    if not is_admin():
        try:
            root = tk.Tk()
            root.withdraw()  # Hide the main window

            messagebox.showwarning(
                "Warning",
                "This script is not running as administrator.\n\n"
                "Some games may block inputs from non-admin processes.\n\n"
                "For best results, please run the script as administrator."
            )

            root.destroy()
        except:
            # Fallback to console message if GUI is not available
            print("WARNING: Not running as administrator. Some games may block inputs.")

def ensure_admin():
    """Ensure the application is running as administrator, exit if user declines."""
    if not is_admin():
        try:
            root = tk.Tk()
            root.withdraw()  # Hide the main window

            result = messagebox.askyesno(
                "Administrator Required",
                "This application requires administrator privileges to function properly.\n\n"
                "Click 'Yes' to restart as administrator, or 'No' to exit.",
                icon='warning'
            )

            root.destroy()

            if result:
                print("Restarting as administrator...")
                if not run_as_admin():
                    sys.exit(0)  # Exit current process
            else:
                print("Administrator privileges required. Exiting...")
                sys.exit(1)
        except:
            # Fallback for console mode
            print("ERROR: Administrator privileges required.")
            print("Please run this application as administrator.")
            sys.exit(1)
    else:
        print("✓ Running with administrator privileges.")
