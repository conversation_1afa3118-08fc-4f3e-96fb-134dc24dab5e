"""
Sc<PERSON>t to fix recording manager references
"""

def fix_recording_manager():
    """Fix all recorded_actions, is_recording, is_paused references."""
    
    # Read the file
    with open('recording_manager.py', 'r') as f:
        content = f.read()
    
    # Replace all instances
    replacements = [
        ('recorded_actions', 'self.recorded_actions'),
        ('is_recording', 'self.is_recording'),
        ('is_paused', 'self.is_paused'),
    ]
    
    for old, new in replacements:
        # Only replace standalone instances, not those already with self.
        import re
        # Replace word boundaries to avoid replacing parts of other words
        pattern = r'\b' + re.escape(old) + r'\b'
        # Don't replace if already preceded by 'self.'
        content = re.sub(r'(?<!self\.)' + pattern, new, content)
    
    # Write back
    with open('recording_manager.py', 'w') as f:
        f.write(content)
    
    print("Fixed recording manager references")

if __name__ == "__main__":
    fix_recording_manager()
