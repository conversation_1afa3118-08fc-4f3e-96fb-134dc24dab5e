#!/usr/bin/env python3
"""
Test script to verify recording and playback functionality works correctly.
"""

import sys
import time
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_recording_playback():
    """Test that recording and playback work correctly."""
    print("=" * 60)
    print("Recording and Playback Test")
    print("=" * 60)
    
    try:
        # Import required modules
        print("1. Testing imports...")
        from recorder import (
            set_global_state, get_recorded_actions, 
            record_key_press, record_mouse_click,
            playback_actions
        )
        print("   ✅ Recorder imports OK")
        
        from recording_manager import RecordingManager
        print("   ✅ Recording manager import OK")
        
        # Create a mock main GUI
        class MockMainGUI:
            def __init__(self):
                self.recording = False
                self.paused = False
        
        # Initialize recorder state
        print("\n2. Initializing recorder state...")
        set_global_state([], None, False, False)
        initial_actions = get_recorded_actions()
        print(f"   ✅ Initial actions count: {len(initial_actions)}")
        
        # Create recording manager
        print("\n3. Creating recording manager...")
        mock_gui = MockMainGUI()
        recording_manager = RecordingManager(mock_gui)
        print("   ✅ Recording manager created")
        
        # Test recording some actions
        print("\n4. Testing action recording...")
        
        # Record a key press
        success = record_key_press('a')
        print(f"   Key press recorded: {success}")
        
        # Record a mouse click
        success = record_mouse_click('left', 100, 200)
        print(f"   Mouse click recorded: {success}")
        
        # Record another key press
        time.sleep(0.1)  # Small delay
        success = record_key_press('b')
        print(f"   Second key press recorded: {success}")
        
        # Check recorded actions
        actions = get_recorded_actions()
        print(f"   ✅ Total actions recorded: {len(actions)}")
        
        if len(actions) > 0:
            print("   📋 Recorded actions:")
            for i, action in enumerate(actions):
                print(f"      {i+1}. {action}")
        
        # Test recording manager access
        print("\n5. Testing recording manager access...")
        manager_actions = recording_manager.recorded_actions
        print(f"   ✅ Recording manager sees {len(manager_actions)} actions")
        
        # Verify they're the same
        if len(actions) == len(manager_actions):
            print("   ✅ Action counts match!")
        else:
            print(f"   ❌ Action count mismatch: recorder={len(actions)}, manager={len(manager_actions)}")
            return False
        
        # Test playback check
        print("\n6. Testing playback readiness...")
        if actions:
            print("   ✅ Actions available for playback")
            print("   📝 Playback would work (not actually running playback in test)")
        else:
            print("   ❌ No actions available for playback")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 All recording and playback tests passed!")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_recording_playback()
    sys.exit(0 if success else 1)
