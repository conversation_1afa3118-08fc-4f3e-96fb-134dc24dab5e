"""
Test script for recording manager functionality
"""

def test_recording_manager():
    """Test if recording manager can be imported and initialized."""
    print("Testing recording manager...")
    
    try:
        # Test imports
        print("Testing imports...")
        from recorder import get_recorded_actions, is_recording, is_paused
        print("✅ Recorder imports OK")
        
        from ui_helpers import create_styled_button, show_info_message
        print("✅ UI helpers imports OK")
        
        from config import save_actions, load_actions
        print("✅ Config imports OK")
        
        # Test recording manager import
        from recording_manager import RecordingManager
        print("✅ Recording manager import OK")
        
        # Test basic functionality
        print("\nTesting basic functionality...")
        
        # Create a mock main GUI object
        class MockMainGUI:
            def __init__(self):
                self.status_var = None
                self.colors = {
                    'success': '#27ae60',
                    'danger': '#e74c3c',
                    'warning': '#f39c12',
                    'accent': '#3498db'
                }
        
        mock_gui = MockMainGUI()
        
        # Initialize recording manager
        recording_manager = RecordingManager(mock_gui)
        print("✅ Recording manager initialization OK")
        
        # Test property access
        actions = recording_manager.recorded_actions
        print(f"✅ Recorded actions property access OK (found {len(actions)} actions)")
        
        # Test statistics
        stats = recording_manager.get_recording_stats()
        print(f"✅ Recording stats OK: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Recording manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run recording manager tests."""
    print("=" * 60)
    print("Recording Manager Test Suite")
    print("=" * 60)
    
    success = test_recording_manager()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Recording manager tests passed!")
    else:
        print("❌ Recording manager tests failed!")
    print("=" * 60)

if __name__ == "__main__":
    main()
