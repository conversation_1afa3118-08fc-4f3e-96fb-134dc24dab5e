"""
Windows API Module - Contains Windows API structures, constants, and related setup
"""
import ctypes
import win32gui
from imports import logger
from config import game_window_settings

# Global variables
game_window_handle = None

# Load user32.dll for direct Windows API calls
user32 = ctypes.WinDLL('user32', use_last_error=True)

# Constants for SendInput
INPUT_KEYBOARD = 1
INPUT_MOUSE = 0
KEYEVENTF_KEYUP = 0x0002
KEYEVENTF_EXTENDEDKEY = 0x0001

# Define structures for SendInput
class MOUSEINPUT(ctypes.Structure):
    _fields_ = (("dx", ctypes.c_long),
                ("dy", ctypes.c_long),
                ("mouseData", ctypes.c_ulong),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong)))

class KEYBDINPUT(ctypes.Structure):
    _fields_ = (("wVk", ctypes.c_ushort),
                ("wScan", ctypes.c_ushort),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong)))

class HARDWAREINPUT(ctypes.Structure):
    _fields_ = (("uMsg", ctypes.c_ulong),
                ("wParamL", ctypes.c_short),
                ("wParamH", ctypes.c_ushort))

class INPUT_UNION(ctypes.Union):
    _fields_ = (("mi", MOUSEINPUT),
                ("ki", KEYBDINPUT),
                ("hi", HARDWAREINPUT))

class INPUT(ctypes.Structure):
    _fields_ = (("type", ctypes.c_ulong),
                ("union", INPUT_UNION))

def find_game_window():
    """Find the game window by title."""
    global game_window_handle

    try:
        # Find the window by title
        window_title = game_window_settings['window_title']
        game_window_handle = win32gui.FindWindow(None, window_title)

        if game_window_handle:
            logger.info(f"Found game window: {window_title}")
            return True
        else:
            logger.warning(f"Game window not found: {window_title}")
            return False
    except Exception as e:
        logger.error(f"Error finding game window: {e}")
        return False

def get_game_window_rect():
    """Get the game window rectangle."""
    global game_window_handle

    if not game_window_handle:
        if not find_game_window():
            return None

    try:
        # Get window rect
        rect = win32gui.GetWindowRect(game_window_handle)
        x1, y1, x2, y2 = rect
        width = x2 - x1
        height = y2 - y1

        return {
            'x': x1,
            'y': y1,
            'width': width,
            'height': height,
            'center_x': x1 + width // 2,
            'center_y': y1 + height // 2
        }
    except Exception as e:
        logger.error(f"Error getting game window rect: {e}")
        return None
