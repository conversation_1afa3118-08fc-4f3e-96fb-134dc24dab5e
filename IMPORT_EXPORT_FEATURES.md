# Import/Export Features - Complete Action Management

## Overview

The Macro Recorder now includes comprehensive import and export functionality, allowing users to save, share, backup, and restore their recorded actions. This system supports multiple formats and provides flexible options for managing macro collections.

## New Features Added

### 📤 **Export Functionality**

#### **1. Smart Export Dialog**
- **Format selection**: Choose between JSON (importable) or Text (readable)
- **User guidance**: Clear explanations of each format's purpose
- **Intelligent defaults**: JSON format recommended for sharing/backup

#### **2. JSON Export (Recommended)**
- **Complete metadata**: Export date, version, action count, application info
- **Importable format**: Can be loaded back into the application
- **Structured data**: Clean, organized JSON format
- **Cross-platform**: Works on any system with the application

#### **3. Text Export (Human Readable)**
- **Documentation format**: Perfect for reviewing macro sequences
- **Detailed information**: Both formatted descriptions and raw data
- **Timestamp information**: Export date and time included
- **Analysis friendly**: Easy to read and understand

### 📥 **Import Functionality**

#### **1. Flexible Import Options**
- **Multiple formats**: Supports both new and legacy JSON formats
- **Smart detection**: Automatically detects file format structure
- **Error handling**: Clear error messages for invalid files
- **Validation**: Ensures imported data is valid

#### **2. Merge Options**
- **Replace existing**: Clear current actions and load imported ones
- **Append to existing**: Add imported actions to current sequence
- **Cancel option**: Abort import if needed
- **Clear confirmation**: Shows action counts before proceeding

#### **3. Compatibility**
- **New format**: Full metadata with actions array
- **Legacy format**: Direct actions list (backward compatible)
- **Error recovery**: Graceful handling of format issues

### 💾 **Macro Management**

#### **1. Save as Macro**
- **Named saving**: Save with custom filename
- **Standard format**: Uses application's native macro format
- **Integration**: Works with existing load/save system
- **File dialog**: Standard save interface

#### **2. Load Macro**
- **File browser**: Standard open dialog
- **Merge options**: Same replace/append choices as import
- **Validation**: Ensures macro file is valid
- **Error handling**: Clear feedback on load issues

#### **3. Quick Save**
- **Auto-naming**: Timestamp-based filenames
- **Auto-directory**: Creates 'saved_macros' folder automatically
- **Instant saving**: No dialog, immediate save
- **Confirmation**: Shows save location and details

## User Interface Integration

### **Recording Tab Layout**
```
[Edit Action] [Delete Action] [Duplicate Action] [Insert Delay]
[Move Up]     [Move Down]     [Import Actions] [Export Actions]
[Clear All]   [Save as Macro] [Load Macro]     [Quick Save]
```

### **Context Menu Options**
- Edit Action
- Delete Action
- Duplicate Action
- Move Up / Move Down
- Insert Delay Before
- ─────────────────
- Import Actions
- Export Actions
- ─────────────────
- Save as Macro
- Load Macro
- ─────────────────
- Clear All Actions

### **Button Color Coding**
- **Blue (Info)**: Import, Export, Load Macro
- **Green (Success)**: Quick Save, Import Actions
- **Orange (Warning)**: Save as Macro
- **Red (Danger)**: Delete, Clear All

## File Formats

### **JSON Export Format**
```json
{
  "metadata": {
    "export_date": "2024-01-15T14:30:00.123456",
    "version": "1.0",
    "total_actions": 5,
    "application": "Rappelz Macro Recorder"
  },
  "actions": [
    {
      "type": "key",
      "key": "space",
      "timestamp": 1.234
    },
    {
      "type": "click",
      "button": "left",
      "x": 100,
      "y": 200,
      "timestamp": 2.456
    },
    {
      "type": "delay",
      "duration": 1.5,
      "timestamp": 3.789
    }
  ]
}
```

### **Text Export Format**
```
Macro Actions Export - 3 actions
Export Date: 2024-01-15 14:30:00
============================================================

Action 1: Key Press 'space' at 1.234s
  Raw data: {'type': 'key', 'key': 'space', 'timestamp': 1.234}

Action 2: Left Click at (100, 200) at 2.456s
  Raw data: {'type': 'click', 'button': 'left', 'x': 100, 'y': 200, 'timestamp': 2.456}

Action 3: Delay 1.500 seconds
  Raw data: {'type': 'delay', 'duration': 1.5, 'timestamp': 3.789}
```

## Use Cases and Workflows

### **Macro Sharing**
1. **Export to JSON**: Use Export Actions → JSON Format
2. **Share file**: Send .json file to other users
3. **Import on target**: Use Import Actions to load
4. **Result**: Exact macro reproduction on different systems

### **Backup and Restore**
1. **Regular backups**: Use Quick Save for automatic timestamped backups
2. **Named backups**: Use Save as Macro for important sequences
3. **Restore**: Use Load Macro or Import Actions to restore
4. **Verification**: Use Text Export to document what was backed up

### **Macro Development**
1. **Version control**: Export different versions as development progresses
2. **Documentation**: Use Text Export to document macro functionality
3. **Testing**: Import test sequences for validation
4. **Collaboration**: Share JSON exports with team members

### **Macro Libraries**
1. **Collection building**: Save commonly used sequences
2. **Organization**: Use descriptive filenames for easy identification
3. **Combination**: Import multiple macros and combine them
4. **Distribution**: Share macro libraries as JSON collections

## Error Handling and Validation

### **Import Validation**
- **File format check**: Ensures valid JSON structure
- **Action validation**: Verifies each action has required fields
- **Type checking**: Confirms action types are supported
- **Error reporting**: Clear messages for specific issues

### **Export Safety**
- **File permissions**: Checks write access before attempting save
- **Disk space**: Handles insufficient disk space gracefully
- **Path validation**: Ensures save location is valid
- **Overwrite confirmation**: Asks before overwriting existing files

### **Common Error Messages**
- "Invalid JSON file: Expecting property name"
- "No actions found in the file"
- "Invalid file format. Expected JSON with actions list"
- "Failed to export actions: Permission denied"

## Performance Considerations

### **Large Action Lists**
- **Efficient processing**: Handles thousands of actions smoothly
- **Memory management**: Minimal memory overhead during import/export
- **Progress feedback**: Status updates for large operations
- **Streaming**: Large files processed efficiently

### **File Size Optimization**
- **Compact JSON**: Minimal formatting for smaller files
- **Metadata efficiency**: Only essential metadata included
- **Compression ready**: JSON format compresses well if needed

## Security and Privacy

### **Data Safety**
- **No external connections**: All operations are local
- **No data collection**: No telemetry or usage tracking
- **User control**: Complete control over where files are saved
- **Privacy protection**: No sensitive data in exports

### **File Security**
- **Standard formats**: Uses widely-supported JSON format
- **No encryption**: Files are human-readable for transparency
- **Virus scanning**: Compatible with standard antivirus tools
- **Backup friendly**: Works with standard backup solutions

## Future Enhancements

### **Planned Features**
- **Batch operations**: Import/export multiple files at once
- **Macro templates**: Pre-built macro templates for common tasks
- **Cloud integration**: Optional cloud storage integration
- **Compression**: Built-in compression for large macro files

### **Advanced Features**
- **Macro merging**: Intelligent combination of multiple macros
- **Conflict resolution**: Handle overlapping actions when merging
- **Version tracking**: Track changes between macro versions
- **Diff viewing**: Compare different versions of macros

## Best Practices

### **File Organization**
1. **Use descriptive names**: "farming_sequence_v2.json" vs "macro1.json"
2. **Date stamping**: Include dates for version tracking
3. **Folder structure**: Organize by game, character, or purpose
4. **Regular backups**: Use Quick Save frequently during development

### **Sharing Macros**
1. **Test before sharing**: Verify macros work on your system first
2. **Include documentation**: Use Text Export to document purpose
3. **Version information**: Note game version and requirements
4. **Clear instructions**: Explain how to use the macro

### **Development Workflow**
1. **Incremental saves**: Save frequently during development
2. **Version control**: Keep multiple versions during testing
3. **Documentation**: Export to text for review and analysis
4. **Testing**: Import and test on clean systems

The import/export system provides professional-grade macro management capabilities, making it easy to create, share, and maintain complex automation sequences with confidence and reliability.
