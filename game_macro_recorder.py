"""
Game Macro Recorder - Advanced Auto Clicker with Recording Capabilities
Records and replays sequences of keyboard and mouse actions

This file has been refactored into modular components.
Please use main.py as the entry point instead.
"""

import sys

# Check admin privileges first
try:
    from admin_check import check_admin_and_elevate
    check_admin_and_elevate()
except ImportError:
    print("Warning: Could not check administrator privileges.")

# Add a deprecation warning
print("WARNING: game_macro_recorder.py has been refactored into modular components.")
print("Please use main.py as the entry point instead.")
print("Redirecting to main.py...")

# Import and run main
try:
    import main
    if __name__ == "__main__":
        main.main()
except ImportError as e:
    print(f"Error importing main module: {e}")
    print("Please ensure all module files are present:")
    print("- main.py")
    print("- admin_check.py")
    print("- imports.py")
    print("- win_api.py")
    print("- recorder.py")
    print("- ui_helpers.py")
    print("- config.py")
    print("- death_detection.py")
    print("- minimalist_mode.py")
    sys.exit(1)
except Exception as e:
    print(f"Error running main: {e}")
    sys.exit(1)
