#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify all modules work correctly together
"""

import sys
import traceback

def test_module_imports():
    """Test that all modules can be imported successfully."""
    print("Testing module imports...")
    
    modules_to_test = [
        'admin_check',
        'imports',
        'win_api',
        'recorder',
        'ui_helpers',
        'config',
        'death_detection',
        'minimalist_mode'
    ]
    
    failed_imports = []
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name} imported successfully")
        except Exception as e:
            print(f"❌ {module_name} failed to import: {e}")
            failed_imports.append(module_name)
    
    return len(failed_imports) == 0

def test_basic_functionality():
    """Test basic functionality of key modules."""
    print("\nTesting basic functionality...")

    try:
        # Test admin_check
        from admin_check import is_admin, run_as_admin, check_admin_and_elevate
        admin_status = is_admin()
        print(f"✅ Admin check works: {'Admin' if admin_status else 'Standard User'}")
        print(f"✅ Admin functions available: run_as_admin, check_admin_and_elevate")
    except Exception as e:
        print(f"❌ Admin check failed: {e}")
        return False
    
    try:
        # Test imports module
        from imports import HAVE_PYAUTOGUI, HAVE_KEYBOARD, HAVE_MOUSE
        print(f"✅ Import flags work: PyAutoGUI={HAVE_PYAUTOGUI}, Keyboard={HAVE_KEYBOARD}, Mouse={HAVE_MOUSE}")
    except Exception as e:
        print(f"❌ Import flags failed: {e}")
        return False
    
    try:
        # Test config module
        from config import game_window_settings
        print(f"✅ Config loaded: Game targeting={game_window_settings['target_game_only']}")
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False

    try:
        # Test death detection module
        from death_detection import get_death_detection_settings, is_death_detection_active
        death_settings = get_death_detection_settings()
        death_active = is_death_detection_active()
        print(f"✅ Death detection module works: enabled={death_settings['enabled']}, active={death_active}")
    except Exception as e:
        print(f"❌ Death detection module failed: {e}")
        return False

    try:
        # Test minimalist mode module
        from minimalist_mode import MinimalistWindow
        print(f"✅ Minimalist mode module imported successfully")
    except Exception as e:
        print(f"❌ Minimalist mode module failed: {e}")
        return False
    
    try:
        # Test recorder state management
        from recorder import set_global_state, get_recorded_actions, is_running
        set_global_state([], None, False, False)
        actions = get_recorded_actions()
        running = is_running()
        print(f"✅ Recorder state management works: {len(actions)} actions, running={running}")
    except Exception as e:
        print(f"❌ Recorder state management failed: {e}")
        return False
    
    return True

def test_main_module():
    """Test that main module can be imported (but not run)."""
    print("\nTesting main module import...")
    
    try:
        # Import main but don't run it
        import main
        print("✅ Main module imported successfully")
        
        # Check if the GUI class exists
        if hasattr(main, 'MacroRecorderGUI'):
            print("✅ MacroRecorderGUI class found")
        else:
            print("❌ MacroRecorderGUI class not found")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Main module import failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("Game Macro Recorder - Module Test Suite")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test module imports
    if not test_module_imports():
        all_tests_passed = False
    
    # Test basic functionality
    if not test_basic_functionality():
        all_tests_passed = False
    
    # Test main module
    if not test_main_module():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 All tests passed! The refactored modules are working correctly.")
        print("\nYou can now run the application with:")
        print("  python main.py")
        print("\nOr use the legacy entry point:")
        print("  python game_macro_recorder.py")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
        print("\nCommon issues:")
        print("- Missing dependencies (install with: pip install -r requirements.txt)")
        print("- Python version compatibility")
        print("- File permissions")
    print("=" * 50)
    
    return 0 if all_tests_passed else 1

if __name__ == "__main__":
    sys.exit(main())
