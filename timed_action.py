"""
Timed Action Module

Allows users to schedule key presses at defined intervals and send them directly 
to the game window, even when the window is not focused (runs in the background).
"""

import os
import time
import threading
import json
from datetime import datetime
from imports import logger
# win_api imports handled by recorder module
from config import game_window_settings

# Default settings for timed actions
DEFAULT_SETTINGS = {
    'enabled': False,
    'actions_dir': os.path.join(os.path.dirname(os.path.abspath(__file__)), "timed_actions"),
    'max_concurrent_actions': 10,  # Maximum number of actions that can run simultaneously
    'default_interval': 5000,  # Default interval in milliseconds
    'min_interval': 100,  # Minimum interval in milliseconds (0.1 seconds)
    'max_interval': 3600000,  # Maximum interval in milliseconds (1 hour)
}

# Global state variables
_timed_actions = {}  # Dictionary to store timed action configurations
_active_threads = {}  # Dictionary to store active action threads

class TimedAction:
    """Represents a single timed action configuration."""
    
    def __init__(self, name, keys, interval_ms, enabled=True, description=""):
        self.name = name
        self.keys = keys if isinstance(keys, list) else [keys]  # Ensure keys is a list
        self.interval_ms = max(DEFAULT_SETTINGS['min_interval'], min(interval_ms, DEFAULT_SETTINGS['max_interval']))
        self.enabled = enabled
        self.description = description
        self.created_time = time.time()
        self.last_execution_time = 0
        self.execution_count = 0
        self.is_running = False
        self.thread = None
        
    def to_dict(self):
        """Convert timed action to dictionary for serialization."""
        return {
            'name': self.name,
            'keys': self.keys,
            'interval_ms': self.interval_ms,
            'enabled': self.enabled,
            'description': self.description,
            'created_time': self.created_time,
            'last_execution_time': self.last_execution_time,
            'execution_count': self.execution_count
        }
    
    @classmethod
    def from_dict(cls, data):
        """Create timed action from dictionary."""
        action = cls(
            name=data['name'],
            keys=data['keys'],
            interval_ms=data['interval_ms'],
            enabled=data.get('enabled', True),
            description=data.get('description', '')
        )
        action.created_time = data.get('created_time', time.time())
        action.last_execution_time = data.get('last_execution_time', 0)
        action.execution_count = data.get('execution_count', 0)
        return action
    
    def get_interval_seconds(self):
        """Get interval in seconds."""
        return self.interval_ms / 1000.0
    
    def get_interval_display(self):
        """Get human-readable interval display."""
        if self.interval_ms < 1000:
            return f"{self.interval_ms}ms"
        elif self.interval_ms < 60000:
            return f"{self.interval_ms / 1000:.1f}s"
        else:
            return f"{self.interval_ms / 60000:.1f}m"

def get_timed_action_settings():
    """Get the timed action settings."""
    return DEFAULT_SETTINGS.copy()

def is_timed_actions_enabled():
    """Check if timed actions system is enabled."""
    return DEFAULT_SETTINGS['enabled']

def set_timed_actions_enabled(enabled):
    """Set the timed actions system enabled state."""
    DEFAULT_SETTINGS['enabled'] = enabled

def add_timed_action(action):
    """Add a timed action to the system."""
    global _timed_actions
    _timed_actions[action.name] = action
    logger.info(f"Added timed action: {action.name}")
    save_timed_actions()

def remove_timed_action(name):
    """Remove a timed action from the system."""
    global _timed_actions, _active_threads
    
    # Stop the action if it's running
    stop_timed_action(name)
    
    # Remove from actions
    if name in _timed_actions:
        del _timed_actions[name]
        logger.info(f"Removed timed action: {name}")
        save_timed_actions()
        return True
    return False

def get_timed_action(name):
    """Get a timed action by name."""
    return _timed_actions.get(name)

def get_all_timed_actions():
    """Get all timed actions."""
    return _timed_actions.copy()

def update_timed_action(name, **kwargs):
    """Update a timed action's properties."""
    if name in _timed_actions:
        action = _timed_actions[name]
        
        # If the action is running and we're changing critical properties, restart it
        restart_needed = False
        if action.is_running and ('keys' in kwargs or 'interval_ms' in kwargs):
            stop_timed_action(name)
            restart_needed = True
        
        # Update properties
        for key, value in kwargs.items():
            if hasattr(action, key):
                if key == 'interval_ms':
                    # Validate interval
                    value = max(DEFAULT_SETTINGS['min_interval'], min(value, DEFAULT_SETTINGS['max_interval']))
                setattr(action, key, value)
                logger.debug(f"Updated timed action {name}: {key} = {value}")
        
        save_timed_actions()
        
        # Restart if needed
        if restart_needed and action.enabled:
            start_timed_action(name)
        
        return True
    return False

def save_timed_actions():
    """Save all timed actions to a JSON file."""
    try:
        # Create actions directory if it doesn't exist
        actions_dir = DEFAULT_SETTINGS['actions_dir']
        os.makedirs(actions_dir, exist_ok=True)
        
        # Save actions configuration
        config_path = os.path.join(actions_dir, 'timed_actions_config.json')
        actions_data = {
            name: action.to_dict() 
            for name, action in _timed_actions.items()
        }
        
        with open(config_path, 'w') as f:
            json.dump(actions_data, f, indent=2)
        
        logger.debug(f"Saved {len(_timed_actions)} timed actions to {config_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving timed actions: {e}")
        return False

def load_timed_actions():
    """Load timed actions from JSON file."""
    try:
        global _timed_actions
        actions_dir = DEFAULT_SETTINGS['actions_dir']
        config_path = os.path.join(actions_dir, 'timed_actions_config.json')
        
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                actions_data = json.load(f)
            
            _timed_actions = {}
            for name, data in actions_data.items():
                try:
                    action = TimedAction.from_dict(data)
                    _timed_actions[name] = action
                except Exception as e:
                    logger.error(f"Error loading timed action {name}: {e}")
            
            logger.info(f"Loaded {len(_timed_actions)} timed actions")
            return True
        else:
            logger.debug("No timed actions configuration file found")
            return True
    except Exception as e:
        logger.error(f"Error loading timed actions: {e}")
        return False

def send_key_to_game_window(key):
    """Send a key press to the game window using the existing recorder infrastructure."""
    try:
        # Use the existing send_key_direct function from recorder module
        from recorder import send_key_direct

        # The send_key_direct function already handles:
        # - Game window targeting
        # - Background window sending
        # - Fallback methods
        # - Error handling
        send_key_direct(key)

        logger.debug(f"Sent key '{key}' to game window using recorder infrastructure")
        return True

    except Exception as e:
        logger.error(f"Error sending key '{key}' to game window: {e}")
        return False

def execute_timed_action(action_name):
    """Execute a timed action in a loop until stopped."""
    try:
        action = _timed_actions.get(action_name)
        if not action:
            logger.error(f"Timed action '{action_name}' not found")
            return

        logger.info(f"Starting timed action: {action_name}")
        action.is_running = True

        while action.is_running and action.enabled:
            try:
                # Send each key in the action
                for key in action.keys:
                    if not action.is_running:  # Check if stopped during key sequence
                        break

                    success = send_key_to_game_window(key)
                    if success:
                        action.execution_count += 1
                        action.last_execution_time = time.time()
                        logger.debug(f"Executed key '{key}' for action '{action_name}' (count: {action.execution_count})")
                    else:
                        logger.warning(f"Failed to send key '{key}' for action '{action_name}'")

                    # Small delay between keys in the same action
                    if len(action.keys) > 1:
                        time.sleep(0.1)

                # Wait for the specified interval
                if action.is_running:
                    time.sleep(action.get_interval_seconds())

            except Exception as e:
                logger.error(f"Error executing timed action '{action_name}': {e}")
                time.sleep(1)  # Wait before retrying

        action.is_running = False
        logger.info(f"Stopped timed action: {action_name}")

    except Exception as e:
        logger.error(f"Error in timed action execution thread for '{action_name}': {e}")
        if action_name in _timed_actions:
            _timed_actions[action_name].is_running = False

def start_timed_action(name):
    """Start a timed action."""
    try:
        action = _timed_actions.get(name)
        if not action:
            logger.error(f"Timed action '{name}' not found")
            return False

        if action.is_running:
            logger.warning(f"Timed action '{name}' is already running")
            return False

        if not action.enabled:
            logger.warning(f"Timed action '{name}' is disabled")
            return False

        # Check if we've reached the maximum concurrent actions
        running_count = sum(1 for a in _timed_actions.values() if a.is_running)
        if running_count >= DEFAULT_SETTINGS['max_concurrent_actions']:
            logger.warning(f"Maximum concurrent actions ({DEFAULT_SETTINGS['max_concurrent_actions']}) reached")
            return False

        # Start the action in a new thread
        thread = threading.Thread(target=execute_timed_action, args=(name,))
        thread.daemon = True
        action.thread = thread
        thread.start()

        logger.info(f"Started timed action: {name}")
        return True

    except Exception as e:
        logger.error(f"Error starting timed action '{name}': {e}")
        return False

def stop_timed_action(name):
    """Stop a timed action."""
    try:
        action = _timed_actions.get(name)
        if not action:
            logger.error(f"Timed action '{name}' not found")
            return False

        if not action.is_running:
            logger.debug(f"Timed action '{name}' is not running")
            return True

        # Signal the action to stop
        action.is_running = False

        # Wait for the thread to finish (with timeout)
        if action.thread and action.thread.is_alive():
            action.thread.join(timeout=2.0)
            if action.thread.is_alive():
                logger.warning(f"Timed action '{name}' thread did not stop gracefully")

        action.thread = None
        logger.info(f"Stopped timed action: {name}")
        return True

    except Exception as e:
        logger.error(f"Error stopping timed action '{name}': {e}")
        return False

def stop_all_timed_actions():
    """Stop all running timed actions."""
    try:
        stopped_count = 0
        for name, action in _timed_actions.items():
            if action.is_running:
                if stop_timed_action(name):
                    stopped_count += 1

        logger.info(f"Stopped {stopped_count} timed actions")
        return True

    except Exception as e:
        logger.error(f"Error stopping all timed actions: {e}")
        return False

def get_timed_actions_status():
    """Get the current status of all timed actions."""
    status = {
        'enabled': DEFAULT_SETTINGS['enabled'],
        'total_actions': len(_timed_actions),
        'running_actions': sum(1 for a in _timed_actions.values() if a.is_running),
        'actions': {}
    }

    for name, action in _timed_actions.items():
        status['actions'][name] = {
            'name': name,
            'enabled': action.enabled,
            'running': action.is_running,
            'keys': action.keys,
            'interval': action.get_interval_display(),
            'execution_count': action.execution_count,
            'last_execution': action.last_execution_time
        }

    return status

def parse_interval_string(interval_str):
    """Parse interval string (e.g., '5s', '1000ms', '2m') to milliseconds."""
    try:
        interval_str = interval_str.strip().lower()

        if interval_str.endswith('ms'):
            # Milliseconds
            return int(float(interval_str[:-2]))
        elif interval_str.endswith('s'):
            # Seconds
            return int(float(interval_str[:-1]) * 1000)
        elif interval_str.endswith('m'):
            # Minutes
            return int(float(interval_str[:-1]) * 60 * 1000)
        else:
            # Assume milliseconds if no unit
            return int(float(interval_str))

    except (ValueError, TypeError):
        logger.error(f"Invalid interval string: {interval_str}")
        return DEFAULT_SETTINGS['default_interval']

def validate_keys(keys):
    """Validate that all keys in the list are supported."""
    if isinstance(keys, str):
        keys = [keys]

    supported_keys = get_supported_keys()
    valid_keys = []
    invalid_keys = []

    for key in keys:
        if key.lower() in supported_keys:
            valid_keys.append(key)
        else:
            invalid_keys.append(key)

    return valid_keys, invalid_keys

def export_timed_actions_config(export_path):
    """Export timed actions configuration to a file."""
    try:
        export_data = {
            'settings': DEFAULT_SETTINGS.copy(),
            'actions': {
                name: action.to_dict()
                for name, action in _timed_actions.items()
            },
            'export_date': datetime.now().isoformat(),
            'version': '1.0'
        }

        with open(export_path, 'w') as f:
            json.dump(export_data, f, indent=2)

        logger.info(f"Exported timed actions configuration to {export_path}")
        return True
    except Exception as e:
        logger.error(f"Error exporting timed actions configuration: {e}")
        return False

def import_timed_actions_config(import_path):
    """Import timed actions configuration from a file."""
    try:
        with open(import_path, 'r') as f:
            data = json.load(f)

        # Import settings
        if 'settings' in data:
            for key, value in data['settings'].items():
                if key in DEFAULT_SETTINGS:
                    DEFAULT_SETTINGS[key] = value

        # Import actions
        if 'actions' in data:
            global _timed_actions
            imported_count = 0

            for name, action_data in data['actions'].items():
                try:
                    action = TimedAction.from_dict(action_data)
                    _timed_actions[name] = action
                    imported_count += 1
                except Exception as e:
                    logger.error(f"Error importing timed action {name}: {e}")

            save_timed_actions()
            logger.info(f"Imported {imported_count} timed actions from {import_path}")
            return True

        return False
    except Exception as e:
        logger.error(f"Error importing timed actions configuration: {e}")
        return False

def get_supported_keys():
    """Get list of all supported keys."""
    return [
        # Letters
        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
        'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',

        # Numbers
        '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',

        # Function keys
        'f1', 'f2', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9', 'f10', 'f11', 'f12',

        # Special keys
        'space', 'enter', 'tab', 'escape', 'esc', 'shift', 'ctrl', 'alt',
        'backspace', 'delete', 'insert', 'home', 'end', 'pageup', 'pagedown',
        'up', 'down', 'left', 'right',

        # Numpad
        'numpad0', 'numpad1', 'numpad2', 'numpad3', 'numpad4',
        'numpad5', 'numpad6', 'numpad7', 'numpad8', 'numpad9'
    ]

# Initialize timed actions on module load
load_timed_actions()
