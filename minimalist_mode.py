"""
Minimalist Mode Module - Provides a compact, focused interface for essential controls
"""

import tkinter as tk
from tkinter import ttk
import threading
from imports import logger, HAVE_KEYBOARD, keyboard
from ui_helpers import create_styled_button
from config import save_minimalist_settings, load_minimalist_settings
from recorder import playback_actions, get_recorded_actions, is_running, set_running
from death_detection import start_death_detection, stop_death_detection, is_death_detection_active

class MinimalistWindow:
    def __init__(self, main_gui_instance):
        self.main_gui = main_gui_instance
        self.window = None
        self.is_visible = False
        
        # Load settings
        self.settings = load_minimalist_settings()
        
        # Colors for minimalist theme
        self.colors = {
            'bg': '#2c3e50',
            'fg': '#ecf0f1',
            'accent': '#3498db',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'button_bg': '#34495e',
            'button_hover': '#4a6741'
        }

    def create_window(self):
        """Create the minimalist window."""
        if self.window:
            return
            
        # Create the minimalist window
        self.window = tk.Toplevel(self.main_gui.root)
        self.window.title("Macro Recorder - Minimalist")
        
        # Configure window properties
        self.window.geometry(f"280x400+{self.settings['x']}+{self.settings['y']}")
        self.window.configure(bg=self.colors['bg'])
        self.window.attributes("-topmost", True)
        self.window.attributes("-alpha", self.settings.get('transparency', 0.9))
        self.window.resizable(False, False)
        
        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.hide_window)
        
        # Bind position change to save settings
        self.window.bind('<Configure>', self._on_window_configure)
        
        # Create the interface
        self._create_minimalist_interface()
        
        # Set up periodic updates
        self._update_status()
        
        self.is_visible = True
        logger.info("Minimalist window created")

    def _create_minimalist_interface(self):
        """Create the minimalist interface elements."""
        # Main container
        main_frame = tk.Frame(self.window, bg=self.colors['bg'], padx=10, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="Macro Recorder",
            font=('Arial', 12, 'bold'),
            fg=self.colors['fg'],
            bg=self.colors['bg']
        )
        title_label.pack(pady=(0, 10))
        
        # Status display
        self.status_frame = tk.Frame(main_frame, bg=self.colors['bg'])
        self.status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = tk.Label(
            self.status_frame,
            text="Ready",
            font=('Arial', 9),
            fg=self.colors['fg'],
            bg=self.colors['bg'],
            wraplength=250
        )
        self.status_label.pack()
        
        # Actions count
        self.actions_label = tk.Label(
            self.status_frame,
            text="0 actions recorded",
            font=('Arial', 8),
            fg=self.colors['accent'],
            bg=self.colors['bg']
        )
        self.actions_label.pack()
        
        # Recording controls
        controls_frame = tk.Frame(main_frame, bg=self.colors['bg'])
        controls_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Record button
        self.record_button = self._create_mini_button(
            controls_frame,
            "🔴 Record (F8)",
            self._toggle_recording,
            self.colors['danger']
        )
        self.record_button.pack(fill=tk.X, pady=2)
        
        # Playback button
        self.playback_button = self._create_mini_button(
            controls_frame,
            "▶️ Play (F9)",
            self._toggle_playback,
            self.colors['success']
        )
        self.playback_button.pack(fill=tk.X, pady=2)
        
        # Death detection toggle
        self.death_detection_button = self._create_mini_button(
            controls_frame,
            "💀 Death Detection (F12)",
            self._toggle_death_detection,
            self.colors['accent']
        )
        self.death_detection_button.pack(fill=tk.X, pady=2)
        
        # Separator
        separator = tk.Frame(main_frame, height=2, bg=self.colors['accent'])
        separator.pack(fill=tk.X, pady=10)
        
        # Quick settings
        settings_frame = tk.Frame(main_frame, bg=self.colors['bg'])
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Transparency control
        transparency_frame = tk.Frame(settings_frame, bg=self.colors['bg'])
        transparency_frame.pack(fill=tk.X, pady=2)
        
        tk.Label(
            transparency_frame,
            text="Transparency:",
            font=('Arial', 8),
            fg=self.colors['fg'],
            bg=self.colors['bg']
        ).pack(side=tk.LEFT)
        
        self.transparency_var = tk.DoubleVar(value=self.settings.get('transparency', 0.9))
        self.transparency_scale = tk.Scale(
            transparency_frame,
            from_=0.3,
            to=1.0,
            resolution=0.1,
            orient=tk.HORIZONTAL,
            variable=self.transparency_var,
            command=self._update_transparency,
            bg=self.colors['button_bg'],
            fg=self.colors['fg'],
            highlightthickness=0,
            length=120
        )
        self.transparency_scale.pack(side=tk.RIGHT)
        
        # Bottom controls
        bottom_frame = tk.Frame(main_frame, bg=self.colors['bg'])
        bottom_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # Full mode button
        self.full_mode_button = self._create_mini_button(
            bottom_frame,
            "📋 Full Mode (F10)",
            self._toggle_full_mode,
            self.colors['button_bg']
        )
        self.full_mode_button.pack(fill=tk.X, pady=2)

    def _create_mini_button(self, parent, text, command, color):
        """Create a styled button for the minimalist interface."""
        button = tk.Button(
            parent,
            text=text,
            command=command,
            font=('Arial', 9),
            fg='white',
            bg=color,
            activebackground=self.colors['button_hover'],
            activeforeground='white',
            relief=tk.FLAT,
            borderwidth=0,
            padx=5,
            pady=3
        )
        
        # Add hover effects
        def on_enter(e):
            button.configure(bg=self.colors['button_hover'])
        
        def on_leave(e):
            button.configure(bg=color)
        
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        
        return button

    def _toggle_recording(self):
        """Toggle recording state."""
        self.main_gui._toggle_recording()
        self._update_button_states()

    def _toggle_playback(self):
        """Toggle playback state."""
        self.main_gui._toggle_playback()
        self._update_button_states()

    def _toggle_death_detection(self):
        """Toggle death detection."""
        # Use the main GUI's method to ensure consistency
        self.main_gui._toggle_death_detection()
        self._update_button_states()

    def _toggle_full_mode(self):
        """Toggle back to full mode."""
        self.main_gui._toggle_minimalist_mode()

    def _update_transparency(self, value):
        """Update window transparency."""
        if self.window:
            transparency = float(value)
            self.window.attributes("-alpha", transparency)
            self.settings['transparency'] = transparency
            save_minimalist_settings(self.settings)

    def _update_status(self):
        """Update the status display."""
        if not self.window or not self.is_visible:
            return
            
        try:
            # Update actions count
            actions = get_recorded_actions()
            self.actions_label.configure(text=f"{len(actions)} actions recorded")
            
            # Update button states
            self._update_button_states()
            
            # Schedule next update
            self.window.after(1000, self._update_status)
        except Exception as e:
            logger.error(f"Error updating minimalist status: {e}")

    def _update_button_states(self):
        """Update button states based on current application state."""
        if not self.window:
            return

        try:
            # Update record button
            if hasattr(self.main_gui, 'recording') and self.main_gui.recording:
                self.record_button.configure(text="🔴 Stop Recording", bg=self.colors['warning'])
            else:
                self.record_button.configure(text="🔴 Record (F8)", bg=self.colors['danger'])

            # Update playback button
            if is_running():
                self.playback_button.configure(text="⏸️ Stop (F9)", bg=self.colors['warning'])
            else:
                self.playback_button.configure(text="▶️ Play (F9)", bg=self.colors['success'])

            # Update death detection button
            if is_death_detection_active():
                self.death_detection_button.configure(text="💀 Death ON (F12)", bg=self.colors['success'])
                # Also update status if death detection just turned on
                if hasattr(self, 'status_label'):
                    current_text = self.status_label.cget('text')
                    if "Death detection" not in current_text or "OFF" in current_text:
                        self.status_label.configure(text="Death detection ON")
            else:
                self.death_detection_button.configure(text="💀 Death OFF (F12)", bg=self.colors['accent'])
                # Also update status if death detection just turned off
                if hasattr(self, 'status_label'):
                    current_text = self.status_label.cget('text')
                    if "Death detection" not in current_text or "ON" in current_text:
                        self.status_label.configure(text="Death detection OFF")

        except Exception as e:
            logger.error(f"Error updating button states: {e}")

    def _on_window_configure(self, event):
        """Handle window configuration changes."""
        if event.widget == self.window and self.window:
            # Save position
            self.settings['x'] = self.window.winfo_x()
            self.settings['y'] = self.window.winfo_y()
            save_minimalist_settings(self.settings)

    def show_window(self):
        """Show the minimalist window."""
        if not self.window:
            self.create_window()
        else:
            self.window.deiconify()
            self.window.lift()
            self.is_visible = True
            self._update_status()

    def hide_window(self):
        """Hide the minimalist window."""
        if self.window:
            self.window.withdraw()
            self.is_visible = False

    def destroy_window(self):
        """Destroy the minimalist window."""
        if self.window:
            self.window.destroy()
            self.window = None
            self.is_visible = False

    def is_window_visible(self):
        """Check if the minimalist window is visible."""
        return self.is_visible and self.window and self.window.winfo_exists()

    def force_refresh(self):
        """Force a refresh of the minimalist window display."""
        if self.is_window_visible():
            try:
                self._update_status()
                self._update_button_states()
                logger.debug("Minimalist window refreshed")
            except Exception as e:
                logger.error(f"Error forcing minimalist window refresh: {e}")
