"""
Death Detection Module - Handles automatic character revival detection and clicking
"""

import os
import time
import threading
from imports import logger, pyautogui, cv2, np, HAVE_PYAUTOGUI
from recorder import send_mouse_click_direct
from win_api import find_game_window, get_game_window_rect, game_window_handle
from config import game_window_settings

# Death detection settings
death_detection_settings = {
    'enabled': False,
    'color': (80, 80, 80),  # Gray color in the death dialog
    'last_death_time': 0,  # Last time death was detected
    'threshold': 200,  # Minimum number of matching pixels to trigger detection
    'region': None,  # Custom region for detection (x1, y1, x2, y2)
    'use_template_matching': True,  # Whether to use template matching for revival button
    'confidence': 0.85,  # Confidence threshold for template matching (85%)
    'template_path': os.path.join(os.path.dirname(os.path.abspath(__file__)), "templates", "revival_button.png"),
    'auto_click_revival': True,  # Whether to automatically click the revival button
    'detection_cooldown': 10,  # Seconds to wait before checking again after death
    'check_interval': 2.0,  # Seconds between checks (for compatibility with existing UI)
    'click_position': None,  # Position to click for revival (x, y)
}

# Global state variables
_death_detection_active = False
_death_detection_thread = None

def get_death_detection_settings():
    """Get the death detection settings dictionary."""
    return death_detection_settings

def is_death_detection_active():
    """Check if death detection is currently active."""
    return _death_detection_active

def set_death_detection_active(active):
    """Set the death detection active state."""
    global _death_detection_active
    _death_detection_active = active

def get_death_detection_thread():
    """Get the current death detection thread."""
    return _death_detection_thread

def set_death_detection_thread(thread):
    """Set the death detection thread."""
    global _death_detection_thread
    _death_detection_thread = thread

def validate_game_window():
    """Validate and refresh the game window handle if needed."""
    try:
        import win32gui
        from win_api import game_window_handle

        # Check if current handle is still valid
        if game_window_handle:
            try:
                # Try to get window text to validate handle
                win32gui.GetWindowText(game_window_handle)
                return True
            except:
                # Handle is invalid, need to re-find window
                logger.debug("Game window handle is invalid, re-finding window")
                return find_game_window()
        else:
            # No handle, try to find window
            return find_game_window()
    except Exception as e:
        logger.error(f"Error validating game window: {e}")
        return False

def click_revival_button():
    """Click the revival button using template matching."""
    try:
        # Take a screenshot
        if not HAVE_PYAUTOGUI:
            logger.error("PyAutoGUI not available for template matching")
            return False
            
        screenshot = pyautogui.screenshot()
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        # Load the template
        template_path = death_detection_settings['template_path']
        if not os.path.exists(template_path):
            logger.error(f"Template file not found: {template_path}")
            return False
            
        template = cv2.imread(template_path)
        if template is None:
            logger.error(f"Failed to load template: {template_path}")
            return False
        
        # Template matching
        result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, max_loc = cv2.minMaxLoc(result)
        
        logger.info(f"Template matching result: confidence = {max_val}")
        
        # Check if confidence is above threshold
        if max_val >= death_detection_settings['confidence']:
            # Calculate button center
            h, w = template.shape[:2]
            button_x = max_loc[0] + w // 2
            button_y = max_loc[1] + h // 2

            logger.info(f"Revival button detected at ({button_x}, {button_y}) with confidence {max_val:.3f}")

            # If targeting game window only, activate it first
            if game_window_settings['target_game_only']:
                if validate_game_window():
                    try:
                        import win32gui
                        win32gui.SetForegroundWindow(game_window_handle)
                        time.sleep(0.1)  # Small delay to ensure window is focused
                        logger.debug("Game window activated successfully")
                    except Exception as window_error:
                        logger.warning(f"Could not activate game window: {window_error}")
                        # Continue anyway - we can still click without activating
                else:
                    logger.warning("Could not validate game window, clicking without activation")

            # Click the button
            if death_detection_settings['auto_click_revival']:
                try:
                    send_mouse_click_direct('left', button_x, button_y)
                    logger.info(f"Revival button clicked successfully at ({button_x}, {button_y})")

                    # Update last death time to prevent immediate re-clicking
                    death_detection_settings['last_death_time'] = time.time()
                    return True
                except Exception as click_error:
                    logger.error(f"Failed to click revival button: {click_error}")
                    return False
            else:
                logger.info("Revival button found but auto-click is disabled")
                return True
        else:
            # Only log at debug level for low confidence to reduce spam
            if max_val > 0.3:  # Only log if there's some detection
                logger.debug(f"Revival button not found (confidence: {max_val:.3f} < {death_detection_settings['confidence']:.3f})")
            return False
            
    except Exception as e:
        logger.error(f"Error clicking revival button: {e}")
        return False

def capture_revival_button_template():
    """Capture a template of the revival button from the current screen."""
    try:
        if not HAVE_PYAUTOGUI:
            logger.error("PyAutoGUI not available for template capture")
            return False
            
        # Take a screenshot
        screenshot = pyautogui.screenshot()
        
        # Get the center region of the screen (where revival buttons typically appear)
        width, height = screenshot.size
        center_x, center_y = width // 2, height // 2
        
        # Define a region around the center (adjust size as needed)
        region_size = 200  # 200x200 pixel region
        left = max(0, center_x - region_size // 2)
        top = max(0, center_y - region_size // 2)
        right = min(width, center_x + region_size // 2)
        bottom = min(height, center_y + region_size // 2)
        
        # Crop the screenshot to the center region
        screenshot = screenshot.crop((left, top, right, bottom))
        
        # Create templates directory if it doesn't exist
        templates_dir = os.path.dirname(death_detection_settings['template_path'])
        os.makedirs(templates_dir, exist_ok=True)
        
        # Save the template
        template_path = death_detection_settings['template_path']
        screenshot.save(template_path)
        
        logger.info(f"Revival button template saved to: {template_path}")
        return True
    except Exception as e:
        logger.error(f"Error capturing revival button template: {e}")
        return False

def detect_death():
    """Detect if character is dead by checking for the revival button using template matching."""
    if not HAVE_PYAUTOGUI:
        logger.warning("PyAutoGUI not available, death detection disabled")
        return

    logger.info("Death detection started")
    consecutive_errors = 0
    max_consecutive_errors = 5

    while is_death_detection_active():
        try:
            current_time = time.time()

            # Check if enough time has passed since last death detection
            if (current_time - death_detection_settings['last_death_time']) >= death_detection_settings['detection_cooldown']:

                # Check for revival button using template matching
                if death_detection_settings['use_template_matching']:
                    if os.path.exists(death_detection_settings['template_path']):
                        try:
                            if click_revival_button():
                                logger.info("Revival button detected and processed")
                                consecutive_errors = 0  # Reset error counter on success
                        except Exception as click_error:
                            logger.error(f"Error in revival button detection: {click_error}")
                            consecutive_errors += 1
                    else:
                        if consecutive_errors == 0:  # Only log once to avoid spam
                            logger.warning("Template file not found. Please capture a template of the revival button.")
                        consecutive_errors += 1

                # Wait between checks
                time.sleep(death_detection_settings['check_interval'])
            else:
                # Still in cooldown period, wait longer
                time.sleep(1)

            # Reset error counter if we've been running successfully
            if consecutive_errors > 0:
                consecutive_errors = max(0, consecutive_errors - 1)

        except Exception as e:
            consecutive_errors += 1
            logger.error(f"Error in death detection loop: {e}")

            # If too many consecutive errors, increase wait time
            if consecutive_errors >= max_consecutive_errors:
                logger.warning(f"Too many consecutive errors ({consecutive_errors}), increasing wait time")
                time.sleep(5)
            else:
                time.sleep(1)

    logger.info("Death detection stopped")

def start_death_detection():
    """Start the death detection in a separate thread."""
    global _death_detection_thread
    
    if not _death_detection_active:
        set_death_detection_active(True)
        _death_detection_thread = threading.Thread(target=detect_death)
        _death_detection_thread.daemon = True
        _death_detection_thread.start()
        logger.info("Death detection thread started")
        return True
    else:
        logger.warning("Death detection is already active")
        return False

def stop_death_detection():
    """Stop the death detection."""
    if _death_detection_active:
        set_death_detection_active(False)
        logger.info("Death detection stop requested")
        return True
    else:
        logger.warning("Death detection is not active")
        return False

def update_death_detection_settings(**kwargs):
    """Update death detection settings."""
    for key, value in kwargs.items():
        if key in death_detection_settings:
            death_detection_settings[key] = value
            logger.debug(f"Updated death detection setting: {key} = {value}")
        else:
            logger.warning(f"Unknown death detection setting: {key}")

def get_death_detection_status():
    """Get the current status of death detection."""
    return {
        'active': _death_detection_active,
        'thread_alive': _death_detection_thread.is_alive() if _death_detection_thread else False,
        'settings': death_detection_settings.copy()
    }
