"""
UI Helpers Module - Tooltip display, UI element utilities, and message dialogs
"""
from imports import tk, messagebox, logger, pyautogui, HAVE_PYAUTOGUI

def rgb_to_hex(rgb):
    """Convert RGB tuple to hex color string."""
    return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"

def show_click_feedback(overlay, x, y):
    """Show visual feedback at the click location without interfering with the game."""
    try:
        # Instead of creating a new window that might steal focus,
        # we'll update our existing overlay to show the click location
        if overlay is not None:
            # Update the text in the overlay to show the click location
            for widget in overlay.winfo_children():
                if isinstance(widget, tk.Label):
                    widget.config(text=f"RECORDING ACTIVE\nLast click: {x}, {y}")
                    break
    except Exception as e:
        logger.error(f"Error showing click feedback: {e}")

def get_current_mouse_position():
    """Get the current mouse position."""
    try:
        if HAVE_PYAUTOGUI:
            return pyautogui.position()
        else:
            # Fallback method using Windows API
            import ctypes
            from ctypes import wintypes
            
            class POINT(ctypes.Structure):
                _fields_ = [("x", ctypes.c_long), ("y", ctypes.c_long)]
            
            point = POINT()
            ctypes.windll.user32.GetCursorPos(ctypes.byref(point))
            return (point.x, point.y)
    except Exception as e:
        logger.error(f"Error getting mouse position: {e}")
        return (0, 0)

def create_styled_button(parent, text, command, bg_color, fg_color="white", font=('Arial', 10, 'bold'), width=20):
    """Create a styled button with consistent appearance."""
    return tk.Button(
        parent,
        text=text,
        command=command,
        bg=bg_color,
        fg=fg_color,
        font=font,
        width=width,
        relief=tk.RAISED,
        borderwidth=2
    )

def show_info_message(title, message):
    """Show an information message dialog."""
    messagebox.showinfo(title, message)

def show_error_message(title, message):
    """Show an error message dialog."""
    messagebox.showerror(title, message)

def show_warning_message(title, message):
    """Show a warning message dialog."""
    messagebox.showwarning(title, message)

def ask_yes_no(title, message):
    """Show a yes/no confirmation dialog."""
    return messagebox.askyesno(title, message)

def format_action_display(action, index):
    """Format an action for display in the actions list."""
    # Get timing information based on action type
    if action['type'] == 'delay':
        # For delay actions, show the duration
        timing_str = f"Duration: {action.get('duration', 0):.2f}s"
    else:
        # For other actions, show timestamp or delay from previous action
        timestamp = action.get('timestamp', 0)
        timing_str = f"Time: {timestamp:.2f}s"

    if action['type'] == 'key':
        key_name = str(action['key'])

        # Add visual icons for different key types
        if key_name.lower() == 'space':
            key_display = "⎵ Space"
        elif key_name.lower() == 'enter':
            key_display = "↵ Enter"
        elif key_name.lower() == 'tab':
            key_display = "⇥ Tab"
        elif key_name.lower() == 'esc':
            key_display = "⎋ Esc"
        elif key_name.lower() == 'backspace':
            key_display = "⌫ Backspace"
        elif key_name.lower() == 'shift':
            key_display = "⇧ Shift"
        elif key_name.lower() == 'ctrl':
            key_display = "⌃ Ctrl"
        elif key_name.lower() == 'alt':
            key_display = "⌥ Alt"
        elif key_name.lower() == 'up':
            key_display = "↑ Up"
        elif key_name.lower() == 'down':
            key_display = "↓ Down"
        elif key_name.lower() == 'left':
            key_display = "← Left"
        elif key_name.lower() == 'right':
            key_display = "→ Right"
        elif key_name.lower().startswith('f') and key_name[1:].isdigit():
            # Function keys (F1-F12)
            key_display = f"⊞ {key_name.upper()}"
        else:
            # Regular keys
            key_display = f"⌨ {key_name}"

        # Create a more visually appealing format with better spacing and alignment
        action_str = f"{index}. Key: {key_display:<15} | {timing_str}"
        return action_str, 'key'

    elif action['type'] == 'click' or action['type'] == 'mouse':
        button_name = action.get('button', 'left')

        # Add visual icons for different mouse buttons
        if button_name.lower() == 'left':
            button_display = "🖱 Left Click"
        elif button_name.lower() == 'right':
            button_display = "🖱 Right Click"
        elif button_name.lower() == 'middle':
            button_display = "🖱 Middle Click"
        else:
            button_display = f"🖱 {button_name.title()} Click"

        # Format coordinates with better presentation
        coords = f"({action.get('x', 0)}, {action.get('y', 0)})"

        # Create a more visually appealing format with better spacing and alignment
        action_str = f"{index}. {button_display:<15} at {coords:<10} | {timing_str}"
        return action_str, 'click'

    elif action['type'] == 'delay':
        duration = action.get('duration', 0)
        action_str = f"{index}. ⏱ Delay: {duration:.2f}s | {timing_str}"
        return action_str, 'delay'

    return f"{index}. ❓ Unknown action | {timing_str}", 'unknown'

def create_recording_overlay(root):
    """Create a transparent overlay for recording status."""
    # Create a small window that will show recording status
    overlay = tk.Toplevel(root)

    # Make it small and position it in the corner
    overlay.geometry("300x80+10+10")

    # Make it semi-transparent and remove window decorations
    overlay.attributes("-alpha", 0.7)
    overlay.attributes("-topmost", True)
    overlay.overrideredirect(True)  # Remove window decorations

    # Configure the window
    overlay.configure(bg="red")

    # Add a label to indicate recording mode
    recording_label = tk.Label(
        overlay,
        text="RECORDING ACTIVE\nPress F8 to stop",
        font=('Arial', 12, 'bold'),
        fg="white",
        bg="red",
        padx=10,
        pady=5
    )
    recording_label.pack(fill=tk.BOTH, expand=True)
    
    return overlay

def create_color_picker_overlay(root, callback):
    """Create a color picker overlay for death detection."""
    # Create a fullscreen overlay for color picking
    color_overlay = tk.Toplevel(root)
    color_overlay.attributes("-fullscreen", True)
    color_overlay.attributes("-alpha", 0.3)
    color_overlay.attributes("-topmost", True)
    color_overlay.configure(bg="black")

    # Add instructions
    instructions = tk.Label(
        color_overlay,
        text="Click on the color you want to detect for death\nPress ESC to cancel",
        font=('Arial', 16, 'bold'),
        fg="white",
        bg="black"
    )
    instructions.pack(expand=True)

    # Bind click event
    def on_click(event):
        callback(event.x_root, event.y_root, color_overlay)

    # Bind escape key to cancel
    def on_escape(event):
        color_overlay.destroy()

    color_overlay.bind("<Button-1>", on_click)
    color_overlay.bind("<Escape>", on_escape)
    color_overlay.focus_set()
    
    return color_overlay

def update_listbox_colors(listbox, actions, colors):
    """Update the colors of items in the actions listbox."""
    for i, action in enumerate(actions):
        if action['type'] == 'key':
            listbox.itemconfig(i, foreground=colors.get('accent', '#3498db'))  # Blue for key presses
        elif action['type'] == 'click' or action['type'] == 'mouse':
            listbox.itemconfig(i, foreground=colors.get('success', '#27ae60'))  # Green for mouse clicks
        elif action['type'] == 'delay':
            listbox.itemconfig(i, foreground=colors.get('warning', '#f39c12'))  # Orange for delays
        else:
            listbox.itemconfig(i, foreground=colors.get('light', '#ecf0f1'))  # Light gray for unknown

        # Add alternating row background for better readability
        if i % 2 == 1:
            listbox.itemconfig(i, background='#1c2833')  # Slightly darker background for odd rows
