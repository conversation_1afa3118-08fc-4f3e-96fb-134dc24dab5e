"""
Test script for mouse hook functionality
"""

def test_mouse_hook():
    """Test if mouse hook is working correctly."""
    print("Testing mouse hook functionality...")
    
    try:
        import mouse
        print("✅ Mouse library imported successfully")
        
        # Test basic mouse functionality
        print("Testing basic mouse functions...")
        
        # Get current position
        try:
            import pyautogui
            x, y = pyautogui.position()
            print(f"✅ Current mouse position: ({x}, {y})")
        except Exception as e:
            print(f"❌ Error getting mouse position: {e}")
            return False
        
        # Test mouse hook setup
        print("\nTesting mouse hook setup...")
        
        click_count = 0
        
        def test_callback(event):
            nonlocal click_count
            click_count += 1
            print(f"Mouse event {click_count}: type={event.event_type}, button={event.button}, x={event.x}, y={event.y}")
            
            if event.event_type == 'down' and event.button == 'right':
                print(f"✅ Right-click detected at ({event.x}, {event.y})")
                
            # Stop after 3 clicks
            if click_count >= 3:
                print("Stopping hook after 3 events...")
                mouse.unhook_all()
                return False
        
        print("Setting up mouse hook...")
        hook = mouse.hook(test_callback)
        print("✅ Mouse hook set up successfully")
        
        print("\nPlease right-click anywhere 3 times to test...")
        print("The test will automatically stop after 3 mouse events.")
        
        # Wait for events
        try:
            mouse.wait()  # This will block until unhook_all() is called
            print("✅ Mouse hook test completed")
            return True
            
        except KeyboardInterrupt:
            print("\n❌ Test interrupted by user")
            mouse.unhook_all()
            return False
            
    except ImportError:
        print("❌ Mouse library not available")
        print("Install with: pip install mouse")
        return False
    except Exception as e:
        print(f"❌ Error testing mouse hook: {e}")
        return False

def test_mouse_detection():
    """Test mouse detection without hooks."""
    print("\nTesting mouse detection methods...")
    
    try:
        import mouse
        
        # Test is_pressed
        print("Testing mouse.is_pressed()...")
        print("Right-click and hold to test detection...")
        
        import time
        start_time = time.time()
        detected = False
        
        while time.time() - start_time < 5:  # Test for 5 seconds
            if mouse.is_pressed('right'):
                if not detected:
                    print("✅ Right-click detected with is_pressed()")
                    detected = True
            time.sleep(0.1)
        
        if not detected:
            print("❌ No right-click detected in 5 seconds")
            
        return detected
        
    except Exception as e:
        print(f"❌ Error testing mouse detection: {e}")
        return False

def main():
    """Run mouse hook tests."""
    print("=" * 60)
    print("Mouse Hook Test Suite")
    print("=" * 60)
    
    # Test 1: Basic mouse hook
    print("\n1. Testing mouse hook functionality...")
    hook_test = test_mouse_hook()
    
    # Test 2: Mouse detection
    print("\n2. Testing mouse detection...")
    detection_test = test_mouse_detection()
    
    print("\n" + "=" * 60)
    print("Test Results:")
    print(f"Mouse Hook Test: {'✅ PASSED' if hook_test else '❌ FAILED'}")
    print(f"Mouse Detection Test: {'✅ PASSED' if detection_test else '❌ FAILED'}")
    
    if hook_test and detection_test:
        print("\n🎉 All mouse tests passed!")
        print("The template capture system should work correctly.")
    else:
        print("\n❌ Some tests failed.")
        print("Template capture may not work properly.")
        print("\nTroubleshooting:")
        print("1. Install mouse library: pip install mouse")
        print("2. Run as administrator (required for global hooks)")
        print("3. Check antivirus software (may block mouse hooks)")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
