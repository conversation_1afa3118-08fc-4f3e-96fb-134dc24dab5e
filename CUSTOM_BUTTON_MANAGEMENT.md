# Custom Button Management System

## Overview

The Custom Button Management system is a powerful new feature that generalizes the death detection logic to allow users to create automated button clicking for any visual elements on screen. This system enables users to:

- **Define custom templates** by uploading images of buttons or UI elements
- **Set matching thresholds** to control detection sensitivity
- **Configure automatic clicking** when templates are detected
- **Manage multiple templates** with individual enable/disable controls
- **Monitor detection statistics** and performance

## Architecture

### Core Components

1. **`custom_button_manager.py`** - Core module containing all detection logic
2. **Custom Button Tab in main.py** - GUI interface for template management
3. **Template Detection Engine** - Uses OpenCV for image matching (same as death detection)
4. **Template Storage System** - JSON-based configuration with image file management

### Key Classes

#### `CustomTemplate`
Represents a single button template with the following properties:
- `name` - Unique identifier for the template
- `image_path` - Path to the template image file
- `confidence` - Matching threshold (0.0 to 1.0)
- `auto_click` - Whether to automatically click when detected
- `enabled` - Whether this template is active
- `click_offset_x/y` - Pixel offset from template center for clicking
- `cooldown` - Seconds to wait before detecting this template again
- `detection_count` - Total number of successful detections
- `last_detection_time` - Timestamp of last detection

## Features

### Template Management
- **Add Templates**: Upload image files to create new button templates
- **Edit Templates**: Modify confidence, cooldown, offsets, and other settings
- **Delete Templates**: Remove unwanted templates
- **Enable/Disable**: Toggle individual templates or all at once
- **Test Templates**: Verify template detection without triggering clicks

### Detection System
- **Continuous Monitoring**: Scans screen at configurable intervals
- **Confidence Matching**: Uses OpenCV template matching with adjustable thresholds
- **Cooldown Management**: Prevents spam clicking with per-template cooldowns
- **Global Cooldown**: System-wide delay between any detections
- **Game Window Targeting**: Can focus game window before clicking (if configured)

### Import/Export
- **Configuration Export**: Save all templates and settings to JSON file
- **Configuration Import**: Load templates from exported files
- **Template Sharing**: Share template configurations between users
- **Backup/Restore**: Easy backup of template configurations

### Statistics & Monitoring
- **Detection Counts**: Track how many times each template was detected
- **Last Detection Times**: Monitor when templates were last found
- **Performance Metrics**: View enabled vs total templates
- **Real-time Status**: Live updates of detection activity

## Usage Guide

### Getting Started

1. **Open the Custom Buttons Tab**
   - Launch the Rappelz Macro Recorder
   - Click on the "Custom Buttons" tab

2. **Add Your First Template**
   - Click "Add Template" button
   - Browse and select an image file of the button you want to detect
   - Enter a descriptive name for the template
   - The template will be created with default settings

3. **Configure Template Settings**
   - Double-click the template in the list to edit
   - Adjust confidence threshold (0.85 recommended for most cases)
   - Set cooldown period (2-5 seconds recommended)
   - Configure click offsets if needed
   - Enable auto-clicking if desired

4. **Start Detection**
   - Check "Enable Custom Button Detection"
   - Adjust check interval and global cooldown as needed
   - The system will begin monitoring for your templates

### Best Practices

#### Template Creation
- **High Quality Images**: Use clear, high-resolution template images
- **Consistent Lighting**: Capture templates under normal game lighting
- **Minimal Background**: Include only the button, avoid surrounding elements
- **Standard Size**: Templates work best when 50-200 pixels in size

#### Confidence Settings
- **Start High**: Begin with 0.85-0.90 confidence for reliable detection
- **Adjust Down**: Lower confidence if template isn't being detected
- **Avoid Too Low**: Below 0.70 may cause false positives

#### Cooldown Configuration
- **Button Spam Prevention**: Use 2-5 second cooldowns for most buttons
- **Fast Actions**: Use 0.5-1 second for rapid-fire actions
- **Slow Actions**: Use 10+ seconds for infrequent actions

#### Performance Optimization
- **Limit Active Templates**: Keep only necessary templates enabled
- **Reasonable Check Intervals**: 1-2 seconds is usually sufficient
- **Global Cooldown**: 0.5 seconds prevents system overload

### Advanced Features

#### Click Offsets
- Use when you need to click slightly away from the template center
- Positive X values click to the right, negative to the left
- Positive Y values click down, negative up
- Useful for dropdown menus or adjacent buttons

#### Template Testing
- Use "Test Template" to verify detection without clicking
- Helps debug confidence and positioning issues
- Safe way to validate templates before enabling auto-click

#### Bulk Operations
- "Enable All" / "Disable All" for quick template management
- Export/Import for sharing configurations
- Statistics reset for fresh monitoring data

## Technical Details

### Detection Algorithm
1. **Screen Capture**: Takes screenshot of current screen
2. **Template Matching**: Uses OpenCV `cv2.matchTemplate` with `TM_CCOEFF_NORMED`
3. **Confidence Check**: Compares match confidence against threshold
4. **Cooldown Validation**: Ensures sufficient time has passed since last detection
5. **Click Execution**: Sends mouse click to detected location (if enabled)

### File Structure
```
custom_templates/
├── templates_config.json    # Template configurations
├── template_1234567890.png  # Template image files
├── template_1234567891.png
└── ...
```

### Configuration Format
```json
{
  "template_name": {
    "name": "My Button",
    "image_path": "custom_templates/template_1234567890.png",
    "confidence": 0.85,
    "auto_click": true,
    "enabled": true,
    "click_offset_x": 0,
    "click_offset_y": 0,
    "cooldown": 2.0,
    "detection_count": 15,
    "last_detection_time": 1672531200.0,
    "created_time": 1672531200.0
  }
}
```

## Integration with Existing Features

### Death Detection Compatibility
- Custom Button Management uses the same detection engine as death detection
- Both systems can run simultaneously without conflicts
- Shared game window targeting and focus management

### Recording Integration
- Template clicks can be recorded as part of macro sequences
- Recorded macros can include both manual actions and automated template clicks
- Full compatibility with existing recording/playback system

### Settings Integration
- Respects game window targeting settings
- Uses same administrator privileges for reliable clicking
- Integrates with minimalist mode for streamlined operation

## Troubleshooting

### Common Issues

**Template Not Detected**
- Check confidence threshold (try lowering to 0.75-0.80)
- Verify template image quality and clarity
- Ensure game lighting matches template capture conditions
- Test template detection manually

**False Positives**
- Increase confidence threshold (try 0.90-0.95)
- Capture more specific template with less common elements
- Add cooldown to prevent rapid false triggers

**Performance Issues**
- Reduce number of enabled templates
- Increase check interval (2-3 seconds)
- Increase global cooldown (1-2 seconds)
- Close unnecessary applications

**Clicking Wrong Location**
- Adjust click offsets in template settings
- Verify template captures the exact button area
- Test with auto-click disabled first

### Debug Information
- Check application logs for detection events
- Use template testing feature for validation
- Monitor statistics for detection patterns
- Export configuration for backup before changes

## Future Enhancements

Potential future improvements to the Custom Button Management system:

- **Multi-Monitor Support**: Detection across multiple screens
- **Template Regions**: Define specific screen areas for detection
- **Conditional Logic**: Template detection based on game state
- **Template Sequences**: Chain multiple template detections
- **Visual Feedback**: On-screen indicators for detected templates
- **Template Editor**: Built-in image editing for template refinement
